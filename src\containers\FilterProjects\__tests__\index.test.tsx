import React from 'react';
import {renderWithProviders} from '@src/utils/utils-for-tests';
import FilterProjects from '@src/containers/FilterProjects';

describe('FilterProjects', () => {
  const handleResetFilterOption = jest.fn();
  it('should render and match the snapshot', () => {
    const tree = renderWithProviders(
      <FilterProjects
        onClick={undefined}
        filter={{
          projectName: undefined,
          projectNumber: undefined,
          sector: undefined,
          bu: undefined,
          program: undefined,
          platformCode: '',
          page: 0,
          size: 0,
        }}
        handleResetFilterOption={handleResetFilterOption}
      />,
      {},
    );
    expect(tree).toMatchSnapshot();
  });
});
