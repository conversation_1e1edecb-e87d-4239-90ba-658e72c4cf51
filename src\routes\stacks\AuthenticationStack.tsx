import { createStackNavigator } from "@react-navigation/stack";
import React from "react";
import { ROUTE_NAMES } from "@src/constants/navigation";
import LoginScreen from "@src/screens/LoginScreen";

const Stack = createStackNavigator();

export const AuthStack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name={ROUTE_NAMES.LOGIN_SCREEN}>
        {_ => <LoginScreen />}
      </Stack.Screen>
    </Stack.Navigator>
  );
};
