import React, {useState} from 'react';
import {Text, View} from 'react-native';
import SelectDropdown from 'react-native-select-dropdown';
import {Filter} from '@src/models/projectFilter';

import style from '@src/components/CustomDropDownSelect/style';
import {PERCENTAGES} from '@src/constants';

type DropdownSelectProps = {
  selectedEnumInFilterParams: Filter | null;
  enums: Filter[];
  label: string;
  setCurrentEnum: (typeFilter: string, value: string) => void;
};

const getItemLabel = (item: Filter | null): string => {
  return item?.name || 'Select ...';
};

const CustomDropdownSelect = (props: DropdownSelectProps) => {
  const {enums, label, selectedEnumInFilterParams, setCurrentEnum} = props;
  const [selectedValue, setSelectedValue] = useState<string>(
    getItemLabel(selectedEnumInFilterParams),
  );

  const handleValueChange = (itemValue: string) => {
    console.log(itemValue, 'selected');
    setSelectedValue(itemValue);
    setCurrentEnum(label, itemValue);
  };

  const buttonTextAfterSelection = (item: Filter) => {
    return item.name || 'Select ...';
  };

  const rowTextForSelection = (item: Filter): string => {
    return item.name || 'Select ...';
  };

  const onSelect = (item: Filter) => {
    handleValueChange(item.code);
  };

  const renderEmptyDropdown = () => (
    <Text style={style.nonItemSelectedTextStyle}>No options available</Text>
  );

  const renderSelectDropdown = () => {
    return (
      <SelectDropdown
        buttonStyle={{
          backgroundColor: 'transparent',
          width: PERCENTAGES['100'],
        }}
        defaultButtonText={selectedValue}
        buttonTextStyle={style.nonItemSelectedTextStyle}
        data={enums}
        onSelect={onSelect}
        buttonTextAfterSelection={buttonTextAfterSelection}
        rowTextForSelection={rowTextForSelection}
      />
    );
  };

  return (
    <>
      <Text style={style.label}>{label}</Text>
      <View style={style.pickerContainer}>
        {enums.length > 0 ? renderSelectDropdown() : renderEmptyDropdown()}
      </View>
    </>
  );
};

export default CustomDropdownSelect;
