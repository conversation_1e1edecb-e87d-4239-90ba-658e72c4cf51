import {StyleSheet} from 'react-native';
import typography from '@src/theme/fonts';
import fonts from '@src/theme/fonts';
import {COMMON_COLORS} from '@src/theme/colors';

const style = StyleSheet.create({
  valuePendingContractorChange: {
    color: COMMON_COLORS.SEMI_DARK_BLUE,
    fontSize: fonts.fontSizes.sm,
    fontFamily: typography.fontFamily.montserratSemiBold,
    fontWeight: 'bold',
  },
  MMAD: {
    fontSize: fonts.fontSizes.sm,
    fontFamily: typography.fontFamily.montserratRegular,
    color: COMMON_COLORS.GREY_40,
  },
});

export default style;
