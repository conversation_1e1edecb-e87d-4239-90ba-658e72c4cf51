import {authenticationSelector} from '@src/store/authentication/selectors';
import {RootState} from '@src/store/store';
import {AuthSliceType} from '@src/store/authentication/slice';

describe('authenticationSelector', () => {
  it('should select the authentication state from the root state', () => {
    // Arrange
    const authenticationState: AuthSliceType = {
      name: '',
      userAvatar: 'userAvatar',
      isAuthenticated: true,
    };

    // @ts-ignore
    const mockRootState: RootState = {
      authentication: authenticationState,
    };

    // Act
    const selectedState = authenticationSelector(mockRootState);

    // Assert
    expect(selectedState).toEqual(authenticationState);
  });
});
