import React from 'react';
import {Modal, Text, TouchableOpacity, View} from 'react-native';
import {BlurView} from '@react-native-community/blur';
import RNExitApp from 'react-native-exit-app';
import RNRestart from 'react-native-restart';
import {ErrorSvg} from '@src/svg/modals/ErrorSvg';
import {styles} from '@src/components/ErrorModal/style';

const restartAppCallback = () => {
  RNRestart.restart();
};

const exitAppCallback = () => {
  RNExitApp.exitApp();
};

const ErrorModal = () => {
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={true}
      onRequestClose={exitAppCallback}>
      <View style={styles.modalContainer}>
        <BlurView
          style={styles.blurContainer}
          blurType="dark"
          blurAmount={20}
          reducedTransparencyFallbackColor="white"
        />
        <View style={styles.modalContent}>
          <ErrorSvg style={styles.error_illustration} />
          <Text style={styles.title}>Error</Text>
          <Text style={styles.subTitle}>
            Please contact the administrator or reload the application
          </Text>
          <TouchableOpacity
            onPress={restartAppCallback}
            style={styles.updateButton}>
            <Text style={styles.updateButtonText}>Reload</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={exitAppCallback}
            style={styles.closeButton}>
            <Text style={styles.closeButtonText}>Quit the app</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

export default ErrorModal;
