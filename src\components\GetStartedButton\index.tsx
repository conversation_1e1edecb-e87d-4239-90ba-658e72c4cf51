import { Pressable, Text } from "react-native";
import style from "./styles";
import LinearGradient from "react-native-linear-gradient";
import React from "react";
import { ArrowGetStartedLogin } from "@src/svg/ArrowGetStartedLogin";
import { getStartedButtonLinearGradientColors } from "@src/theme/colors";
// @ts-ignore
const GetStartedButton = ({ onClick, testID }) => {
  return (
    <Pressable testID={testID} onPress={onClick}>
      <LinearGradient
        colors={getStartedButtonLinearGradientColors}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={style.container}>
        <Text style={style.text}>Get Started</Text>
        <ArrowGetStartedLogin style={style.arrow} />
      </LinearGradient>
    </Pressable>
  );
};

export default GetStartedButton;
