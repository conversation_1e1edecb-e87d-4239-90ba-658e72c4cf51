import {StyleSheet} from 'react-native';
import typography from '@src/theme/fonts';
import {COMMON_COLORS} from '@src/theme/colors';
import {correspondentHeight, correspondentWidth} from '@src/utils/imageUtils';

const style = StyleSheet.create({
  searchContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    height: correspondentHeight(50),
    marginBottom: typography.fontSizes.md,
  },
  searchInput: {
    width: '100%',
    height: '100%',
    paddingHorizontal: typography.fontSizes.md,
  },
  searchBtn: {
    width: correspondentWidth(47),
    height: correspondentHeight(372),
  },
  searchWrapper: {
    marginRight: typography.fontSizes.sm,
    borderRadius: typography.fontSizes.md,
    marginEnd: correspondentWidth(12),
    height: '100%',
    borderColor: COMMON_COLORS.GREY_30,
    borderWidth: 1,
    flex: 1,
  },
  placeholder: {
    color: COMMON_COLORS.GREY_50,
    opacity: 0.7,
    fontSize: typography.fontSizes.sm,
    alignSelf: 'center',
    marginLeft: typography.fontSizes.sm,
    fontFamily: typography.fontFamily.montserratSemiBold,
  },
  filteredItemsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
export default style;
