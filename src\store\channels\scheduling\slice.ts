import { createSlice } from "@reduxjs/toolkit";
import { SLICES_NAMES } from "@src/constants/store";
import { SchedulingChannelSummary } from "@src/models/channelsSummary/schedulingChannelSummary";

export type SchedulingSliceType = {
  features: SchedulingChannelSummary | null;
  channelStatus: string;
  loading: boolean;
  success: boolean;
  error: boolean;
};

export const initialState: SchedulingSliceType = {
  features: null,
  channelStatus: "PRIMARY",
  loading: false,
  success: false,
  error: false
};
export const slice = createSlice({
  name: SLICES_NAMES.QA_CHANNEL,
  initialState,
  reducers: {
    getSchedulingFeatures: state => {
      return { ...state, loading: true };
    },
    getSchedulingFeaturesWithSuccess: (
      state,
      action: {
        payload: {
          features: SchedulingChannelSummary;
        };
      }
    ) => {
      let finalChannelStatus: any;
      if (action.payload.features.channelStatus !== null) {
        finalChannelStatus = action.payload.features.channelStatus!!;
      } else {
        finalChannelStatus = "PRIMARY";
      }
      return {
        ...state,
        features: action.payload.features,
        channelStatus: finalChannelStatus,
        error: false,
        success: true,
        loading: false
      };
    },
    getSchedulingFeaturesWithError: state => {
      return {
        ...state,
        features: null,
        channelStatus: "PRIMARY",
        error: true,
        success: false,
        loading: false
      };
    },
    cleanUpScheduling: () => {
      return initialState;
    }
  }
});

export const {
  getSchedulingFeatures,
  getSchedulingFeaturesWithSuccess,
  getSchedulingFeaturesWithError,
  cleanUpScheduling
} = slice.actions;
export default slice.reducer;
