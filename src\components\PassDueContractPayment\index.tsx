import {Text, View} from 'react-native';
import React from 'react';
import layout from '@src/theme/layout';
import style from './style';
import {addCommasToNumber} from '@src/utils/stringUtils';

export const PassDueContractPayment = (props: {value: number | null}) => {
  const finalValue = props.value ?? 0;
  return (
    <View style={layout.row}>
      <Text style={style.valuePendingContractorChange}>
        {addCommasToNumber(finalValue as unknown as number)}
      </Text>
      <Text style={style.MMAD}> MMAD</Text>
    </View>
  );
};
