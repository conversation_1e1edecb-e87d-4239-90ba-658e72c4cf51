import ReactNativeBlobUtil from "react-native-blob-util";
import { useSelector } from "react-redux";
import { authenticationSelector } from "@src/store/authentication/selectors";
import { useEffect, useState } from "react";
import { FileTypes, getImageUrl } from "@src/api/request";

type Props = {
  clientUrl: string;
};

const useGetClientImage = (props: Props) => {
  const { clientUrl } = props;
  const { userToken } = useSelector(authenticationSelector);
  const [clientImage, setClientImage] = useState<string>("");
  const [loading, setLoading] = useState(true);

  const loadClientPicture = async () => {
    try {
      const reactNativeBlobUtil = await ReactNativeBlobUtil.fetch(
        "GET",
        getImageUrl(clientUrl, FileTypes[FileTypes.CLIENT_PICTURE]),
        {
          Authorization: `Bearer ${userToken}`
        }
      );
      try {
        /*
         * const data = '{"title":"PictureNotFoundException","status":404,"detail":"The project picture is not available"}';
         * this is the kind of data when there is an error when fetching the client picture
         */
        JSON.parse(reactNativeBlobUtil.data);
        setClientImage("");
        setLoading(false);
      } catch (e) {
        setClientImage(reactNativeBlobUtil.data);
        setLoading(false);
      }
    } catch (err) {
      setClientImage("");
      setLoading(false);
    }
  };

  useEffect(() => {
    loadClientPicture();
  }, []);

  return { clientImage, loadingClientImage: loading };
};

export default useGetClientImage;
