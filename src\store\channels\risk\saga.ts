import {call, put, select, takeEvery} from 'redux-saga/effects';
import {
  getRiskFeatures,
  getRiskFeaturesWithError,
  getRiskFeaturesWithSuccess,
} from './slice';
import {ChannelsWebServices} from '@src/api/channels';
import {projectSelector} from '@src/store/project/selectors';
import baseRequest from '@src/api/request';

export function* handleRiskFeatures() {
  try {
    const {projectId, reportingDateFilter} = yield select(projectSelector);
    const {data} = yield call(
      baseRequest.get,
      ChannelsWebServices.WS_GET_RISK_SUMMARY(projectId),
      {params: reportingDateFilter},
    );
    yield put(getRiskFeaturesWithSuccess({features: data}));
  } catch (e) {
    yield put(getRiskFeaturesWithError());
  }
}

export default function* riskSaga() {
  // same as incrementNumber.type or incrementNumber
  yield takeEvery(getRiskFeatures, handleRiskFeatures);
}
