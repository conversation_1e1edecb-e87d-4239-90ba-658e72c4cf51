import React, { memo, useCallback, useEffect, useRef, useState } from "react";
import Interaction from "src/components/Interaction";
import { FlatList, View } from "react-native";
import { Interaction as InteractionModel } from "@src/models/interaction";
import Loader from "@src/components/Loader";
import { format } from "date-fns";
import { useSelector } from "react-redux";
import { wait } from "@src/utils/timeUtils";

type ListInteractionsProps = {
  handleClickInteraction: (arg: number) => void;
  interactions: InteractionModel[] | null;
  getDiscussionDetails: () => void;
};
const ListInteractions = (props: ListInteractionsProps) => {
  const { handleClickInteraction, interactions, getDiscussionDetails } = props;
  // Use drawer status from React Navigation
  const isDrawerOpen = false;

  const renderItem = (prop: { item: InteractionModel; index: number }) => {
    const { item, index } = prop;
    if (item?.comment !== null && item?.creationDate !== undefined) {
      let time: string;
      if (item?.creationDate !== undefined) {
        time = format(new Date(item.creationDate), "dd MMM, yyyy");
      } else {
        time = "Now";
      }
      return (
        <View key={index}>
          <Interaction
            userId={item?.userId}
            handleClickInteraction={() => {
              handleClickInteraction(item?.id!!);
            }}
            time={time}
            content={item?.comment!!}
            creator={item?.displayName!!}
            isParentComment={true}
          />
        </View>
      );
    } else {
      return null;
    }
  };


  const [isRefreshing, setIsRefreshing] = useState(false);

  const onRefresh = useCallback(() => {
    setIsRefreshing(true);
    wait(1500).then(() => setIsRefreshing(false));
    getDiscussionDetails();
  }, [getDiscussionDetails]);

  const flatListRef = useRef<FlatList>(null);

  // Scrolls to the end of the FlatList when data changes
  useEffect(() => {
    if (interactions !== null) {
      if (
        interactions.length > 0 &&
        flatListRef.current &&
        !isDrawerOpen
      ) {
        flatListRef.current.scrollToEnd({ animated: true });
      }
    }
  }, [interactions, isDrawerOpen]);

  // Callback for content size change
  const onContentSizeChange = () => {
    if (flatListRef.current && !isDrawerOpen) {
      //flatListRef.current.scrollToEnd({animated: true});
    }
  };

  if (interactions === null) {
    return <Loader show={true} />;
  } else {
    return (
      <>
        <FlatList
          onContentSizeChange={onContentSizeChange}
          ref={flatListRef}
          data={interactions}
          renderItem={renderItem}
          extraData={isRefreshing}
          removeClippedSubviews={true}
          showsVerticalScrollIndicator={false}
          refreshing={isRefreshing}
          onRefresh={onRefresh}
        />
      </>
    );
  }
};

export default memo(ListInteractions);
