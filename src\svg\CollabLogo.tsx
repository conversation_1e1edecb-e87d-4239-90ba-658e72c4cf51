import {Defs, G, LinearGradient, Path, Stop, Svg} from 'react-native-svg';
import React from 'react';

export const CollabLogo = (props: {style: {}}) => {
  return (
    <Svg style={props.style} viewBox="0 0 52.1 52.1">
      <Defs>
        <LinearGradient
          id="linear-gradient"
          x1="0.158"
          y1="0.148"
          x2="0.847"
          y2="0.831"
          gradientUnits="objectBoundingBox">
          <Stop offset="0" stopColor="#007cff" />
          <Stop offset="1" stopColor="#0af" />
        </LinearGradient>
      </Defs>
      <G
        id="Collab_Logo"
        data-name="Collab Logo"
        transform="translate(7956 -9286)">
        <G
          id="Group_61191"
          data-name="Group 61191"
          transform="translate(-7956 9286)">
          <G
            id="Group_61189"
            data-name="Group 61189"
            transform="translate(0 0)">
            <Path
              id="Path_51406"
              data-name="Path 51406"
              d="M724.242,263.325a26.047,26.047,0,1,0,.493,5.042A26.048,26.048,0,0,0,724.242,263.325Z"
              transform="translate(-672.638 -242.317)"
              fill="url(#linear-gradient)"
            />
            <Path
              id="Path_51407"
              data-name="Path 51407"
              d="M750.552,316.531h9.068a16.88,16.88,0,1,1,.015-8.473h-9.11a8.357,8.357,0,1,0,.028,8.473Z"
              transform="translate(-717.086 -286.278)"
              fill="#fff"
            />
          </G>
        </G>
      </G>
    </Svg>
  );
};
