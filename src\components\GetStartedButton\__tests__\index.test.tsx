import {renderWithProviders} from '@src/utils/utils-for-tests';
import React from 'react';
import GetStartedButton from '@src/components/GetStartedButton';

describe('GetStartedButton', function () {
  const onClick = jest.fn();
  it('Should render and match the snapshot', () => {
    const tree = renderWithProviders(
      <GetStartedButton onClick={onClick} testID={'testId'} />,
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
