import { createSlice } from "@reduxjs/toolkit";
import { SLICES_NAMES } from "@src/constants/store";
import { RiskChannelSummary } from "@src/models/channelsSummary/riskChannelSummary";

export type RiskSliceType = {
  features: RiskChannelSummary | null;
  channelStatus: string;
  loading: boolean;
  error: boolean;
  success: boolean;
};

export const initialState: RiskSliceType = {
  features: null,
  channelStatus: "PRIMARY",
  loading: false,
  error: false,
  success: false
};
export const slice = createSlice({
  name: SLICES_NAMES.RISK_CHANNEL,
  initialState,
  reducers: {
    getRiskFeatures: state => {
      return { ...state, loading: true, success: false, error: false };
    },
    getRiskFeaturesWithSuccess: (
      state,
      action: { payload: { features: RiskChannelSummary } }
    ) => {
      let finalChannelStatus: any;
      if (action.payload.features.channelStatus !== null) {
        finalChannelStatus = action.payload.features.channelStatus!!;
      } else {
        finalChannelStatus = "PRIMARY";
      }
      return {
        ...state,
        features: action.payload.features,
        channelStatus: finalChannelStatus,
        loading: false,
        error: false,
        success: true
      };
    },
    getRiskFeaturesWithError: state => {
      return {
        ...state,
        features: null,
        channelStatus: "PRIMARY",
        error: true,
        loading: false,
        success: false
      };
    },
    cleanUpRisk: () => {
      return initialState;
    }
  }
});

export const {
  getRiskFeaturesWithSuccess,
  getRiskFeaturesWithError,
  getRiskFeatures,
  cleanUpRisk
} = slice.actions;
export default slice.reducer;
