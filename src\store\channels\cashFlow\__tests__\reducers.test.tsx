import reducer, {
  cleanUpCash,
  getCashFlowFeatures,
  getCashFlowFeaturesWithError,
  getCashFlowFeaturesWithSuccess,
  initialState,
} from '@src/store/channels/cashFlow/slice';
import {CashFlowChannelSummary} from '@src/models/channelsSummary/cashChannelSummary';

const features: CashFlowChannelSummary = {
  channelStatus: 'PRIMARY',
  dso: 23,
  passDueContractorPayment: 23,
  passDueJesaPayment: 23,
};
describe('Cash flow reducer', () => {
  it('should return the initial state', () => {
    expect(reducer(undefined, {type: undefined})).toEqual(initialState);
  });

  it('should handle getCashFlowFeatures action', () => {
    const action = getCashFlowFeatures();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      loading: true,
      error: false,
      success: false,
    });
  });

  it('should handle getCashFlowFeaturesWithSuccess action when the channel status is not null', () => {
    const action = getCashFlowFeaturesWithSuccess({features});
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      features: action.payload.features,
      channelStatus: action.payload.features.channelStatus,
      loading: false,
      error: false,
      success: true,
    });
  });

  it('should handle getCashFlowFeaturesWithSuccess action when the channel status is null', () => {
    const action = getCashFlowFeaturesWithSuccess({
      features: {
        ...features,
        channelStatus: null,
      },
    });
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      features: action.payload.features,
      loading: false,
      error: false,
      success: true,
    });
  });

  it('should handle getCashFlowFeaturesWithError action', () => {
    const action = getCashFlowFeaturesWithError();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      loading: false,
      error: true,
      success: false,
    });
  });

  it('should handle cleanUp action', () => {
    const action = cleanUpCash();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
    });
  });
});
