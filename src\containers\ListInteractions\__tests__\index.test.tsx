import React from 'react';
import {renderWithProviders} from '@src/utils/utils-for-tests';
import {NavigationContainer} from '@react-navigation/native';
import ListInteractions from '@src/containers/ListInteractions';

describe('List Interactions', () => {
  it('should render when interactions are not null and match the snapshot', () => {
    const handleClickInteraction = jest.fn();
    const getDiscussionDetails = jest.fn();
    const tree = renderWithProviders(
      <NavigationContainer>
        <ListInteractions
          interactions={[]}
          handleClickInteraction={handleClickInteraction}
          getDiscussionDetails={getDiscussionDetails}
        />
      </NavigationContainer>,
      {},
    );
    expect(tree).toMatchSnapshot();
  });

  it('should render when interactions are  null and match the snapshot', () => {
    const handleClickInteraction = jest.fn();
    const getDiscussionDetails = jest.fn();
    const tree = renderWithProviders(
      <NavigationContainer>
        <ListInteractions
          interactions={null}
          handleClickInteraction={handleClickInteraction}
          getDiscussionDetails={getDiscussionDetails}
        />
      </NavigationContainer>,
      {},
    );
    expect(tree).toMatchSnapshot();
  });
});
