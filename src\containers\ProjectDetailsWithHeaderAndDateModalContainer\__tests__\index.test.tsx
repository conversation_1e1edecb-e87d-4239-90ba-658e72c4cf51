import React from 'react';
import {renderWithProviders} from '@src/utils/utils-for-tests';
import {NavigationContainer} from '@react-navigation/native';
import ProjectDetailsWithHeaderAndDateModalContainer from '@src/containers/ProjectDetailsWithHeaderAndDateModalContainer';

describe('Project Details With Header And Date Modal Container', () => {
  it('should render  and match the snapshot', () => {
    const toggleDrawer = jest.fn();
    const route = jest.fn();
    const tree = renderWithProviders(
      <NavigationContainer>
        <ProjectDetailsWithHeaderAndDateModalContainer
          route={route}
          toggleDrawer={toggleDrawer}
        />
      </NavigationContainer>,
      {},
    );
    expect(tree).toMatchSnapshot();
  });
});
