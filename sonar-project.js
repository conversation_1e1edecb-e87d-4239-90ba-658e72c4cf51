const scanner = require('sonarqube-scanner');

scanner(
  {
    serverUrl: 'https://localhost:9000',
    token: 'sqp_9fedaca37e236a69eaed274e072c0957b0f5ebb2',
    options: {
      'sonar.projectName': 'CollabMobile',
      'sonar.projectDescription': 'This is collab mobile docs !',
      'sonar.sources': 'src',
      'sonar.sourceEncoding': 'UTF-8',
      'sonar.test.inclusions':
        'src/**/*.test.js,src/**/*.test.tsx,src/**/*.test.ts',
      'sonar.testExecutionReportPaths': 'coverage/test-reporter.xml',
      'sonar.javascript.lcov.reportPaths': 'coverage/lcov.info',
      'sonar.javascript.node.maxspace': '4096',
    },
  },
  () => {
    console.log('>> Sonar analysis is done!');
  },
);
