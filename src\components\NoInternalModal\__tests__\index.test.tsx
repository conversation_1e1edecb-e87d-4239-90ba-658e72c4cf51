import { renderWithProviders } from "@src/utils/utils-for-tests";
import React from "react";
import NoInternetModal from "@src/components/NoInternalModal";
import { TEST_IDS } from "@src/constants/strings";

describe("NoInternetModal", function() {
  it("Should render and match the snapshot", () => {
    const tree = renderWithProviders(
      <NoInternetModal />
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });

  it("Should render the modal when the show is true", () => {
    const { getByTestId } = renderWithProviders(
      <NoInternetModal />
    );
    const container = getByTestId(TEST_IDS.VIEW_NO_INTERNET_MODAL);
    expect(container).toBeDefined();
  });

  it("Should render the modal when the show is false", () => {
    const { queryByText } = renderWithProviders(
      <NoInternetModal />
    );
    // Check that the component returns null
    const contentElement = queryByText("No Internet Modal");
    expect(contentElement).toBeNull();
  });
});
