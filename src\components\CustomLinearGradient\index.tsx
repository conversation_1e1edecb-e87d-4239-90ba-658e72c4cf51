import LinearGradient from 'react-native-linear-gradient';
import React from 'react';

type CustomLinearGradientProps = {
  children?: React.ReactNode;
  firstColor: string;
  secondColor: string;
  x_start: number;
  y_start: number;
  x_end: number;
  y_end: number;
  style?: any;
};

export const CustomLinearGradient = (props: CustomLinearGradientProps) => {
  const {
    children,
    firstColor,
    secondColor,
    x_end,
    y_end,
    x_start,
    y_start,
    style,
  } = props;
  return (
    <LinearGradient
      colors={[firstColor, secondColor]}
      start={{x: x_start, y: y_start}}
      end={{x: x_end, y: y_end}}
      style={style}>
      {children}
    </LinearGradient>
  );
};
