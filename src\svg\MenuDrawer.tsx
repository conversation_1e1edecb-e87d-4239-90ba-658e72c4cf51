import {G, Path, Svg} from 'react-native-svg';
import React from 'react';

export const MenuDrawer: React.FC<SvgProps> = props => {
  return (
    <Svg
      style={props.style}
      width={props.style.width}
      height={props.style.height}>
      <G id="Menu" transform="translate(-2 -5)">
        <G id="menu-fries" transform="translate(2.25 5.25)">
          <Path
            id="vector_Stroke_"
            data-name="vector (Stroke)"
            d="M2.25,6.078a.828.828,0,0,1,.828-.828H22.955a.828.828,0,1,1,0,1.656H3.078A.828.828,0,0,1,2.25,6.078Z"
            transform="translate(-2.25 -5.25)"
            fill="#394861"
            stroke="#394861"
            stroke-width="0.5"
            fill-rule="evenodd"
          />
          <Path
            id="vector_Stroke__2"
            data-name="vector (Stroke)_2"
            d="M8.25,12.078a.828.828,0,0,1,.828-.828H22.33a.828.828,0,1,1,0,1.656H9.078A.828.828,0,0,1,8.25,12.078Z"
            transform="translate(-1.624 -4.624)"
            fill="#394861"
            stroke="#394861"
            stroke-width="0.5"
            fill-rule="evenodd"
          />
          <Path
            id="vector_Stroke__3"
            data-name="vector (Stroke)_3"
            d="M2.25,18.078a.828.828,0,0,1,.828-.828H22.955a.828.828,0,1,1,0,1.656H3.078A.828.828,0,0,1,2.25,18.078Z"
            transform="translate(-2.25 -3.999)"
            fill="#394861"
            stroke="#394861"
            stroke-width="0.5"
            fill-rule="evenodd"
          />
        </G>
      </G>
    </Svg>
  );
};
