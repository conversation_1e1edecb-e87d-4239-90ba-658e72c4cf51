import React, { FC, memo } from "react";
import NotificationsContainer from "@src/containers/NotificationsContainer";
import { useIsFocused } from "@react-navigation/core";
import ScalableScreen from "@src/components/ScalableScreen";

/*
  * We utilize the isFocused Hook to ensure precise navigation when a user interacts with a push notification.
  * Upon the user's click on the push notification, our goal is to navigate to the relevant destination seamlessly.
  * To achieve this, we dynamically evaluate the current navigation stack and adjust it as necessary.
  * Here is an example of a navigation stack configuration is represented by the following routes:
  * routes = [
  *   { name: ROUTE_NAMES.HOME },
  *   { name: ROUTE_NAMES.PROJECT_HEALTH_DETAILS },
  *   { name: ROUTE_NAMES.NOTIFICATIONS },
  *   { name: ROUTE_NAMES.DISCUSSION_DETAILS }
  * ];
  *
  * The logic here revolves around the last screen in the navigation stack. If this screen is already focused,
  * indicating the user's current location, we ensure it remains in the stack for consistent back navigation.
  * However, if it's not currently focused, we navigate to it, ensuring the user lands on the appropriate screen.
  * Additional conditions or actions can be integrated here to tailor the navigation behavior further.
 */

interface NotificationsScreenProps {
  toggleDrawer: () => void;
}

const NotificationScreen: FC<NotificationsScreenProps> = ({ toggleDrawer }) => {
  const isFocused = useIsFocused();

  // Only render the container when the screen is focused
  return isFocused ? (
    <NotificationsContainer toggleDrawer={toggleDrawer} />
  ) : null;
};

export default memo(NotificationScreen);
