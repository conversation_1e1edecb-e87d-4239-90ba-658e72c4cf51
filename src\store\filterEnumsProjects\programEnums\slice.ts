import {createSlice} from '@reduxjs/toolkit';
import {SLICES_NAMES} from '@src/constants/store';
import {Filter} from '@src/models/projectFilter';

export type FilterProgramEnumsProjectType = {
  programEnums: Filter[];
  loading: boolean;
  success: boolean;
  error: boolean;
};

export const initialState: FilterProgramEnumsProjectType = {
  programEnums: [],
  loading: false,
  success: false,
  error: false,
};

const slice = createSlice({
  name: SLICES_NAMES.PROGRAM_ENUMS,
  initialState,
  reducers: {
    getProgramEnums: (
      state,
      action: {
        payload: {
          types: string;
        };
      },
    ) => {
      return {
        ...state,
        loading: true,
        error: false,
        success: false,
      };
    },
    getProgramEnumsWithSuccess: (state, action) => {
      return {
        ...state,
        programEnums: action.payload,
        loading: false,
        error: false,
        success: true,
      };
    },
    getProgramEnumsWithError: state => {
      return {
        ...state,
        loading: false,
        error: true,
        success: false,
      };
    },
    cleanUp: state => {
      return state;
    },
    // Other reducer actions
  },
});

export default slice.reducer;
export const {
  getProgramEnumsWithSuccess,
  getProgramEnumsWithError,
  getProgramEnums,
  cleanUp,
} = slice.actions;
