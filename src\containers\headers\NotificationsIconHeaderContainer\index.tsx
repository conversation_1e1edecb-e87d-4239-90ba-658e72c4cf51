import {globalStyles} from '@src/theme/style';
import ClickableImage from '@src/components/ClickableImage';
import {navigate} from '@src/utils/navigationUtils';
import {ROUTE_NAMES} from '@src/constants/navigation';
import {useSelector} from 'react-redux';
import React, {useMemo} from 'react';
import {notificationsSelector} from '@src/store/notifications/selectors';
import {ReadNotifications} from '@src/svg/ReadNotifications';
import {UnReadNotifications} from '@src/svg/UnReadNotifications';

const NotificationsIconHeaderContainer = (props: {
  projectId?: number | null;
}) => {
  const {projectId} = props;

  const onPress = () => {
    if (projectId !== null) {
      navigate(ROUTE_NAMES.NOTIFICATIONS, {
        projectId,
      });
    } else {
      navigate(ROUTE_NAMES.NOTIFICATIONS);
    }
  };

  const {notificationExistence} = useSelector(notificationsSelector);
  const notificationSvgIcon = useMemo(
    () =>
      !notificationExistence ? (
        <ReadNotifications style={globalStyles.notificationLogoImage} />
      ) : (
        <UnReadNotifications style={globalStyles.notificationLogoImage} />
      ),
    [notificationExistence],
  );

  return (
    <ClickableImage
      onPress={onPress}
      url={null}
      imageStyle={globalStyles.notificationLogoImage}
      SvgComponent={notificationSvgIcon}
    />
  );
};

export default React.memo(NotificationsIconHeaderContainer);
