// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Feature Should render and match the snapshot 1`] = `
<View
  style={
    {
      "alignItems": "center",
      "backgroundColor": "#FFFFFF",
      "borderColor": "#EAEEF4",
      "borderRadius": 14,
      "borderWidth": 1,
      "flexDirection": "row",
      "justifyContent": "space-between",
      "marginBottom": 25.346,
      "marginHorizontal": 20,
      "paddingHorizontal": 18.009,
      "paddingVertical": 22.678,
    }
  }
>
  <Text
    style={
      [
        {
          "color": "#3C485F",
          "fontSize": 14,
          "fontWeight": "bold",
        },
        {
          "color": "red",
        },
      ]
    }
  >
    label
  </Text>
  <View />
</View>
`;
