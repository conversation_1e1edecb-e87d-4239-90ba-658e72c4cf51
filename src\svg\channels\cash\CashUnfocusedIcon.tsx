import React from 'react';
import {G, <PERSON>, Svg} from 'react-native-svg';
import {svgChannelIconHeight, svgChannelIconWidth} from '@src/theme/style';

const CashUnfocusedIcon = () => (
  <Svg
    width={svgChannelIconWidth(28.556)}
    height={svgChannelIconHeight(28.541)}
    viewBox="0 0 28.556 28.541">
    <G
      id="Groupe_61267"
      data-name="Groupe 61267"
      transform="translate(-768.001 -348.104)"
      opacity="0.3">
      <Path
        id="Tracé_9345"
        data-name="Tracé 9345"
        d="M908.538,433.1a3.938,3.938,0,0,0-3.933-3.934H903.48a1.278,1.278,0,0,1,0-2.555H904.6a1.277,1.277,0,0,1,1.178.781,1.328,1.328,0,0,0,2.446-1.035,3.928,3.928,0,0,0-2.858-2.327v-1.006a1.328,1.328,0,0,0-2.656,0v1.007a3.934,3.934,0,0,0,.766,7.792H904.6a1.278,1.278,0,0,1,0,2.555H903.48a1.27,1.27,0,0,1-1.191-.814,1.328,1.328,0,0,0-2.475.965,3.908,3.908,0,0,0,2.9,2.431v1.005a1.328,1.328,0,1,0,2.656,0v-1.007a3.94,3.94,0,0,0,3.167-3.859Zm0,0"
        transform="translate(-121.764 -68.122)"
        fill="#0af"
        opacity="0.08"
      />
      <G
        id="Groupe_1353"
        data-name="Groupe 1353"
        transform="translate(768.001 348.104)">
        <Path
          id="Tracé_9348"
          data-name="Tracé 9348"
          d="M790.322,357.144l.788-2.941a13.825,13.825,0,0,0-15.588,20.326.11.11,0,0,1-.017.134l-1.246,1.25a.254.254,0,0,0,.114.424L780.633,378a.381.381,0,0,0,.466-.468l-1.685-6.254a.254.254,0,0,0-.424-.113l-1.076,1.079a.111.111,0,0,1-.179-.03,10.782,10.782,0,0,1,12.588-15.075Zm0,0"
          transform="translate(-773.204 -353.293)"
          fill="#0af"
          opacity="0.04"
        />
        <Path
          id="Tracé_9349"
          data-name="Tracé 9349"
          d="M912,420.313l-.788,2.94A13.825,13.825,0,0,0,926.8,402.927a.111.111,0,0,1,.017-.134l1.247-1.25a.254.254,0,0,0-.114-.424l-6.259-1.667a.381.381,0,0,0-.466.467l1.685,6.255a.254.254,0,0,0,.424.113l1.076-1.079a.111.111,0,0,1,.179.03A10.782,10.782,0,0,1,912,420.313Zm0,0"
          transform="translate(-900.561 -395.621)"
          fill="#0af"
          opacity="0.04"
        />
        <Path
          id="Tracé_9352"
          data-name="Tracé 9352"
          d="M899.746,418.193V417.6a1.527,1.527,0,1,0-3.054,0v.592a3.806,3.806,0,0,0,1.035,7.468h.983a.752.752,0,1,1,0,1.5h-.983a.748.748,0,0,1-.7-.479,1.527,1.527,0,1,0-2.845,1.11,3.779,3.779,0,0,0,2.511,2.282v.59a1.527,1.527,0,1,0,3.054,0v-.592a3.809,3.809,0,0,0,2.28-1.8.366.366,0,1,0-.637-.36,3.077,3.077,0,0,1-2.08,1.509.366.366,0,0,0-.294.359v.88a.8.8,0,1,1-1.591,0v-.879a.366.366,0,0,0-.3-.359,3.05,3.05,0,0,1-2.265-1.9.8.8,0,1,1,1.482-.578,1.474,1.474,0,0,0,1.382.944h.983a1.483,1.483,0,0,0,0-2.966h-.983a3.074,3.074,0,0,1-.6-6.09.366.366,0,0,0,.295-.359v-.88a.8.8,0,1,1,1.591,0v.88a.366.366,0,0,0,.295.359,3.069,3.069,0,0,1,2.233,1.819.8.8,0,0,1-1.466.62,1.481,1.481,0,0,0-1.367-.906h-.983a1.483,1.483,0,0,0,0,2.966h.983a3.078,3.078,0,0,1,3.074,3.074.366.366,0,1,0,.731,0,3.81,3.81,0,0,0-3.806-3.805h-.983a.752.752,0,0,1,0-1.5h.983a.751.751,0,0,1,.693.459,1.527,1.527,0,1,0,2.813-1.19,3.8,3.8,0,0,0-2.47-2.181Zm0,0"
          transform="translate(-883.941 -409.862)"
          fill="#007cff"
        />
        <Path
          id="Tracé_9353"
          data-name="Tracé 9353"
          d="M769.84,369.346l-1.08,1.083a.672.672,0,0,0,.3,1.123l6.26,1.667a.8.8,0,0,0,.978-.98l-1.685-6.255a.672.672,0,0,0-1.124-.3l-.762.765a10.5,10.5,0,0,1-.832-4.484,10.355,10.355,0,0,1,13.112-9.608.418.418,0,0,0,.516-.295l.788-2.94a.418.418,0,0,0-.293-.511,14.283,14.283,0,0,0-11.183,1.568.418.418,0,0,0,.435.715,13.454,13.454,0,0,1,10.124-1.581l-.572,2.134a11.191,11.191,0,0,0-13.763,10.489,11.318,11.318,0,0,0,1.095,5.275.529.529,0,0,0,.852.144l.875-.877,1.594,5.918-5.922-1.577,1.045-1.048a.529.529,0,0,0,.082-.641,13.413,13.413,0,0,1,3.048-17.142.418.418,0,1,0-.531-.646,14.249,14.249,0,0,0-3.357,18Zm0,0"
          transform="translate(-768.001 -348.104)"
          fill="#007cff"
        />
        <Path
          id="Tracé_9354"
          data-name="Tracé 9354"
          d="M922.07,397.712l1.081-1.083a.672.672,0,0,0-.3-1.124l-6.26-1.667a.8.8,0,0,0-.978.98l1.685,6.255a.672.672,0,0,0,1.124.3l.762-.765a10.39,10.39,0,0,1,.417,1.16.418.418,0,1,0,.8-.235,11.214,11.214,0,0,0-.646-1.685.529.529,0,0,0-.852-.144l-.875.877-1.595-5.918,5.922,1.577-1.045,1.048a.529.529,0,0,0-.081.641,13.408,13.408,0,0,1-14.714,19.816l.572-2.134a11.2,11.2,0,0,0,13.707-12.09.418.418,0,1,0-.832.088A10.355,10.355,0,0,1,906.9,414.7a.418.418,0,0,0-.515.295l-.788,2.94a.418.418,0,0,0,.292.511,14.09,14.09,0,0,0,3.755.506,14.241,14.241,0,0,0,12.423-21.241Zm0,0"
          transform="translate(-895.353 -390.411)"
          fill="#007cff"
        />
      </G>
    </G>
  </Svg>
);

export default CashUnfocusedIcon;
