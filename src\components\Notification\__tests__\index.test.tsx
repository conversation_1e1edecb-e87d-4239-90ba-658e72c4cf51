import {renderWithProviders} from '@src/utils/utils-for-tests';
import React from 'react';
import Notification from '@src/components/Notification';

describe('Notification', function () {
  const onDeleteNotification = jest.fn();
  const onClick = jest.fn();
  it('Should render and match the snapshot', () => {
    const tree = renderWithProviders(
      <Notification
        notification={{id: 2}}
        onDeleteNotification={onDeleteNotification}
        type={'type'}
        onClick={onClick}
        time={'time'}
        isRead={false}
      />,
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
