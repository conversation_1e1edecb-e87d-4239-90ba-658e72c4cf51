import {Text, View} from 'react-native';
import style from '@src/components/CriticalSOR/style';
import React from 'react';
import layout from '@src/theme/layout';
import {addCommasToNumber} from '@src/utils/stringUtils';

export const DSO = (props: {value: number}) => {
  return (
    <View style={layout.rowSpaceBetweenCenter}>
      <Text style={style.valueEvolution}>
        {addCommasToNumber(props.value as unknown as number)}
      </Text>
    </View>
  );
};
