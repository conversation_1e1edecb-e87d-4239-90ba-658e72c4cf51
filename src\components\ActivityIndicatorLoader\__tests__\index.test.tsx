import {renderWithProviders} from '@src/utils/utils-for-tests';
import React from 'react';
import Loader from '@src/components/Loader';
import {TEST_IDS} from '@src/constants/strings';

describe('Loader', function () {
  it('Should render and match the snapshot', () => {
    const tree = renderWithProviders(<Loader show={true} />).toJSON();
    expect(tree).toMatchSnapshot();
  });

  it('Should render the modal when the show is true', () => {
    const {getByTestId} = renderWithProviders(<Loader show={true} />);
    const container = getByTestId(TEST_IDS.VIEW_LOADER);
    expect(container).toBeDefined();
  });

  it('Should render the modal when the show is false', () => {
    const {queryByText} = renderWithProviders(<Loader show={false} />);
    // Check that the component returns null
    const contentElement = queryByText('Discussion content');
    expect(contentElement).toBeNull();
  });
});
