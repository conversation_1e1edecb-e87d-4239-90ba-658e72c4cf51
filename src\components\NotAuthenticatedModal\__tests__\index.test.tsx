import {renderWithProviders} from '@src/utils/utils-for-tests';
import React from 'react';
import {TEST_IDS} from '@src/constants/strings';
import NotAuthenticatedModal from '@src/components/NotAuthenticatedModal';

describe('NotAuthenticatedModal', function () {
  it('Should render and match the snapshot', () => {
    const tree = renderWithProviders(
      <NotAuthenticatedModal show={true} />,
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });

  it('Should render the modal when the show is true', () => {
    const {getByTestId} = renderWithProviders(
      <NotAuthenticatedModal show={true} />,
    );
    const container = getByTestId(TEST_IDS.VIEW_NOT_AUTHENTICATED);
    expect(container).toBeDefined();
  });

  it('Should render the modal when the show is false', () => {
    const {queryByText} = renderWithProviders(
      <NotAuthenticatedModal show={false} />,
    );
    // Check that the component returns null
    const contentElement = queryByText('No Internet Modal');
    expect(contentElement).toBeNull();
  });
});
