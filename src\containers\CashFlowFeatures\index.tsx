import React from "react";
import { View } from "react-native";
import { useSelector } from "react-redux";
import Loader from "@src/components/Loader";
import FeatureContainer, { renderFeatureContainers } from "src/containers/FeatureContainer";
import { ObjectUtils } from "@src/utils/objectUtils";
import {
  COLLAB_FEATURES,
  COLLAB_FEATURES_TITLES,
  COLLAB_SUMMARY_FEATURE_KEY_WITH_FEATURES_CODE,
  getSectionValueByKey
} from "@src/constants/channels/features";
import { ChannelFeaturesDefaultValues } from "@src/constants/channelFeaturesDefaultValues";
import { KEYS_CHANNEL } from "@src/constants/channels/modules";
import { cashFlowSelector } from "@src/store/channels/cashFlow/selectors";

const CashFlowFeatures = () => {
  const { features, loading } = useSelector(cashFlowSelector);

  // Use the reusable function for specific features
  function renderDefaultValues() {
    const cashFlowFeatures: FeatureContainer[] = [
      {
        featureKey: COLLAB_FEATURES.CASH_FLOW.DSO,
        featureTitle: COLLAB_FEATURES_TITLES.CASH_FLOW.dso,
        featureValue: null,
        defaultFeatureValue: ChannelFeaturesDefaultValues.CASH.dso
      },
      {
        featureKey: COLLAB_FEATURES.CASH_FLOW.CASH_CONTRACTOR_PAYMENT,
        featureTitle: COLLAB_FEATURES_TITLES.CASH_FLOW.passDueJesaPayment,
        featureValue: null,
        defaultFeatureValue: ChannelFeaturesDefaultValues.CASH.passDueContractorPayment
      },
      {
        featureKey: COLLAB_FEATURES.CASH_FLOW.passDueJesaPayment,
        featureTitle: COLLAB_FEATURES_TITLES.CASH_FLOW.passDueContractorPayment,
        featureValue: null,
        defaultFeatureValue: ChannelFeaturesDefaultValues.CASH.passDueJesaPayment
      }
    ];

    return renderFeatureContainers(cashFlowFeatures);
  }


  if (loading) {
    return <Loader show={true} />;
  }


  return (
    <View>
      {features
        ? ObjectUtils.convertObjectToKeyValuesArray(features).map((item) => {
          if (item.key !== KEYS_CHANNEL.COMMON.CHANNEL_STATUS) {
            return (
              <FeatureContainer
                key={item.key}
                featureKey={
                  getSectionValueByKey(COLLAB_SUMMARY_FEATURE_KEY_WITH_FEATURES_CODE, "CASH_FLOW", item.key)
                }
                featureTitle={
                  getSectionValueByKey(COLLAB_FEATURES_TITLES, "CASH_FLOW", item.key)
                }
                featureValue={item.value}
                defaultFeatureValue={
                  getSectionValueByKey(ChannelFeaturesDefaultValues, "CASH", item.key)
                }
              />
            );
          }
          return null;
        })
        : renderDefaultValues()}
    </View>
  );
};

export default CashFlowFeatures;
