import {Defs, G, LinearGradient, Path, Rect, Stop, Svg} from 'react-native-svg';
import React from 'react';
import {svgChannelIconHeight, svgChannelIconWidth} from '@src/theme/style';

export const MiniReportIcon = (props: {style: {}}) => {
  return (
    <Svg
      style={props.style}
      width={svgChannelIconWidth(24)}
      height={svgChannelIconHeight(24)}
      viewBox="0 0 24 24">
      <Defs>
        <LinearGradient
          id="linear-gradient"
          x1="0.158"
          y1="0.148"
          x2="0.886"
          y2="0.783"
          gradientUnits="objectBoundingBox">
          <Stop offset="0" stopColor="#007cff" />
          <Stop offset="1" stopColor="#0af" />
        </LinearGradient>
      </Defs>
      <G
        id="Groupe_52586"
        data-name="Groupe 52586"
        transform="translate(-24 -135)">
        <Rect
          id="Rectangle_939"
          data-name="Rectangle 939"
          width="24"
          height="24"
          rx="6"
          transform="translate(24 135)"
          fill="url(#linear-gradient)"
        />
        <G
          id="Traffic_light"
          data-name="Traffic light"
          transform="translate(25.85 136.379)">
          <Path
            id="primary"
            d="M12.357,6h2.207a.736.736,0,0,1,.736.736V16.3a.736.736,0,0,1-.736.736H5.736A.736.736,0,0,1,5,16.3V6.736A.736.736,0,0,1,5.736,6H7.943"
            transform="translate(0 -0.793)"
            fill="none"
            stroke="#fff"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1"
          />
          <Path
            id="secondary"
            d="M12.678,4.471h0A1.471,1.471,0,0,0,11.207,3h0A1.471,1.471,0,0,0,9.736,4.471h0A.736.736,0,0,0,9,5.207V6.678h4.414V5.207A.736.736,0,0,0,12.678,4.471Z"
            transform="translate(-1.057)"
            fill="none"
            stroke="#fff"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1"
          />
          <Path
            id="secondary-2"
            data-name="secondary"
            d="M9,13.471l1.471,1.471L13.414,12"
            transform="translate(-1.057 -2.379)"
            fill="none"
            stroke="#fff"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1"
          />
        </G>
      </G>
    </Svg>
  );
};
