import {expectSaga} from 'redux-saga-test-plan';
import {
  handleDeleteNotificationSaga,
  handleMarkAsReadSaga,
  handleNotificationExistenceSagas,
  handleNotificationsListSagas,
} from '@src/store/notifications/saga';
import {call} from 'redux-saga/effects';
import baseRequest from '@src/api/request';
import {NotificationsWebServices} from '@src/api/notifications';
import {
  getNotificationsExistenceWithError,
  getNotificationsExistenceWithSuccess,
  getNotificationsListWithError,
  getNotificationsListWithSuccess,
  markAsReadWithError,
  markAsReadWithSuccess,
} from '@src/store/notifications/slice';

describe('notificationsSagas', () => {
  const userId = '1234';
  const projectId = 22;
  describe('getNotificationsList', () => {
    it('should yield an api call', () => {
      expectSaga(handleNotificationsListSagas)
        .provide([
          [
            call(
              baseRequest.get,
              NotificationsWebServices.WS_GET_NOTIFICATION_LIST,
            ),
            {params: {userId, projectId: projectId}},
          ],
        ])
        .run();
    });

    it('should handle getNotificationsListWithSuccess sagas successfully', () => {
      expectSaga(handleNotificationsListSagas)
        .provide([
          [
            call(
              baseRequest.get,
              NotificationsWebServices.WS_GET_NOTIFICATION_LIST,
            ),
            {params: {userId, projectId: projectId}},
          ],
        ])
        .put(getNotificationsListWithSuccess([]))
        .run();
    });

    it('should handle getNotificationsListWithError sagas successfully', () => {
      expectSaga(handleNotificationsListSagas)
        .provide([
          [
            call(
              baseRequest.get,
              NotificationsWebServices.WS_GET_NOTIFICATION_LIST,
            ),
            {throws: true},
          ],
        ])
        .put(getNotificationsListWithError())
        .run();
    });
  });

  describe('get Notifications Existence', () => {
    it('should yield an api call', () => {
      expectSaga(handleNotificationExistenceSagas, {payload: {projectId}})
        .provide([
          [
            call(
              baseRequest.get,
              NotificationsWebServices.WS_CHECK_NOTIFICATION_EXISTENCE,
            ),
            {params: {userId, projectId: projectId}},
          ],
        ])
        .run();
    });

    it('should handle getNotificationsExistenceWithSuccess sagas successfully', () => {
      expectSaga(handleNotificationExistenceSagas, {payload: {projectId}})
        .provide([
          [
            call(
              baseRequest.get,
              NotificationsWebServices.WS_CHECK_NOTIFICATION_EXISTENCE,
            ),
            {params: {userId, projectId: projectId}},
          ],
        ])
        .put(getNotificationsExistenceWithSuccess(true))
        .run();
    });

    it('should handle getNotificationsExistenceWithError sagas successfully', () => {
      expectSaga(handleNotificationsListSagas)
        .provide([
          [
            call(
              baseRequest.get,
              NotificationsWebServices.WS_CHECK_NOTIFICATION_EXISTENCE,
            ),
            {throws: true},
          ],
        ])
        .put(getNotificationsExistenceWithError())
        .run();
    });
  });

  describe('mark as read', () => {
    it('should yield an api call', () => {
      expectSaga(handleMarkAsReadSaga, {payload: {notificationId: 22}})
        .provide([
          [call(baseRequest.get, NotificationsWebServices.WS_MARK_AS_READ), {}],
        ])
        .run();
    });

    it('should handle markAsRead sagas successfully', () => {
      expectSaga(handleMarkAsReadSaga, {payload: {notificationId: 22}})
        .provide([
          [call(baseRequest.get, NotificationsWebServices.WS_MARK_AS_READ), {}],
        ])
        .put(markAsReadWithSuccess())
        .run();
    });

    it('should handle getNotificationsExistenceWithError sagas successfully', () => {
      expectSaga(handleMarkAsReadSaga, {payload: {notificationId: 22}})
        .provide([
          [
            call(baseRequest.get, NotificationsWebServices.WS_MARK_AS_READ),
            {throws: true},
          ],
        ])
        .put(markAsReadWithError())
        .run();
    });
  });

  describe('mark as delete', () => {
    it('should yield an api call', () => {
      expectSaga(handleDeleteNotificationSaga, {payload: {notificationId: 22}})
        .provide([
          [
            call(baseRequest.get, NotificationsWebServices.WS_MARK_AS_DELETE),
            {},
          ],
        ])
        .run();
    });

    it('should handle markAsRead sagas successfully', () => {
      expectSaga(handleDeleteNotificationSaga, {payload: {notificationId: 22}})
        .provide([
          [
            call(baseRequest.get, NotificationsWebServices.WS_MARK_AS_DELETE),
            {},
          ],
        ])
        .put(markAsReadWithSuccess())
        .run();
    });

    it('should handle getNotificationsExistenceWithError sagas successfully', () => {
      expectSaga(handleDeleteNotificationSaga, {payload: {notificationId: 22}})
        .provide([
          [
            call(baseRequest.get, NotificationsWebServices.WS_MARK_AS_DELETE),
            {throws: true},
          ],
        ])
        .put(markAsReadWithError())
        .run();
    });
  });
});
