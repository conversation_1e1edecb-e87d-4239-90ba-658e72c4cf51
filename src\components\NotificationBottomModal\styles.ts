import {StyleSheet} from 'react-native';
import {PERCENTAGES} from '@src/constants';
import {COMMON_COLORS} from '@src/theme/colors';
import layout from '@src/theme/layout';
import {correspondentHeight, correspondentWidth} from '@src/utils/imageUtils';
import typography from '@src/theme/fonts';

const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  blurContainer: {
    position: 'absolute',
    backgroundColor: COMMON_COLORS.BLUE_40,
    opacity: 0.3,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  container: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  content: {
    backgroundColor: 'white',
    padding: 20,
    paddingTop: correspondentHeight(12),
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  closeButton: {
    alignSelf: 'flex-end',
    marginBottom: correspondentHeight(10),
  },
  line: {
    width: '28%',
    height: correspondentHeight(4),
    backgroundColor: COMMON_COLORS.GREY_15,
    alignSelf: 'center',
    marginVertical: correspondentHeight(12),
    borderRadius: 11,
  },
  title: {
    fontSize: typography.fontSizes.lg,
    fontWeight: '900',
    color: COMMON_COLORS.BLUE_40,
  },
  notificationContent: {
    fontSize: typography.fontSizes.md - 1,
    fontWeight: '400',
    marginBottom: '6%',
    marginTop: '5%',
    textAlign: 'center',
    color: COMMON_COLORS.BLUE_20,
  },
  button: {
    backgroundColor: COMMON_COLORS.PRIMARY,
    padding: 12,
    paddingHorizontal: correspondentWidth(26),
    alignItems: 'center',
    borderRadius: 14,
    width: PERCENTAGES['50'],
    alignSelf: 'flex-end',
  },

  buttonText: {
    color: COMMON_COLORS.WHITE,
    fontSize: typography.fontSizes.sm - 1,
    fontWeight: 'bold',
  },

  twoButtonsContainer: {
    marginTop: correspondentHeight(20),
    ...layout.rowVCenterSpaceBetween,
  },
  filterFieldsTitle: {
    fontSize: typography.fontSizes.lg,
    color: COMMON_COLORS.BLUE_40,
    fontWeight: '700',
    marginBottom: correspondentHeight(30),
  },
  dropdown: {
    borderWidth: 1,
    borderColor: COMMON_COLORS.GREY_30,
    height: correspondentHeight(44),
    borderRadius: 14,
    paddingHorizontal: correspondentWidth(18),
    paddingVertical: correspondentHeight(4),
    marginBottom: correspondentHeight(8),
  },
  input: {
    borderWidth: 1,
    borderColor: COMMON_COLORS.GREY_30,
    height: correspondentHeight(44),
    borderRadius: 14,
    paddingHorizontal: correspondentWidth(18),
    paddingVertical: correspondentHeight(4),
    marginBottom: correspondentHeight(8),
  },
  label: {
    fontSize: typography.fontSizes.xs,
    fontWeight: 'bold',
    marginBottom: correspondentHeight(8),
    color: 'gray',
  },
  placeholderStyle: {
    fontSize: typography.fontSizes.xs,
  },
  selectedTextStyle: {
    fontSize: typography.fontSizes.xs,
  },
  itemTextStyle: {
    fontSize: typography.fontSizes.xs,
  },
  subTitle: {
    fontSize: typography.fontSizes.xs + 1,
    color: COMMON_COLORS.PRIMARY,
  },
});

export default styles;
