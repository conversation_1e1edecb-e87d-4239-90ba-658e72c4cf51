import {StyleSheet} from 'react-native';
import {windowHeight, windowWidth} from '@src/constants';

export const styles = StyleSheet.create({
  taggedPersonContainer: {
    height: windowHeight * 0.06,
    width: windowWidth,
    color: 'black',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  userAvatar: {
    resizeMode: 'contain',
    marginEnd: windowWidth * 0.03,
    height: windowHeight * 0.037,
    width: windowHeight * 0.037,
    borderRadius: 12,
  },
  userName: {color: 'black'},
});
