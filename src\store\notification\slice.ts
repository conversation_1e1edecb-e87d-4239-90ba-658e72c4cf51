import {createSlice} from '@reduxjs/toolkit';
import {SLICES_NAMES} from '@src/constants/store';
import {NotificationModel} from '@src/models/notification';

export type NotificationsSliceType = {
  notification: NotificationModel | null;
  notificationOpenedDestination: string | null;
  notificationId: string | null;
  isOpenedFromNotification: boolean;
  loading: boolean;
  error: boolean;
  success: boolean;
};

export const initialState: NotificationsSliceType = {
  isOpenedFromNotification: false,
  notificationOpenedDestination: null,
  notificationId: null,
  notification: null,
  loading: false,
  error: false,
  success: false,
};
const slice = createSlice({
  name: SLICES_NAMES.NOTIFICATION_SLICE,
  initialState,
  reducers: {
    setNotification: (state, action) => {
      return {
        ...state,
        notification: action.payload,
      };
    },

    setOpenedFromNotification: (state, action) => {
      return {
        ...state,
        isOpenedFromNotification: action.payload,
      };
    },
    cleanUp: () => {
      return {
        ...initialState,
      };
    },
  },
});

export const {setOpenedFromNotification, cleanUp, setNotification} =
  slice.actions;
export default slice.reducer;
