import { formatDistanceToNow } from "date-fns";

export const DateUtils = {
  formatDateToISOString(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const seconds = String(date.getSeconds()).padStart(2, "0");
    const milliseconds = String(date.getMilliseconds()).padStart(3, "0");

    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${milliseconds}`;
  },

  extractNotificationTimeFromCreationDate(creationDate?: string): string {
    try {
      return formatDistanceToNow(new Date(creationDate!!), {
        includeSeconds: true,
        addSuffix: true
      });
    } catch (e) {
      return "Now";
    }
  },
  returnCurrentYear(): number {
    return new Date().getFullYear();
  },
  returnCurrentMonth(): number {
    return new Date().getMonth();
  },
  extractYearsInterval(startDate: string, endDate: string): string[] {
    const start = new Date(startDate);
    const end = new Date(endDate);

    const years: string[] = [];
    let currentYear = start.getFullYear();
    while (currentYear <= end.getFullYear()) {
      years.push(currentYear.toString());
      currentYear++;
    }

    return years;
  },
  extractYearFromDate(date: string): string {
    return new Date(date).getFullYear().toString();
  },

  extractMonthsFromYear(
    selectedYear: number,
    startYear: string,
    endYear: string
  ): string[] {
    const months: string[] = [];

    const startDate = new Date(startYear);
    const endDate = new Date(endYear);

    const isSameYearStart = selectedYear == startDate.getFullYear();
    const isSameYearEnd = selectedYear == endDate.getFullYear();

    const startMonth = isSameYearStart ? startDate.getMonth() : 0;
    const endMonth = isSameYearEnd ? endDate.getMonth() : 11;
    for (let month = startMonth; month <= endMonth; month++) {
      const date = new Date(selectedYear, month);
      const monthName = date.toLocaleString("en-US", { month: "long" });
      months.push(monthName);
    }

    return months;
  },
  extractMonthAndYear(timestamp: string): { month: number; year: string } {
    const dateObj = new Date(timestamp);
    const month = dateObj.getMonth() + 1; // Adding 1 since getMonth() returns zero-based index
    const year = dateObj.getFullYear().toString();

    return { month, year };
  },
  convertMonthNumberToName(monthNumber: number | null): string | null {
    const months = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December"
    ];

    if (monthNumber != null && monthNumber >= 1 && monthNumber <= 12) {
      return months[monthNumber - 1];
    } else {
      return null;
    }
  }
};
