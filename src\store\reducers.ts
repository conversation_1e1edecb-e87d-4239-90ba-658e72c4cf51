import authenticationReducer from "@src/store/authentication/slice";
import notificationsReducer from "@src/store/notifications/slice";
import notificationReducer from "@src/store/notification/slice";
import projectReducer from "@src/store/project/slice";
import projectsReducer from "@src/store/projects/slice";
import globalReducer from "@src/store/global/slice";
import hseChannelSlice from "@src/store/channels/hse/slice";
import qaChannelSlice from "@src/store/channels/qa/slice";
import progressChannelSlice from "@src/store/channels/progress/slice";
import changeManagementSlice from "@src/store/channels/changeManagement/slice";
import humanCapChannelSlice from "@src/store/channels/humanCapital/slice";
import riskChannelSlice from "@src/store/channels/risk/slice";
import scheduleChannelSlice from "@src/store/channels/scheduling/slice";
import cashFlowChannelSlice from "@src/store/channels/cashFlow/slice";
import costChannelSlice from "@src/store/channels/cost/slice";
import buEnumsReducer from "@src/store/filterEnumsProjects/buEnums/slice";
import sectorEnumsReducer from "@src/store/filterEnumsProjects/sectorEnums/slice";
import programEnumsReducer from "@src/store/filterEnumsProjects/programEnums/slice";
import discussionReducer from "@src/store/discussion/slice";

export const reducers = {
  authentication: authenticationReducer,
  notifications: notificationsReducer,
  notification: notificationReducer,
  project: projectReducer,
  projects: projectsReducer,
  global: globalReducer,
  hseChannel: hseChannelSlice,
  qaChannel: qaChannelSlice,
  costChannel: costChannelSlice,
  progressChannel: progressChannelSlice,
  cashFlowChannel: cashFlowChannelSlice,
  changeManagementChannel: changeManagementSlice,
  humanCapChannel: humanCapChannelSlice,
  riskChannel: riskChannelSlice,
  scheduleChannel: scheduleChannelSlice,
  buEnums: buEnumsReducer,
  sectorEnums: sectorEnumsReducer,
  programEnums: programEnumsReducer,
  discussion: discussionReducer
};

/*export default function createReducer(injectedReducers = {}) {
  return combineReducers({
    authentication: authenticationReducer,
    global: globalReducer,
    project: projectReducer,
    projects: projectsReducer,
    notifications: notificationsReducer,
    carouselProjectDetails: carouselProjectDetailsSlice,
    discussion: discussionReducer,
    notification: notificationReducer,
    ...injectedReducers,
  });
}*/

/*
const persistConfig = {
  key: 'root',
  storage: AsyncStorage,
  // There is an issue in the source code of redux-persist (default setTimeout does not cleaning)
  timeout: undefined,
  whitelist: [''],
};
*/

// Setup Reducers
//export const persistedRootReducer = combineReducers(reducers);

//export default persistedRootReducer;
