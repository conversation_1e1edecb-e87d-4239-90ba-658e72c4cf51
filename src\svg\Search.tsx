import React from 'react';
import {Defs, G, LinearGradient, Path, Rect, Stop, Svg} from 'react-native-svg';

export const Search = () => {
  return (
    <Svg width="44" height="44" viewBox="0 0 44 44">
      <Defs>
        <LinearGradient
          id="linear-gradient"
          x1="0.158"
          y1="0.148"
          x2="0.847"
          y2="0.831"
          gradientUnits="objectBoundingBox">
          <Stop offset="0" stopColor="#007cff" />
          <Stop offset="1" stopColor="#0af" />
        </LinearGradient>
      </Defs>
      <G
        id="Groupe_61215"
        data-name="Groupe 61215"
        transform="translate(-301 -149)">
        <Rect
          id="Rectangle_2549"
          data-name="Rectangle 2549"
          width="44"
          height="44"
          rx="14"
          transform="translate(301 149)"
          fill="url(#linear-gradient)"
        />
        <G id="Search" transform="translate(314 161)">
          <Path
            id="Tracé_28791"
            data-name="Tracé 28791"
            d="M14,5h6"
            transform="translate(-2 -2)"
            fill="none"
            stroke="#fff"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
          />
          <Path
            id="Tracé_28792"
            data-name="Tracé 28792"
            d="M14,8h3"
            transform="translate(-2 -2)"
            fill="none"
            stroke="#fff"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
          />
          <Path
            id="Tracé_28793"
            data-name="Tracé 28793"
            d="M21,11.5A9.5,9.5,0,1,1,11.5,2"
            transform="translate(-2 -2)"
            fill="none"
            stroke="#fff"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
          />
          <Path
            id="Tracé_28794"
            data-name="Tracé 28794"
            d="M22,22l-2-2"
            transform="translate(-2 -2)"
            fill="none"
            stroke="#fff"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
          />
        </G>
      </G>
    </Svg>
  );
};
