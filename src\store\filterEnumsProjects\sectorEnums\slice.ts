// filterSlice.js
import {createSlice} from '@reduxjs/toolkit';
import {SLICES_NAMES} from '@src/constants/store';
import {Filter} from '@src/models/projectFilter';

export type FilterSectorEnumsProjectType = {
  sectorEnums: Filter[];
  loading: boolean;
  success: boolean;
  error: boolean;
};

export const initialState: FilterSectorEnumsProjectType = {
  sectorEnums: [],
  loading: false,
  success: false,
  error: false,
};

const slice = createSlice({
  name: SLICES_NAMES.SECTOR_ENUMS,
  initialState,
  reducers: {
    getSectorEnums: (
      state,
      action: {
        payload: {
          types: string;
        };
      },
    ) => {
      return {
        ...state,
        loading: true,
        error: false,
        success: false,
      };
    },
    getSectorEnumsWithSuccess: (state, action) => {
      return {
        ...state,
        sectorEnums: action.payload,
        loading: false,
        error: false,
        success: true,
      };
    },
    getSectorEnumsWithError: state => {
      return {
        ...state,
        loading: false,
        error: true,
        success: false,
      };
    },
    cleanUp: state => {
      return state;
    },
  },
});

export default slice.reducer;
export const {
  getSectorEnumsWithSuccess,
  getSectorEnumsWithError,
  getSectorEnums,
  cleanUp,
} = slice.actions;
