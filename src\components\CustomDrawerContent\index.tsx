import React, {  } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  FlatList,
  Image,
  SafeAreaView
} from "react-native";
import { COMMON_COLORS } from "@src/theme/colors";
import { drawerItems, DrawerItem } from "@src/constants/navigation";
import { Close } from "@src/svg/Close";
import { Logout } from "@src/svg/Logout";
import ClickableImage from "@src/components/ClickableImage";
import { TEST_IDS } from "@src/constants/strings";
import { IMAGES } from "@src/constants/images";
import { AuthManager } from "@src/auth/AuthManager";
import { useDispatch } from "react-redux";
import { logOut } from "@src/store/authentication/slice";
import useGetUserAvatar from "@src/hooks/useGetUserAvatar";
import { returnFinalBase64String } from "@src/utils/imageUtils";
import { correspondentHeight, correspondentWidth } from "@src/utils/imageUtils";
import { windowHeight } from "@src/constants";

// Get screen dimensions
const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

type CustomDrawerContentProps = {
  state: any;
  navigation: any;
  descriptors: any;
};

const CustomDrawerContent: React.FC<CustomDrawerContentProps> = (props) => {
  const { state, navigation } = props;
  const dispatch = useDispatch();
  const currentRoute = state.routes[state.index];

  const onCloseDrawer = () => {
    navigation.closeDrawer();
  };

  const handleLogOut = async () => {
    try {
      await AuthManager.logOut();
    } catch (_) {
      await AuthManager.signOutAsyncAndRemoveLocalData();
      dispatch(logOut());
    }
  };

  const userAvatarFromHook = useGetUserAvatar({ userIdProps: "" }); // Add proper userId when available

  const renderUserImageProfile = () => {
    if (userAvatarFromHook.length > 0) {
      return (
        <Image
          style={styles.profileImage}
          source={{ uri: returnFinalBase64String(userAvatarFromHook) }}
        />
      );
    } else {
      return <Image style={styles.profileImage} source={IMAGES.USER_AVATAR} />;
    }
  };

  const renderItem = ({ item }: { item: DrawerItem }) => {
    const onViewClick = () => {
      navigation.navigate(item.destination);
    };

    const color = COMMON_COLORS.WHITE;

    return (
      <TouchableOpacity
        onPress={onViewClick}
        style={styles.drawerItemContainer}>
        {item.iconFocused}
        <Text style={{ ...styles.textRouteName, color }}>{item.name}</Text>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      
      {/* Drawer content below */}
      <SafeAreaView style={styles.drawerContent}>
        {/* Top section with profile and close button */}
        <View style={styles.header}>
          <View style={styles.profileInformationContainer}>
            <View style={styles.imageContainer}>{renderUserImageProfile()}</View>
            <View style={styles.fullNameAndPositionContainer}>
              <Text style={styles.fullName}>User Name</Text>
            </View>
          </View>
          <ClickableImage
            testID={TEST_IDS.CLOSE_MENU_ICON}
            onPress={onCloseDrawer}
            SvgComponent={<Close />}
            imageStyle={styles.X_button}
            url={null}
          />
        </View>

        {/* Middle section with navigation items */}
        <View style={styles.drawerItemsContainer}>
          <FlatList
            data={drawerItems}
            renderItem={renderItem}
            scrollEnabled={false}
          />
        </View>

        {/* Bottom section with logout button */}
        <TouchableOpacity
          style={styles.logOutButtonContainer}
          onPress={handleLogOut}
        >
          <Logout style={styles.iconImage} />
          <Text style={styles.logOutText}>Log Out</Text>
        </TouchableOpacity>
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COMMON_COLORS.PRIMARY,
  },
  drawerContent: {
    flex: 1,
    paddingHorizontal: 20,
    paddingStart: 30,
    paddingTop: windowHeight * 0.06,
    paddingBottom: windowHeight * 0.05,
    justifyContent: 'space-between',
    zIndex: 2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  profileInformationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  imageContainer: {
    padding: 3,
    width: 70,
    height: 70,
    borderRadius: 70 / 2,
    borderColor: 'white',
    borderWidth: 2,
    overflow: 'hidden',
  },
  profileImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
    borderRadius: 70 / 2,
  },
  fullNameAndPositionContainer: {
    marginStart: 6,
  },
  fullName: {
    color: 'white',
  },
  X_button: {
    marginRight: 15,
    width: 15,
    aspectRatio: 1,
  },
  drawerItemsContainer: {
    flex: 1,
    justifyContent: 'center',
    marginVertical: 80,
  },
  drawerItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 40,
  },
  iconImage: {
    width: correspondentWidth(21),
    height: correspondentHeight(21),
    marginEnd: correspondentWidth(12),
    resizeMode: 'contain',
  },
  textRouteName: {
    fontSize: 16,
    color: COMMON_COLORS.WHITE,
  },
  logOutButtonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: correspondentHeight(10),
  },
  logOutText: {
    color: 'white',
  },
});

export default CustomDrawerContent;
