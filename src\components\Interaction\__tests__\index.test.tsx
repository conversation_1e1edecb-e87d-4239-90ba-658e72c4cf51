import {renderWithProviders} from '@src/utils/utils-for-tests';
import React from 'react';
import Interaction from '@src/components/Interaction';

describe('Interaction', function () {
  const handleClickInteraction = jest.fn();
  it('Should render and match the snapshot', () => {
    const tree = renderWithProviders(
      <Interaction
        time={''}
        content={''}
        creator={''}
        isParentComment={false}
        userId={''}
        handleClickInteraction={handleClickInteraction}
      />,
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
