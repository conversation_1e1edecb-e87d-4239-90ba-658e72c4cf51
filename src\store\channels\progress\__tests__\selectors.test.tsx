import {RootState} from '@src/store/store';
import {ProgressSliceType} from '@src/store/channels/progress/slice';
import {progressSelector} from '@src/store/channels/progress/selectors';

describe('ProgressSelector', () => {
  it('should select the progress state from the root state', () => {
    // Arrange
    const progressState: ProgressSliceType = {
      features: null,
      channelStatus: 'PRIMARY',
      loading: false,
      error: false,
      success: false,
    };

    // @ts-ignore
    const mockRootState: RootState = {
      progressChannel: progressState,
    };

    // Act
    const selectedState = progressSelector(mockRootState);

    // Assert
    expect(selectedState).toEqual(progressState);
  });
});
