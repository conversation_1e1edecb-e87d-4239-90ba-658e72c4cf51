// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ErrorModal Should render and match the snapshot 1`] = `
<Modal
  animationType="fade"
  hardwareAccelerated={false}
  onRequestClose={[Function]}
  transparent={true}
  visible={true}
>
  <View
    style={
      {
        "alignItems": "center",
        "backgroundColor": "rgba(0, 52, 147, 0.3)",
        "flex": 1,
        "justifyContent": "center",
      }
    }
  >
    <BlurView
      blurAmount={20}
      blurType="dark"
      reducedTransparencyFallbackColor="white"
      style={
        [
          {
            "backgroundColor": "transparent",
          },
          {
            "bottom": 0,
            "left": 0,
            "position": "absolute",
            "right": 0,
            "top": 0,
          },
        ]
      }
    />
    <View
      style={
        {
          "alignItems": "center",
          "alignSelf": "center",
          "backgroundColor": "white",
          "borderRadius": 20,
          "justifyContent": "center",
          "paddingHorizontal": 97.5,
          "paddingVertical": 75,
          "width": 660,
        }
      }
    >
      <RNSVGSvgView
        align="xMidYMid"
        bbHeight="144.446"
        bbWidth="142.691"
        focusable={false}
        height="144.446"
        meetOrSlice={0}
        minX={0}
        minY={0}
        style={
          [
            {
              "backgroundColor": "transparent",
              "borderWidth": 0,
            },
            {
              "alignSelf": "center",
              "marginBottom": 40.019999999999996,
            },
            {
              "flex": 0,
              "height": 144,
              "width": 142,
            },
          ]
        }
        vbHeight={144.446}
        vbWidth={142.691}
        width="142.691"
      >
        <RNSVGGroup
          fill={
            {
              "payload": 4278190080,
              "type": 0,
            }
          }
        >
          <RNSVGGroup
            fill={
              {
                "payload": 4278190080,
                "type": 0,
              }
            }
            matrix={
              [
                1,
                0,
                0,
                1,
                18996.404,
                -71.493,
              ]
            }
            name="Group_61436"
          >
            <RNSVGPath
              d="M349.226,69.082c.462.917,1.053,1.762,1.431,2.728a12.838,12.838,0,0,1,.837,3.589c.325,3.728-1.108,6.838-3.384,9.64a38.963,38.963,0,0,0-3.327,4.087,24.115,24.115,0,0,0-3.129,8.189,30.345,30.345,0,0,0-.406,7.106,33.506,33.506,0,0,0,.937,6.191,55.251,55.251,0,0,0,3.522,10c1.035,2.276,2.3,4.429,3.444,6.643a23.246,23.246,0,0,1,2.3,6.668,15.173,15.173,0,0,1-2.016,10.949,21.794,21.794,0,0,1-7.751,7.181,38.113,38.113,0,0,1-10.58,4.156,39.69,39.69,0,0,1-6.732,1.1c-1.306.088-2.61.24-3.921.209a26.9,26.9,0,0,1-10.754-2.43,43.379,43.379,0,0,1-7.94-4.862c-1.865-1.407-3.649-2.92-5.411-4.459-2.535-2.214-5.034-4.477-7.735-6.491a23.247,23.247,0,0,0-6.9-3.831,11.187,11.187,0,0,0-8.667.626,8.7,8.7,0,0,1-9.718-.914,17.479,17.479,0,0,1-4.592-5.541,26.422,26.422,0,0,1-2.869-8.638,28.143,28.143,0,0,1-.153-6.836,22.394,22.394,0,0,1,2.693-8.8,20.6,20.6,0,0,1,4.726-5.739,39.814,39.814,0,0,1,5.335-3.517,49.369,49.369,0,0,0,5.946-4.225,21.2,21.2,0,0,0,5.815-7.051,13.094,13.094,0,0,0,1.121-4.2,17.862,17.862,0,0,0-.743-6.415,70.673,70.673,0,0,1-1.816-7.169,11.393,11.393,0,0,1,.42-6.146,11.765,11.765,0,0,1,3.188-4.162,26.626,26.626,0,0,1,5.609-3.8,30.334,30.334,0,0,1,6.714-2.543,32.056,32.056,0,0,1,5.475-.817,20.684,20.684,0,0,1,7.947,1.027,17.892,17.892,0,0,1,7.986,5.345c1.093,1.258,2.137,2.564,3.329,3.725a23.718,23.718,0,0,0,4.948,3.855,10.43,10.43,0,0,0,8.458.919,28.442,28.442,0,0,0,2.911-1.029,8,8,0,0,1,6.229.07C344.053,64.24,347.365,67.1,349.226,69.082Z"
              fill={
                {
                  "payload": 4292733182,
                  "type": 0,
                }
              }
              matrix={
                [
                  0.7771459614569709,
                  -0.6293203910498374,
                  0.6293203910498374,
                  0.7771459614569709,
                  -19226.184,
                  254.362,
                ]
              }
              name="Path_53099"
              propList={
                [
                  "fill",
                ]
              }
            />
            <RNSVGGroup
              fill={
                {
                  "payload": 4278190080,
                  "type": 0,
                }
              }
              matrix={
                [
                  1,
                  0,
                  0,
                  1,
                  -6.168,
                  -9.641,
                ]
              }
              name="Group_61438"
            >
              <RNSVGPath
                d="M1052.374,167.967a2.556,2.556,0,0,0-2.216,1.722c-.2.551-.579.652-1.2.443-.676-.229-.925-.575-.785-1.136a3.673,3.673,0,0,1,2.627-2.733,3.562,3.562,0,0,0,1.516-.519,1.216,1.216,0,0,0,.113-2.07,1.975,1.975,0,0,0-2.438-.2c-.608.367-1,.341-1.4-.094a1.053,1.053,0,0,1,.007-1.477,2.448,2.448,0,0,1,1.853-.926,5,5,0,0,1,4.085,1.635,2.9,2.9,0,0,1-.563,4.558A9.938,9.938,0,0,1,1052.374,167.967Z"
                fill={
                  {
                    "payload": 4285111294,
                    "type": 0,
                  }
                }
                matrix={
                  [
                    1,
                    0,
                    0,
                    1,
                    -19931.857,
                    -56.685,
                  ]
                }
                name="Path_53100"
                propList={
                  [
                    "fill",
                  ]
                }
              />
              <RNSVGPath
                d="M1032.521,252.292a1.387,1.387,0,0,1,1.422-1.416,1.431,1.431,0,1,1-.03,2.861A1.4,1.4,0,0,1,1032.521,252.292Z"
                fill={
                  {
                    "payload": 4285111294,
                    "type": 0,
                  }
                }
                matrix={
                  [
                    1,
                    0,
                    0,
                    1,
                    -19918.002,
                    -136.45,
                  ]
                }
                name="Path_53106"
                propList={
                  [
                    "fill",
                  ]
                }
              />
            </RNSVGGroup>
            <RNSVGGroup
              fill={
                {
                  "payload": 4278190080,
                  "type": 0,
                }
              }
              matrix={
                [
                  0.9876883405951378,
                  -0.15643446504023087,
                  0.15643446504023087,
                  0.9876883405951378,
                  -255.585,
                  -2951.157,
                ]
              }
              name="Group_61435"
            >
              <RNSVGPath
                d="M552.2,244.788c-1.2-.1-2.6-.4-4.006-.623-3.127-.5-6.25-1.023-9.375-1.537q-6.019-.99-12.038-1.98c-2.182-.358-4.361-.738-6.548-1.062a4.344,4.344,0,0,1-3.662-5.062c.654-3.8,1.252-7.618,1.878-11.427q.93-5.659,1.866-11.317.819-4.993,1.629-9.987.921-5.632,1.845-11.263.882-5.382,1.764-10.763.761-4.632,1.526-9.263c.125-.758.313-1.512.366-2.275a4.437,4.437,0,0,1,5.5-3.9c3.827.732,7.689,1.279,11.535,1.913q5.99.987,11.978,1.987c2.57.425,5.143.831,7.712,1.267a4.312,4.312,0,0,1,3.454,5.008c-.69,4.065-1.337,8.137-2.007,12.2-.658,3.993-1.328,7.985-1.983,11.979-.622,3.791-1.229,7.585-1.849,11.377-.505,3.088-1.025,6.174-1.531,9.262-.584,3.569-1.157,7.14-1.74,10.709q-.753,4.605-1.519,9.209c-.122.739-.21,1.487-.379,2.215A4.273,4.273,0,0,1,552.2,244.788Zm-3.053-47.379a2.8,2.8,0,0,0,.192,1.1c.344.877,1.061,1,1.6.241a3.73,3.73,0,0,0,.555-3.463.9.9,0,0,0-.707-.72.954.954,0,0,0-.9.473A4.143,4.143,0,0,0,549.146,197.409Zm-13.783-2.355a2.541,2.541,0,0,0,.284,1.414.758.758,0,0,0,1.3.2,3.77,3.77,0,0,0,.675-3.774c-.361-.691-.892-.78-1.431-.214A3.409,3.409,0,0,0,535.363,195.054Z"
                fill={
                  {
                    "payload": 4285111294,
                    "type": 0,
                  }
                }
                matrix={
                  [
                    1,
                    0,
                    0,
                    1,
                    -19460.199,
                    -59.581,
                  ]
                }
                name="Path_53101"
                propList={
                  [
                    "fill",
                  ]
                }
              />
              <RNSVGPath
                d="M805.831,435.9a4.143,4.143,0,0,1,.745-2.369.954.954,0,0,1,.9-.473.9.9,0,0,1,.707.72,3.731,3.731,0,0,1-.556,3.463c-.542.761-1.259.636-1.6-.241A2.8,2.8,0,0,1,805.831,435.9Z"
                fill={
                  {
                    "payload": 4294901502,
                    "type": 0,
                  }
                }
                matrix={
                  [
                    1,
                    0,
                    0,
                    1,
                    -19716.885,
                    -298.068,
                  ]
                }
                name="Path_53107"
                propList={
                  [
                    "fill",
                  ]
                }
              />
              <RNSVGPath
                d="M683.639,415.809a3.409,3.409,0,0,1,.829-2.379c.538-.566,1.069-.477,1.431.214a3.77,3.77,0,0,1-.675,3.774.758.758,0,0,1-1.3-.2A2.541,2.541,0,0,1,683.639,415.809Z"
                fill={
                  {
                    "payload": 4294901502,
                    "type": 0,
                  }
                }
                matrix={
                  [
                    1,
                    0,
                    0,
                    1,
                    -19608.477,
                    -280.336,
                  ]
                }
                name="Path_53108"
                propList={
                  [
                    "fill",
                  ]
                }
              />
              <RNSVGPath
                d="M708.4,527.323a6.181,6.181,0,0,1,4.629,2.315c.156.188.293.381.078.588-.243.234-.446.071-.619-.126a5.675,5.675,0,0,0-2.458-1.732,5.241,5.241,0,0,0-4.352.388c-.148.081-.3.154-.449.236-.2.111-.411.155-.542-.072a.382.382,0,0,1,.173-.57A6.981,6.981,0,0,1,708.4,527.323Z"
                fill={
                  {
                    "payload": 4294901502,
                    "type": 0,
                  }
                }
                matrix={
                  [
                    1,
                    0,
                    0,
                    1,
                    -19627.637,
                    -380.032,
                  ]
                }
                name="Path_53109"
                propList={
                  [
                    "fill",
                  ]
                }
              />
            </RNSVGGroup>
            <RNSVGPath
              d="M865.59,424.5c-.522.171-.9.333-1.3.421a4.793,4.793,0,0,0-4.015,3.731c-.08.334-.216.654-.327.981-.06.178-.058.448-.319.421-.226-.023-.25-.276-.293-.462a6.4,6.4,0,0,0-1.631-3.4,5.863,5.863,0,0,0-2.48-1.24c-.465-.145-.933-.283-1.571-.477,1.1-.393,2.05-.675,2.951-1.069a4.37,4.37,0,0,0,2.385-2.858c.158-.491.317-.981.473-1.472.035-.111.008-.274.173-.283.184-.01.23.149.274.291.138.446.29.888.41,1.339.57,2.14,2.143,3.127,4.144,3.627A3.083,3.083,0,0,1,865.59,424.5Z"
              fill={
                {
                  "payload": 4281571583,
                  "type": 0,
                }
              }
              matrix={
                [
                  1,
                  0,
                  0,
                  1,
                  -19736.279,
                  -254.943,
                ]
              }
              name="Path_53156"
              propList={
                [
                  "fill",
                ]
              }
            />
            <RNSVGPath
              d="M495.842,768.973a3.771,3.771,0,0,0-3.376-3.343,3.7,3.7,0,0,0,3.324-3.324,3.825,3.825,0,0,0,3.4,3.288A3.786,3.786,0,0,0,495.842,768.973Z"
              fill={
                {
                  "payload": 4281571583,
                  "type": 0,
                }
              }
              matrix={
                [
                  1,
                  0,
                  0,
                  1,
                  -19455.969,
                  -655.425,
                ]
              }
              name="Path_53157"
              propList={
                [
                  "fill",
                ]
              }
            />
            <RNSVGPath
              d="M290.162,407.051c.456.25.346.633.49.9a2.119,2.119,0,0,0,1.441,1.159c.148.033.3.084.33.25.024.115-.08.172-.18.19a2.3,2.3,0,0,0-1.853,2c-.325-.009-.294-.243-.333-.38a1.838,1.838,0,0,0-1.49-1.532c-.24-.041-.553-.325-.13-.46A2.318,2.318,0,0,0,290.162,407.051Z"
              fill={
                {
                  "payload": 4294901502,
                  "type": 0,
                }
              }
              matrix={
                [
                  1,
                  0,
                  0,
                  1,
                  -19242.107,
                  -237.569,
                ]
              }
              name="Path_53158"
              propList={
                [
                  "fill",
                ]
              }
            />
          </RNSVGGroup>
        </RNSVGGroup>
      </RNSVGSvgView>
      <Text
        style={
          {
            "alignSelf": "center",
            "color": "#003493",
            "fontFamily": "Montserrat-SemiBold",
            "fontSize": 18,
            "fontWeight": "700",
            "marginBottom": 26.68,
          }
        }
      >
        Error
      </Text>
      <Text
        style={
          {
            "alignSelf": "center",
            "color": "#5F6A7E",
            "fontFamily": "Montserrat-Regular",
            "fontSize": 12,
            "marginBottom": 46.690000000000005,
            "textAlign": "center",
          }
        }
      >
        Please contact the administrator or reload the application
      </Text>
      <View
        accessibilityState={
          {
            "busy": undefined,
            "checked": undefined,
            "disabled": undefined,
            "expanded": undefined,
            "selected": undefined,
          }
        }
        accessibilityValue={
          {
            "max": undefined,
            "min": undefined,
            "now": undefined,
            "text": undefined,
          }
        }
        accessible={true}
        collapsable={false}
        focusable={true}
        onClick={[Function]}
        onResponderGrant={[Function]}
        onResponderMove={[Function]}
        onResponderRelease={[Function]}
        onResponderTerminate={[Function]}
        onResponderTerminationRequest={[Function]}
        onStartShouldSetResponder={[Function]}
        style={
          {
            "backgroundColor": "#007CFF",
            "borderRadius": 14,
            "marginBottom": 13.34,
            "opacity": 1,
            "padding": 11,
            "width": "100%",
          }
        }
      >
        <Text
          style={
            {
              "alignSelf": "center",
              "color": "white",
              "fontFamily": "Montserrat-Bold",
              "fontSize": 13,
              "textAlign": "center",
            }
          }
        >
          Reload
        </Text>
      </View>
      <View
        accessibilityState={
          {
            "busy": undefined,
            "checked": undefined,
            "disabled": undefined,
            "expanded": undefined,
            "selected": undefined,
          }
        }
        accessibilityValue={
          {
            "max": undefined,
            "min": undefined,
            "now": undefined,
            "text": undefined,
          }
        }
        accessible={true}
        collapsable={false}
        focusable={true}
        onClick={[Function]}
        onResponderGrant={[Function]}
        onResponderMove={[Function]}
        onResponderRelease={[Function]}
        onResponderTerminate={[Function]}
        onResponderTerminationRequest={[Function]}
        onStartShouldSetResponder={[Function]}
        style={
          {
            "backgroundColor": "#EAEEF4",
            "borderRadius": 14,
            "marginBottom": 13.34,
            "opacity": 1,
            "padding": 11,
            "width": "100%",
          }
        }
      >
        <Text
          style={
            {
              "alignSelf": "center",
              "color": "#394861",
              "fontFamily": "Montserrat-Bold",
              "fontSize": 13,
              "textAlign": "center",
            }
          }
        >
          Quit the app
        </Text>
      </View>
    </View>
  </View>
</Modal>
`;
