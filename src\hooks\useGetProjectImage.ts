import ReactNativeBlobUtil from "react-native-blob-util";
import { useSelector } from "react-redux";
import { authenticationSelector } from "@src/store/authentication/selectors";
import { useEffect, useState } from "react";
import { FileTypes, getImageUrl } from "@src/api/request";

type Props = {
  projectPictureLink: string;
};

const useGetProjectImage = (props: Props) => {
  const { projectPictureLink } = props;
  const { userToken } = useSelector(authenticationSelector);
  const [projectImage, setProjectImage] = useState<string>("");
  const [loading, setLoading] = useState(true);

  const loadProjectPicture = async () => {
    try {
      const reactNativeBlobUtil = await ReactNativeBlobUtil.fetch(
        "GET",
        getImageUrl(projectPictureLink, FileTypes[FileTypes.PROJECT_PICTURE]),
        {
          Authorization: `Bearer ${userToken}`
        }
      );
      try {
        JSON.parse(reactNativeBlobUtil.data);
        setLoading(false);
      } catch (e) {
        setProjectImage(reactNativeBlobUtil.data);
        setLoading(false);
      }
    } catch (err) {
      setProjectImage("");
      setLoading(false);
    }
  };

  useEffect(() => {
    loadProjectPicture();
  }, []);

  return { projectImage, loadingProjectImage: loading };
};

export default useGetProjectImage;
