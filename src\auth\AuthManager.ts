import { AuthConfiguration, authorize, logout, refresh } from "react-native-app-auth";
import { compareAsc, parseISO, sub } from "date-fns";

import { AuthConfig } from "./AuthConfig";
import jwtDecode from "jwt-decode";
import { getItem, removeItem, setItem } from "@src/utils/storageUtils";
import {
  EXPIRE_TIME_KEY,
  ID_TOKEN_KEY,
  REFRESH_TOKEN_KEY,
  USER_ID_KEY,
  USER_TOKEN_KEY,
  USERNAME_KEY
} from "@src/constants/auth";
import Config from "react-native-config";

const issuer = `https://login.microsoftonline.com/${Config.AZURE_AD_TENANT_ID}/wsfed`;
const authorizationEndpoint = `https://login.microsoftonline.com/${Config.AZURE_AD_TENANT_ID}/oauth2/v2.0/authorize`;
const tokenEndpoint = `https://login.microsoftonline.com/${Config.AZURE_AD_TENANT_ID}/oauth2/v2.0/token`;
const endSessionEndPoint = `https://login.microsoftonline.com/${Config.AZURE_AD_TENANT_ID}/oauth2/v2.0/logout`;

const config: AuthConfiguration = {
  clientId: Config.AZURE_APP_ID ?? "",
  redirectUrl: AuthConfig.redirectUri,
  issuer: issuer,
  scopes: AuthConfig.appScopes,
  additionalParameters: { prompt: "select_account" },
  serviceConfiguration: {
    authorizationEndpoint: authorizationEndpoint,
    tokenEndpoint: tokenEndpoint,
    endSessionEndpoint: endSessionEndPoint
  }
};

interface DecodedJwtPayload {
  name: string;
  oid: string;
}

export class AuthManager {
  static signInAsync = async () => {
    try {
      const result = await authorize(config);
      const idToken = result.idToken;
      const decoded: DecodedJwtPayload = jwtDecode(idToken);
      await setItem(ID_TOKEN_KEY, idToken);
      await setItem(USERNAME_KEY, decoded.name);
      await setItem(USER_ID_KEY, decoded.oid);
      await setItem(USER_TOKEN_KEY, result.accessToken);
      await setItem(REFRESH_TOKEN_KEY, result.refreshToken);
      await setItem(EXPIRE_TIME_KEY, result.accessTokenExpirationDate);
      return result.accessToken;
    } catch (_) {
    }
  };

  static logOut = async () => {
    const idToken = await getItem(ID_TOKEN_KEY);
    await logout(config, {
      idToken: idToken!!,
      postLogoutRedirectUrl: config.redirectUrl
    });
  };

  static signOutAsyncAndRemoveLocalData = async () => {
    // Clear storage
    await removeItem(USER_TOKEN_KEY);
    await removeItem(REFRESH_TOKEN_KEY);
    await removeItem(EXPIRE_TIME_KEY);
  };

  static getAccessTokenAsync = async () => {
    let expireTime = await getItem(EXPIRE_TIME_KEY);
    if (expireTime !== null) {
      // Get expiration time - 5 minutes
      // If it's <= 5 minutes before expiration, then refresh
      /*  if (expireTime === undefined) {
          expireTime = mockDateBeforeExpire;
        }*/
      const expire = sub(parseISO(expireTime!!), { minutes: 5 });
      const now = new Date();
      // here we will compare now is later or the same as expire time
      if (compareAsc(now, expire) >= 0) {
        // Expired, refresh
        let refreshToken = await getItem(REFRESH_TOKEN_KEY);
        try {
          const result = await refresh(config, {
            refreshToken: refreshToken ?? ""
          });

          await setItem(USER_TOKEN_KEY, result.accessToken);
          await setItem(REFRESH_TOKEN_KEY, result.refreshToken ?? "");
          await setItem(EXPIRE_TIME_KEY, result.accessTokenExpirationDate);
          return result.accessToken;
        } catch (e) {
          return null;
        }
      }
      return await getItem(USER_TOKEN_KEY);
    }
    return null;
  };
}
