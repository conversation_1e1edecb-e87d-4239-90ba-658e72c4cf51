import {G, Path, Svg} from 'react-native-svg';
import React from 'react';
import {svgChannelIconHeight, svgChannelIconWidth} from '@src/theme/style';

export const Close = () => {
  return (
    <Svg
      width={svgChannelIconWidth(15)}
      height={svgChannelIconHeight(15.238)}
      viewBox="0 0 15 15.238">
      <G id="Close" transform="translate(-300.392 -232.763)">
        <Path
          id="Path_29182"
          data-name="Path 29182"
          d="M16.34,22.708a.922.922,0,0,1-.655-.272L9.01,15.742a.925.925,0,0,1,0-1.306l6.675-6.694a.925.925,0,1,1,1.31,1.306l-6.024,6.041,6.024,6.041a.925.925,0,0,1-.655,1.578Z"
          transform="translate(298.127 225.293)"
          fill="#fff"
        />
        <Path
          id="Path_29182-2"
          data-name="Path 29182"
          d="M9.665,22.708a.922.922,0,0,0,.655-.272l6.674-6.694a.925.925,0,0,0,0-1.306L10.32,7.742A.925.925,0,0,0,9.01,9.048l6.023,6.041L9.01,21.13a.925.925,0,0,0,.655,1.578Z"
          transform="translate(291.652 225.293)"
          fill="#fff"
        />
      </G>
    </Svg>
  );
};
