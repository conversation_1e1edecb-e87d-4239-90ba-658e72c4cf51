import LoginScreen from "@src/screens/LoginScreen";
import React from "react";
import { renderWithProviders } from "@src/utils/utils-for-tests";

export const mockAccessToken =
  "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6Ii1LSTNROW5OUjdiUm9meG1lWm9YcWJIWkdldyIsImtpZCI6Ii1LSTNROW5OUjdiUm9meG1lWm9YcWJIWkdldyJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.JI0MznY6MWni-rH20zJj9q4P4QYagtXqzFXp_v49R65vXG9c4Zzm34gnyTDvees44Y8hvcl2nMzt2aO40Bgq7TbmySPuwtcErPQ-A3twwTzAH9nH2rSdVBlN2OOneH_BafLYXKb1Lgm7vHwaXynOp9BFUyWmGguVevveWki_TLnfqnSdAX_oeX-xCJfnHntD8bhBjXT3fNCGOk7IWdYVaA-oFJMsJgh91cfWstDGY2eqscFy-KMAEN_RERgtwXjjA_teI7Wb4M8DPjfrEQjHsdLuERfOBslu-y4Ykev1eucAwyQ0LksCAv3xieSfE9d_nuKkjKDHmv9fpRQ9D2c4tg";

export const mockRefreshToken =
  "AbQAS/8UAAAAiN3Xo/jagcHL55UAlFl5xTdUWcgkaBujQ2SUEQNkiNEUB5uHWi/+dD1dTli/2lyT5xKgow7/gG5N1kwGzEtMorSm15ldIw0D4eJBz5BDE1AcnoLvf4jRHCxuFTIJbrMOUjaIj3JAPYhHqJjov9AnuKQUyYgPRkIpPZwLvOJ+3JOQFfKSUZZzjq/EI3S0R/yOFJ4ygvHuUNCg4AIbfd/XiegpGt/z+yDbsGplgjCmiAw=";

export const mockDateBeforeExpire = "2023-09-09T00:00:00.000Z";

jest.mock("react-native-app-auth", () => ({
  authorize: jest.fn(() => {
    return {
      accessToken: mockAccessToken,
      accessTokenExpirationDate: mockDateBeforeExpire,
      idToken: mockAccessToken,
      refreshToken: mockRefreshToken,
      tokenType: "Bearer",
      scopes: ["openid", "profile"],
      authorizationCode: "mockAuthorizationCode"
    };
  }),
  refresh: jest.fn(() => {
    return {
      accessToken: mockAccessToken,
      accessTokenExpirationDate: "2023-09-09T00:00:00.000Z",
      idToken: mockAccessToken,
      refreshToken: mockRefreshToken,
      tokenType: "Bearer",
      scopes: ["openid", "profile"],
      authorizationCode: "mockAuthorizationCode"
    };
  })
}));

jest.mock("@src/auth/AuthManager", () => {
  return {
    signInAsync: jest.fn(async () => {
    })
  };
});

// Mock the dependencies
jest.mock("react-native-encrypted-storage", () => ({
  setItem: jest.fn(async (key: string, value: string) => {
  }),
  getItem: jest.fn(async (key: string) => {
  }),
  removeItem: jest.fn(async (key: string) => {
  })
}));
describe("Login Screen Tests", () => {
  it("Should render and match the snapshot", () => {
    const tree = renderWithProviders(<LoginScreen />);
    expect(tree).toMatchSnapshot();
  });
});
