import { StyleSheet } from "react-native";
import { GLOBAL_MARGIN_HORIZONTAL } from "@src/constants";
import { CustomTheme } from "@src/theme";
import layout from "@src/theme/layout";
import { COMMON_COLORS } from "@src/theme/colors";

export const customStyles = (props: { theme: CustomTheme }) => {
  const { theme } = props;
  return StyleSheet.create({
    container: {
      flex: 1,
      marginHorizontal: GLOBAL_MARGIN_HORIZONTAL
    },
    divider: {
      marginVertical: 22,
      width: "100%",
      borderWidth: 0.5,
      backgroundColor: "#DDEBFF"
    },
    listPersonsContributed: {
      flexDirection: "row"
    },
    profileImage: {
      borderRadius: 16,
      marginStart: -20,
      width: 24,
      height: 24
    },
    interactionsContainer: {},
    headerListInteractions: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: 24,
      backgroundColor: theme.colors.PRIMARY
    }
  });
};

export const styles = StyleSheet.create({
  textNotFoundView: {
    ...layout.center,
    flex: 1
  },
  textNotFound: {
    color: COMMON_COLORS.BLACK
  }
});
