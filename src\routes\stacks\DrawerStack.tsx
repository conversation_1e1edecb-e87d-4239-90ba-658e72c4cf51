import React from "react";
import { ROUTE_NAMES } from "@src/constants/navigation";
import HomeScreen from "@src/screens/HomeScreen";
import ProjectHealthScreen from "@src/screens/ProjectHealthDetailsScreen";
import NotificationDetailsScreen from "src/screens/DiscussionDetailsScreen";
import NotificationsScreen from "@src/screens/NotificationsScreen";
import { createDrawerNavigator} from "@react-navigation/drawer";
import { COMMON_COLORS } from "@src/theme/colors";
import { useNavigation } from "@react-navigation/native";
import CustomDrawerContent from "@src/components/CustomDrawerContent";
import { Colors } from "react-native/Libraries/NewAppScreen";
import { createStackNavigator } from "@react-navigation/stack";

// Create drawer navigator
const Drawer = createDrawerNavigator();
const Stack = createStackNavigator()

/**
 * Screen wrapper components to provide toggleDrawer prop
 */
const ProjectHealthScreenWrapper = () => {
  const navigation = useNavigation();
  const toggleDrawer = () => {
    // @ts-ignore - openDrawer exists on the drawer navigation
    navigation.openDrawer();
  };
  return <ProjectHealthScreen toggleDrawer={toggleDrawer} />;
};

const NotificationsScreenWrapper = () => {
  const navigation = useNavigation();
  const toggleDrawer = () => {
    // @ts-ignore - openDrawer exists on the drawer navigation
    navigation.openDrawer();
  };
  return <NotificationsScreen toggleDrawer={toggleDrawer} />;
};

const DiscussionDetailsScreenWrapper = () => {
  const navigation = useNavigation();
  const toggleDrawer = () => {
    // @ts-ignore - openDrawer exists on the drawer navigation
    navigation.openDrawer();
  };
  return <NotificationDetailsScreen toggleDrawer={toggleDrawer} />;
};

export const DrawerStack = () => {

  return (
    <Drawer.Navigator
      // eslint-disable-next-line react/no-unstable-nested-components
      drawerContent={(props) => <CustomDrawerContent {...props} />}
      screenOptions={{
        drawerStyle: {
          backgroundColor: '#FF0000', // Red background
          width: '80%', // Width of the drawer
        },
        headerShown: true,
      }}
    >
      <Drawer.Screen name={ROUTE_NAMES.HOME} component={HomeScreen} />
      <Drawer.Screen
        name={ROUTE_NAMES.PROJECT_HEALTH_DETAILS}
        component={ProjectHealthScreenWrapper}
      />
      <Drawer.Screen
        name={ROUTE_NAMES.NOTIFICATIONS}
        component={NotificationsScreenWrapper}
      />
      <Drawer.Screen
        name={ROUTE_NAMES.DISCUSSION_DETAILS}
        component={DiscussionDetailsScreenWrapper}
      />
    </Drawer.Navigator>
  );
};
