import {call} from 'redux-saga/effects';
import {expectSaga} from 'redux-saga-test-plan';
import baseRequest from '@src/api/request';
import {handleCashFlowFeatures} from '@src/store/channels/cashFlow/saga';
import {ChannelsWebServices} from '@src/api/channels';
import {ChangeManagementChannelSummary} from '@src/models/channelsSummary/changeManagementChannelSummary';
import {handleChangeManagementFeatures} from '@src/store/channels/changeManagement/saga';
import {
  getChangeManagementFeaturesWithError,
  getChangeManagementFeaturesWithSuccess,
} from '@src/store/channels/changeManagement/slice';

describe('changeManagement saga', () => {
  const projectId = 22;
  const reportingDateFilter = {};
  const features: ChangeManagementChannelSummary = {
    channelStatus: 'PRIMARY',
  };
  it('should yield an api call', () => {
    expectSaga(handleChangeManagementFeatures)
      .provide([
        [
          call(
            baseRequest.get,
            ChannelsWebServices.WS_GET_CHANGE_MANAGEMENT_SUMMARY(projectId),
          ),
          {params: reportingDateFilter},
        ],
      ])
      .run();
  });

  it('should handle changeManagement flow features successfully', () => {
    expectSaga(handleCashFlowFeatures)
      .provide([
        [
          call(
            baseRequest.get,
            ChannelsWebServices.WS_GET_CHANGE_MANAGEMENT_SUMMARY(projectId),
          ),
          {params: reportingDateFilter},
        ],
      ])
      .put(getChangeManagementFeaturesWithSuccess({features}))
      .run();
  });

  it('should handle changeManagement features with error', () => {
    expectSaga(handleCashFlowFeatures)
      .provide([
        [
          call(
            baseRequest.get,
            ChannelsWebServices.WS_GET_CHANGE_MANAGEMENT_SUMMARY(projectId),
          ),
          {throw: true},
        ],
      ])
      .put(getChangeManagementFeaturesWithError())
      .run();
  });
});
