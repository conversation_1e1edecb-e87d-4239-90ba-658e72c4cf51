import {ROUTE_NAMES} from '@src/constants/navigation';
import {Linking} from 'react-native';

const config = {
  screens: {
    [ROUTE_NAMES.HOME]: {
      path: 'home',
      parse: {
        id: (id: any) => `${id}`,
      },
    },
    [ROUTE_NAMES.NOTIFICATIONS]: {
      path: 'notifications',
    },
    [ROUTE_NAMES.DISCUSSION_DETAILS]: {
      path: 'notifications/:id?',
      parse: {
        id: (id: number) => `${id}`,
      },
    },
  },
};

const linking = {
  prefixes: ['jesamobile://collab'],
  config,
  async getInitialURL() {
    // Check if app was opened from a deep link
    const url = await Linking.getInitialURL();

    if (url != null) {
      return url;
    }
    // check if the app is launched from a notification !
    // const message = await messaging().getInitialNotification();
    // if (message?.data?.link) {
    //   //baseRequest.post(NotificationsWebServices.WS_MARK_AS_READ);
    // }
    // return message?.data?.link;
    return ROUTE_NAMES.NOTIFICATIONS;
  },
  // here we add the listener to listen for notifications when the application is in the background !
  subscribe(listener: (arg0: string) => void) {
    const onReceiveURL = ({url}: {url: string}) => listener(url);
    // Listen to incoming links from deep linking
    Linking.addEventListener('url', onReceiveURL);

    return () => {
      // Clean up the event listeners
      (Linking as any).removeEventListener('url', onReceiveURL);
    };
  },
};

export default linking;
