import React from 'react';
import {Text, View} from 'react-native';
import style from './style';
import layout from '@src/theme/layout';
import {addCommasToNumber} from '@src/utils/stringUtils';

export const PendingJesaChange = (props: {value: number}) => {
  return (
    <View style={layout.row}>
      <Text style={style.valuePendingJesaChange}>
        {addCommasToNumber(props.value as unknown as number)}
      </Text>
      <Text style={style.MMAD}> MMAD</Text>
    </View>
  );
};
