import reducer, {
  cleanUpHse,
  getHseFeatures,
  getHseFeaturesWithError,
  getHseFeaturesWithSuccess,
  initialState,
} from '@src/store/channels/hse/slice';
import {HseChannelSummary} from '@src/models/channelsSummary/hseChannelSummary';

const features: HseChannelSummary = {
  channelStatus: 'PRIMARY',
};
describe('Hse reducer', () => {
  it('should return the initial state', () => {
    expect(reducer(undefined, {type: undefined})).toEqual(initialState);
  });

  it('should handle getHseFeatures action', () => {
    const action = getHseFeatures();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      loading: true,
      error: false,
      success: false,
    });
  });

  it('should handle getHseFeaturesWithSuccess action when the channel status is not null', () => {
    const action = getHseFeaturesWithSuccess({features});
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      features: action.payload.features,
      channelStatus: action.payload.features.channelStatus,
      loading: false,
      error: false,
      success: true,
    });
  });

  it('should handle getHseFeaturesWithSuccess action when the channel status is null', () => {
    const action = getHseFeaturesWithSuccess({
      features: {
        ...features,
        channelStatus: null,
      },
    });
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      features: action.payload.features,
      loading: false,
      error: false,
      success: true,
    });
  });

  it('should handle getHseFeaturesWithError action', () => {
    const action = getHseFeaturesWithError();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      loading: false,
      error: true,
      success: false,
    });
  });

  it('should handle cleanUp action', () => {
    const action = cleanUpHse();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
    });
  });
});
