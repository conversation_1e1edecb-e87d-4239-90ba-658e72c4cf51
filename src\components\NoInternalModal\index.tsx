import React from "react";
import { Modal, Text, TouchableOpacity, View } from "react-native";
import { BlurView } from "@react-native-community/blur";
import RNExitApp from "react-native-exit-app";
import { TEST_IDS } from "@src/constants/strings";
import useInternetConnectivity from "@src/hooks/useInternetConnectivity";
import { NoInternet } from "@src/svg/modals/NoInternet";
import { styles } from "./style";

const exitAppCallback = () => {
  RNExitApp.exitApp();
};

const NoInternetModal = () => {
  const { checkInternetConnectivity, isConnected } = useInternetConnectivity();

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={!isConnected}
      onRequestClose={exitAppCallback}>
      <View
        style={styles.modalContainer}
        testID={TEST_IDS.VIEW_NO_INTERNET_MODAL}>
        <BlurView
          style={styles.blurContainer}
          blurType="dark"
          blurAmount={20}
          reducedTransparencyFallbackColor="white"
        />
        <View style={styles.modalContent}>
          <NoInternet style={styles.no_internet_illustration} />
          <Text style={styles.title}>You are offline</Text>
          <Text style={styles.subTitle}>
            Check you connection and try again
          </Text>
          <TouchableOpacity
            onPress={checkInternetConnectivity}
            style={styles.updateButton}>
            <Text style={styles.updateButtonText}>Retry</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={exitAppCallback}
            style={styles.closeButton}>
            <Text style={styles.closeButtonText}>Close the app</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

export default NoInternetModal;
