import {Defs, G, LinearGradient, Path, Stop, Svg} from 'react-native-svg';
import React from 'react';
import {svgChannelIconHeight, svgChannelIconWidth} from '@src/theme/style';

export const ReportIcon = () => {
  return (
    <Svg
      id="trash"
      width={svgChannelIconWidth(57)}
      height={svgChannelIconHeight(52)}
      viewBox="0 0 57 52">
      <Defs>
        <LinearGradient
          id="linear-gradient"
          x1="0.158"
          y1="0.148"
          x2="0.886"
          y2="0.783"
          gradientUnits="objectBoundingBox">
          <Stop offset="0" stopColor="#007cff" />
          <Stop offset="1" stopColor="#0af" />
        </LinearGradient>
      </Defs>
      <G id="Report" transform="translate(-41.613 -92)">
        <Path
          id="Rectangle_5654"
          data-name="Rectangle 5654"
          d="M0,0H57a0,0,0,0,1,0,0V42A10,10,0,0,1,47,52H10A10,10,0,0,1,0,42V0A0,0,0,0,1,0,0Z"
          transform="translate(41.613 92)"
          fill="url(#linear-gradient)"
        />
        <G
          id="Report-2"
          data-name="ReportMiniIcon"
          transform="translate(54.613 102)">
          <Path
            id="Tracé_53094"
            data-name="Tracé 53094"
            d="M25,32H7a3,3,0,0,1-3-3V7A3,3,0,0,1,7,4H8A1,1,0,0,1,8,6H7A1,1,0,0,0,6,7V29a1,1,0,0,0,1,1H25a1,1,0,0,0,1-1V16a1,1,0,0,1,2,0V29a3,3,0,0,1-3,3Z"
            fill="#fff"
          />
          <Path
            id="Tracé_53095"
            data-name="Tracé 53095"
            d="M27,13a1,1,0,0,1-1-1V7a1,1,0,0,0-1-1H20a1,1,0,0,1,0-2h5a3,3,0,0,1,3,3v5A1,1,0,0,1,27,13Z"
            fill="#fff"
          />
          <Path
            id="Tracé_53096"
            data-name="Tracé 53096"
            d="M19,8H13a2,2,0,0,1-2-2V4a2,2,0,0,1,2-2h6a2,2,0,0,1,2,2V6A2,2,0,0,1,19,8ZM13,4V6h6V4Z"
            fill="#fff"
          />
          <Path
            id="Tracé_53097"
            data-name="Tracé 53097"
            d="M16,4a1,1,0,0,1-1-1V1a1,1,0,1,1,2,0V3A1,1,0,0,1,16,4Z"
            fill="#fff"
          />
          <Path
            id="Tracé_53098"
            data-name="Tracé 53098"
            d="M13,17H9a1,1,0,0,1-1-1V12a1,1,0,0,1,1-1h4a1,1,0,0,1,1,1v4A1,1,0,0,1,13,17Zm-3-2h2V13H10Z"
            fill="#fff"
          />
          <Path
            id="Tracé_53099"
            data-name="Tracé 53099"
            d="M13,26H9a1,1,0,0,1-1-1V21a1,1,0,0,1,1-1h4a1,1,0,0,1,1,1v4A1,1,0,0,1,13,26Zm-3-2h2V22H10Z"
            fill="#fff"
          />
          <Path
            id="Tracé_53100"
            data-name="Tracé 53100"
            d="M23,15H17a1,1,0,0,1,0-2h6a1,1,0,0,1,0,2Z"
            fill="#fff"
          />
          <Path
            id="Tracé_53101"
            data-name="Tracé 53101"
            d="M23,24H17a1,1,0,0,1,0-2h6a1,1,0,0,1,0,2Z"
            fill="#fff"
          />
        </G>
      </G>
    </Svg>
  );
};
