/*
const {
  mockAccessToken,
  mockRefreshToken,
} = require('@src/auth/__tests__/index.test');

jest.mock('react-native-app-auth', () => ({
  authorize: jest.fn(() => {
    return {
      accessToken: mockAccessToken,
      accessTokenExpirationDate: '2023-09-09T00:00:00.000Z',
      idToken: mockAccessToken,
      refreshToken: mockRefreshToken,
      tokenType: 'Bearer',
      scopes: ['openid', 'profile'],
      authorizationCode: 'mockAuthorizationCode',
    };
  }),
  refresh: jest.fn(() => {
    return {
      accessToken: mockAccessToken,
      accessTokenExpirationDate: '2023-09-09T00:00:00.000Z',
      idToken: mockAccessToken,
      refreshToken: mockRefreshToken,
      tokenType: 'Bearer',
      scopes: ['openid', 'profile'],
      authorizationCode: 'mockAuthorizationCode',
    };
  }),
}));
*/
