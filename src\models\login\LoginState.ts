export class LoginState {
  isLoggedIn: boolean
  token: string
  email: string
  password: string
  isLoginLoading: boolean;
  message: string;

  constructor(isLoggedIn: boolean, token: string, email: string, password: string, isLoginLoading: boolean, message: string) {
    this.isLoggedIn = isLoggedIn;
    this.token = token;
    this.email = email;
    this.password = password;
    this.isLoginLoading = isLoginLoading;
    this.message = message;
  }
}