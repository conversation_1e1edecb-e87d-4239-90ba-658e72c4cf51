import React from 'react';
import {fireEvent} from '@testing-library/react-native';
import ClickableImage from '@src/components/ClickableImage';
import {renderWithProviders} from '@src/utils/utils-for-tests';

jest.mock('@src/utils/imageUtils', () => ({
  correspondentWidth: jest.fn(() => 2),
  correspondentHeight: jest.fn(() => 2),
}));

// Now you can use correspondantWidth and correspondantHeight as mocked functions in your tests.

describe('ClickableImage', () => {
  const onPress = jest.fn();
  const imageStyle = jest.fn();
  const url = jest.fn();
  const testID = 'clickable-image';
  it('calls onPress when the image is clicked', () => {
    const {getByTestId} = renderWithProviders(
      <ClickableImage
        onPress={onPress}
        imageStyle={imageStyle}
        url={url}
        testID={testID}
        SvgComponent={null}
      />,
    );

    const clickableImage = getByTestId(testID);
    fireEvent.press(clickableImage);
    expect(onPress).toHaveBeenCalled();
  });
  it('Should render and match the snapshot', () => {
    const tree = renderWithProviders(
      <ClickableImage
        onPress={onPress}
        imageStyle={imageStyle}
        url={url}
        testID={testID}
        SvgComponent={null}
      />,
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
