import reducer, {
  cleanUpHumanCap,
  getHumanCapFeatures,
  getHumanCapFeaturesWithError,
  getHumanCapFeaturesWithSuccess,
  initialState,
} from '@src/store/channels/humanCapital/slice';
import {HumanCapitalChannelSummary} from '@src/models/channelsSummary/humanCapitalChannelSummary';

const features: HumanCapitalChannelSummary = {
  channelStatus: 'PRIMARY',
  contractorHeadCount: 12,
  jesaHeadCount: 21,
};
describe('HumanCap reducer', () => {
  it('should return the initial state', () => {
    expect(reducer(undefined, {type: undefined})).toEqual(initialState);
  });

  it('should handle getHumanCapFeatures action', () => {
    const action = getHumanCapFeatures();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      loading: true,
      error: false,
      success: false,
    });
  });

  it('should handle getHumanCapFeaturesWithSuccess action when the channel status is not null', () => {
    const action = getHumanCapFeaturesWithSuccess({features});
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      features: action.payload.features,
      channelStatus: action.payload.features.channelStatus,
      loading: false,
      error: false,
      success: true,
    });
  });

  it('should handle getHumanCapFeaturesWithSuccess action when the channel status is null', () => {
    const action = getHumanCapFeaturesWithSuccess({
      features: {
        ...features,
        channelStatus: null,
      },
    });
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      features: action.payload.features,
      loading: false,
      error: false,
      success: true,
    });
  });

  it('should handle getHumanCapFeaturesWithError action', () => {
    const action = getHumanCapFeaturesWithError();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      loading: false,
      error: true,
      success: false,
    });
  });

  it('should handle cleanUp action', () => {
    const action = cleanUpHumanCap();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
    });
  });
});
