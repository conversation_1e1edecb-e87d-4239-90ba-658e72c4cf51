import {StyleSheet} from 'react-native';
import {
  GLOBAL_MARGIN_HORIZONTAL,
  PERCENTAGES,
  windowWidth,
} from '@src/constants';
import layout from '@src/theme/layout';
import {COMMON_COLORS} from '@src/theme/colors';
import {correspondentHeight, correspondentWidth} from '@src/utils/imageUtils';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: COMMON_COLORS.WHITE,
    ...layout.rowSpaceBetween,
    width: windowWidth,
    shadowOffset: {
      width: 0,
      height: -6,
    },
    shadowOpacity: 0.1,
    shadowRadius: 11,
    elevation: 12,
    left: -GLOBAL_MARGIN_HORIZONTAL,
  },
  iconContainer: {
    width: correspondentWidth(43),
    height: correspondentHeight(32),
    marginEnd: correspondentWidth(10),
    marginStart: -correspondentWidth(16),
  },
  profileImage: {
    borderRadius: correspondentWidth(100) / 2,
    width: correspondentWidth(42),
    aspectRatio: 1,
  },
  imageContainer: {
    marginHorizontal: correspondentWidth(16),
    marginVertical: correspondentHeight(16),
  },
  userAvatarInMentionList: {
    width: correspondentWidth(33),
    aspectRatio: 1,
    borderRadius: 12,
  },

  taggedPersonContainer: {
    ...layout.rowCenter,
    paddingHorizontal: correspondentWidth(16),
    marginHorizontal: correspondentWidth(12),
    height: correspondentHeight(50),
  },
  userAvatar: {
    marginEnd: correspondentWidth(12),
  },
  flat_list_filtered_users: {
    position: 'absolute',
    backgroundColor: COMMON_COLORS.WHITE,
    shadowColor: COMMON_COLORS.BLACK,
    width: correspondentWidth(270),
    zIndex: 10000,
    borderRadius: 8,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  flat_list_filtered_users_content_container_style: {
    alignItems: 'flex-start',
  },
  width_text_input: {
    width: PERCENTAGES['70'],
  },
});
