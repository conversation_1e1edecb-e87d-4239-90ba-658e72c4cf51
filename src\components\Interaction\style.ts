import { StyleSheet } from "react-native";
import { OPACITY, PERCENTAGES, windowHeight, windowWidth } from "@src/constants";
import layout from "@src/theme/layout";

import typography from "@src/theme/fonts";
import { COMMON_COLORS } from "@src/theme/colors";

const styles = StyleSheet.create({
  container: {
    marginBottom: 16
  },
  iconTitleTimeNotificationContainer: {
    ...layout.rowSpaceBetweenCenter
  },
  childInteraction: {
    marginStart: 37,
    backgroundColor: "#F1F7FF",
    paddingVertical: 14,
    paddingHorizontal: 14
  },
  titleIcon: {
    borderRadius: 12,
    width: PERCENTAGES["100"],
    height: PERCENTAGES["100"]
  },
  iconContainer: {
    width: windowWidth * 0.085,
    borderRadius: 12,
    aspectRatio: 1
  },
  time: {
    textDecorationColor: COMMON_COLORS.BLUE_20,
    fontSize: 12,
    opacity: OPACITY["70"]
  },
  content: {
    color: COMMON_COLORS.BLUE_20,
    fontSize: 14
  },
  leftContainer: {
    flexDirection: "row",
    maxWidth: PERCENTAGES["70"],
    minWidth: PERCENTAGES["70"],
    alignItems: "center"
  },
  contentContainer: {},
  createCommentContainer: {
    marginStart: windowWidth * 0.02,
    maxWidth: windowWidth * 0.5
  },
  creator: {
    fontSize: typography.fontSizes.sm,
    color: COMMON_COLORS.BLUE_20,
    fontWeight: "bold"
  },
  newComment: {
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 2,
    marginStart: windowWidth * 0.02
  },
  bottomLine: {
    backgroundColor: "#DDEBFF",
    height: 1,
    marginTop: 23
  },
  subTitle: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: "4%"
  },
  notificationContent: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: "4%",
    textAlign: "center"
  },
  button: {
    backgroundColor: "blue",
    padding: 12,
    paddingHorizontal: 26,
    alignItems: "center",
    borderRadius: 8
  },
  buttonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "bold"
  },
  buttonContainer: {
    ...layout.rowEndCenter
  },
  commentBottom: {
    marginTop: windowHeight * 0.01,
    flexDirection: "row"
  },
  verticalDivider: {
    borderWidth: 1,
    borderColor: "#007EFF",
    marginHorizontal: 8
  },
  numberLikes: {
    ...layout.rowHCenter
  },
  contentAndBottomCommentContainer: {
    marginStart: windowWidth * 0.1
  },
  replyText: { color: "#007CFF", fontSize: 13, fontWeight: "bold" },
  replyTextContainer: {
    ...layout.rowSpaceBetweenCenter
  }
});
export default styles;
