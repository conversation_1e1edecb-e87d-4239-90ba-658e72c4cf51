import {call} from 'redux-saga/effects';
import {expectSaga} from 'redux-saga-test-plan';
import baseRequest from '@src/api/request';
import {handleCashFlowFeatures} from '@src/store/channels/cashFlow/saga';
import {ChannelsWebServices} from '@src/api/channels';
import {handleCostFeatures} from '@src/store/channels/cost/saga';
import {HumanCapitalChannelSummary} from '@src/models/channelsSummary/humanCapitalChannelSummary';
import {handleHumanCapFeatures} from '@src/store/channels/humanCapital/saga';
import {
  getHumanCapFeaturesWithError,
  getHumanCapFeaturesWithSuccess,
} from '@src/store/channels/humanCapital/slice';

describe('humanCap saga', () => {
  const projectId = 22;
  const reportingDateFilter = {};
  const features: HumanCapitalChannelSummary = {
    jesaHeadCount: 12,
    contractorHeadCount: 12,
    channelStatus: 'PRIMARY',
  };
  it('should yield an api call', () => {
    expectSaga(handleHumanCapFeatures)
      .provide([
        [
          call(
            baseRequest.get,
            ChannelsWebServices.WS_GET_HUMAN_CAPITAL_SUMMARY(projectId),
          ),
          {params: reportingDateFilter},
        ],
      ])
      .run();
  });

  it('should handle human cap features successfully', () => {
    expectSaga(handleCostFeatures)
      .provide([
        [
          call(
            baseRequest.get,
            ChannelsWebServices.WS_GET_HUMAN_CAPITAL_SUMMARY(projectId),
          ),
          {params: reportingDateFilter},
        ],
      ])
      .put(getHumanCapFeaturesWithSuccess({features}))
      .run();
  });

  it('should handle human cap features with error', () => {
    expectSaga(handleCashFlowFeatures)
      .provide([
        [
          call(
            baseRequest.get,
            ChannelsWebServices.WS_GET_HUMAN_CAPITAL_SUMMARY(projectId),
          ),
          {throw: true},
        ],
      ])
      .put(getHumanCapFeaturesWithError())
      .run();
  });
});
