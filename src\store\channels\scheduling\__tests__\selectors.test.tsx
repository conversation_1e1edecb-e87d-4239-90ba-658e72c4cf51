import {RootState} from '@src/store/store';
import {SchedulingSliceType} from '@src/store/channels/scheduling/slice';
import {schedulingSelector} from '@src/store/channels/scheduling/selectors';

describe('Scheduling Selector', () => {
  it('should select the scheduling state from the root state', () => {
    // Arrange
    const schedulingState: SchedulingSliceType = {
      features: null,
      channelStatus: 'PRIMARY',
      error: false,
      success: false,
    };

    // @ts-ignore
    const mockRootState: RootState = {
      scheduleChannel: schedulingState,
    };

    // Act
    const selectedState = schedulingSelector(mockRootState);

    // Assert
    expect(selectedState).toEqual(schedulingState);
  });
});
