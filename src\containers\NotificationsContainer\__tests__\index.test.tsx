import React from 'react';
import {renderWithProviders} from '@src/utils/utils-for-tests';
import NotificationsContainer from '@src/containers/NotificationsContainer';
import {NavigationContainer} from '@react-navigation/native';

describe('Notifications Container', () => {
  it('should render and match the snapshot', () => {
    const toggleDrawer = jest.fn();
    const tree = renderWithProviders(
      <NavigationContainer>
        <NotificationsContainer
          toggleDrawer={toggleDrawer}
        />
      </NavigationContainer>,
      {},
    );
    expect(tree).toMatchSnapshot();
  });
});
