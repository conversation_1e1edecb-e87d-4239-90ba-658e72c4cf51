import React from 'react';
import {G, Path, Svg} from 'react-native-svg';
import {svgChannelIconHeight, svgChannelIconWidth} from '@src/theme/style';

const HumanCapitalFocusedIcon = () => (
  <Svg
    id="Groupe_52558"
    data-name="Groupe 52558"
    width={svgChannelIconWidth(28.051)}
    height={svgChannelIconHeight(27.626)}
    viewBox="0 0 28.051 27.626">
    <G id="Groupe_447" data-name="Groupe 447" transform="translate(0 12.246)">
      <Path
        id="Tracé_9243"
        data-name="Tracé 9243"
        d="M731.39,512.738a2.542,2.542,0,0,0-1.567-.657,2.244,2.244,0,0,0-1.724.656l-3.119,3.086a2.068,2.068,0,0,0-.211-.3,2.022,2.022,0,0,0-1.58-.707h-4.366a8.954,8.954,0,0,1-2.387-.317,5.873,5.873,0,0,0-5.479,1.159,7.949,7.949,0,0,0-1.427,1.531l-.783-.76a.556.556,0,0,0-.386-.157h0a.558.558,0,0,0-.4.17l-3.806,3.951a.553.553,0,0,0,.017.783l6.437,6.129a.552.552,0,0,0,.776-.014l3.77-3.868a.555.555,0,0,0-.011-.785l-.328-.318.751-.085a11.947,11.947,0,0,1,1.375-.08h6.643a4.989,4.989,0,0,0,2.725-.7l.018-.012a3.685,3.685,0,0,0,.7-.639l4.477-5.267a1.928,1.928,0,0,0-.119-2.8Zm-20.414,13.393-5.64-5.371,3.038-3.154,5.607,5.441Zm19.693-11.309-4.477,5.267a2.579,2.579,0,0,1-.485.442,3.924,3.924,0,0,1-2.122.522h-6.643a13,13,0,0,0-1.5.087l-1.652.185-3.459-3.356a6.888,6.888,0,0,1,1.322-1.455,4.751,4.751,0,0,1,4.487-.951,10.059,10.059,0,0,0,2.681.357h4.366a1,1,0,0,1,.993,1.081,1.194,1.194,0,0,1-.258.766.937.937,0,0,1-.753.316h-3.8a.552.552,0,0,0,0,1.1h3.8a2.047,2.047,0,0,0,1.594-.7,2.266,2.266,0,0,0,.52-1.406l3.592-3.555a1.292,1.292,0,0,1,1.764.029.831.831,0,0,1,.027,1.272Zm0,0"
        transform="translate(-704.002 -512.079)"
        fill="#007cff"
      />
    </G>
    <Path
      id="Tracé_9244"
      data-name="Tracé 9244"
      d="M933.88,621.49a.552.552,0,0,0-1.074.252l.005.023a.552.552,0,0,0,.537.426.569.569,0,0,0,.126-.014.552.552,0,0,0,.411-.663Zm0,0"
      transform="translate(-920.231 -602.835)"
      fill="#007cff"
    />
    <Path
      id="Tracé_9245"
      data-name="Tracé 9245"
      d="M748.941,300.634h5.377a1.331,1.331,0,0,1,1.568,0h5.377a1.33,1.33,0,0,1,1.568,0h5.377a1.338,1.338,0,0,0,1.336-1.336v-2.507a2.894,2.894,0,0,0-1.574-2.587,3.183,3.183,0,1,0-4.9,0,1.034,1.034,0,0,1-2.044,0,3.183,3.183,0,1,0-4.9,0,1.034,1.034,0,0,1-2.044,0,3.183,3.183,0,1,0-4.9,0,2.894,2.894,0,0,0-1.574,2.587V299.3a1.338,1.338,0,0,0,1.336,1.336ZM765.521,290.1a2.079,2.079,0,1,1-2.079,2.079,2.082,2.082,0,0,1,2.079-2.079Zm-1.434,4.92a3.172,3.172,0,0,0,2.867,0,1.793,1.793,0,0,1,1.487,1.775V299.3a.233.233,0,0,1-.232.233h-5.377a.233.233,0,0,1-.232-.233v-2.507a1.793,1.793,0,0,1,1.487-1.775Zm-5.512-4.92a2.079,2.079,0,1,1-2.079,2.079,2.082,2.082,0,0,1,2.079-2.079Zm-1.433,4.92a3.172,3.172,0,0,0,2.867,0,1.793,1.793,0,0,1,1.487,1.775V299.3a.233.233,0,0,1-.232.233h-5.377a.233.233,0,0,1-.232-.233v-2.507a1.793,1.793,0,0,1,1.487-1.775Zm-5.512-4.92a2.079,2.079,0,1,1-2.079,2.079,2.081,2.081,0,0,1,2.079-2.079Zm-2.921,6.7a1.793,1.793,0,0,1,1.487-1.775,3.172,3.172,0,0,0,2.867,0,1.793,1.793,0,0,1,1.488,1.775V299.3a.233.233,0,0,1-.233.233h-5.377a.233.233,0,0,1-.232-.233Zm0,0"
      transform="translate(-745.211 -288.992)"
      fill="#007cff"
    />
  </Svg>
);

export default HumanCapitalFocusedIcon;
