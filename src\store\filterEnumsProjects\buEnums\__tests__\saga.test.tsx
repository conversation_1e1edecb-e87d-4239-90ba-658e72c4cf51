import {call} from 'redux-saga/effects';
import {expectSaga} from 'redux-saga-test-plan';
import baseRequest from '@src/api/request';
import {handleBuEnums} from '@src/store/filterEnumsProjects/buEnums/saga';
import {ProjectWebServices} from '@src/api/project';
import {
  getBuEnumsWithError,
  getBuEnumsWithSuccess,
} from '@src/store/filterEnumsProjects/buEnums/slice';
import ProjectFilter from '@src/models/projectFilter';

const buEnumsExamples: ProjectFilter[] = [
  {
    type: 'type',
    code: 'code',
    label: 'label',
    id: 2,
  },
];
describe('bu enums saga', () => {
  it('should yield an api call', () => {
    // @ts-ignore
    return expectSaga(handleBuEnums)
      .provide([
        [
          call(baseRequest.get, ProjectWebServices.WS_GET_BU_ENUMS),
          {param: ''},
        ],
      ])
      .run();
  });

  it('should handle bu enums successfully', () => {
    // @ts-ignore
    expectSaga(handleBuEnums)
      .provide([
        [call(baseRequest.get, ProjectWebServices.WS_GET_BU_ENUMS), {}],
      ])
      .put(getBuEnumsWithSuccess(buEnumsExamples))
      .run();
  });

  it('should handle bu enums with error', () => {
    // @ts-ignore
    expectSaga(handleBuEnums)
      .provide([
        [
          call(baseRequest.get, ProjectWebServices.WS_GET_BU_ENUMS),
          {throw: true},
        ],
      ])
      .put(getBuEnumsWithError())
      .run();
  });
});
