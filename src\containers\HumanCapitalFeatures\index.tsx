import { View } from "react-native";
import { ObjectUtils } from "@src/utils/objectUtils";
import React from "react";
import {
  COLLAB_FEATURES,
  COLLAB_FEATURES_TITLES,
  COLLAB_SUMMARY_FEATURE_KEY_WITH_FEATURES_CODE,
  getSectionValueByKey
} from "@src/constants/channels/features";
import { useSelector } from "react-redux";
import { humanCapSelector } from "@src/store/channels/humanCapital/selectors";
import Loader from "@src/components/Loader";
import { KEYS_CHANNEL } from "@src/constants/channels/modules";
import { ChannelFeaturesDefaultValues } from "@src/constants/channelFeaturesDefaultValues";
import FeatureContainer, { renderFeatureContainers } from "@src/containers/FeatureContainer";

const HumanCapitalFeatures = () => {
  const { features, loading } = useSelector(humanCapSelector);


  function renderDefaultValues() {
    const humanCapitalFlowFeatures: FeatureContainer[] = [
      {
        featureKey: COLLAB_FEATURES.HUMAN_CAPITAL.HUMAN_CAP_PROJECT_CLIENT_TEAM,
        featureTitle: COLLAB_FEATURES_TITLES.HUMAN_CAPITAL.jesaHeadCount,
        defaultFeatureValue: ChannelFeaturesDefaultValues.HUMAN_CAPITAL.jesaHeadCount
      },
      {
        featureKey: COLLAB_FEATURES.HUMAN_CAPITAL.HUMAN_CAP_PROJECT_CONTRACTOR_TEAM,
        featureTitle: COLLAB_FEATURES_TITLES.HUMAN_CAPITAL.contractorHeadCount,
        defaultFeatureValue: ChannelFeaturesDefaultValues.HUMAN_CAPITAL.contractorHeadCount
      }
    ];
    return renderFeatureContainers(humanCapitalFlowFeatures);
  }

  if (loading) {
    return <Loader show={true} />;
  }

  return (
    <View>
      {features
        ? ObjectUtils.convertObjectToKeyValuesArray(features).map((item) => {
          if (item.key !== KEYS_CHANNEL.COMMON.CHANNEL_STATUS) {
            return (
              <FeatureContainer
                key={item.key}
                featureKey={
                  getSectionValueByKey(COLLAB_SUMMARY_FEATURE_KEY_WITH_FEATURES_CODE, "HUMAN_CAPITAL", item.key)
                }
                featureTitle={
                  getSectionValueByKey(COLLAB_FEATURES_TITLES, "HUMAN_CAPITAL", item.key)
                }
                featureValue={item.value}
                defaultFeatureValue={
                  getSectionValueByKey(ChannelFeaturesDefaultValues, "HUMAN_CAPITAL", item.key)
                }
              />
            );
          }
          return null;
        })
        : renderDefaultValues()}
    </View>
  );
};

export default HumanCapitalFeatures;
