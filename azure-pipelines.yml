# Android
# Build your Android project with <PERSON><PERSON><PERSON>.
# Add steps that test, sign, and distribute the APK, save build artifacts, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/android

pool:
  vmImage: 'ubuntu-latest'

name: $(date:yyyy).$(Month)$(rev:.r)

steps:

- script: npm install

- task: Gradle@2
  displayName: "Releasing task"
  inputs:
    gradleWrapperFile: 'android/gradlew'
    workingDirectory: 'android/'
    #options: '-PversionName=$(Build.BuildNumber) -PversionCode=$(Build.BuildId)'
    tasks: 'assembleRelease'
    publishJUnitResults: false
    javaHomeOption: 'JDKVersion'
    gradleOptions: '-Xmx3072m'

- task: AndroidSigning@3
  displayName: "Signing the application"
  inputs:
    apkFiles: '**/*.apk'
    apksignerKeystoreFile: 'collab-mobile-recette.keystore'
    apksignerKeystorePassword: '$(KeyStorePassword)'
    apksignerKeystoreAlias: '$(KeyAlias)'
    apksignerKeyPassword: '$(KeyPassword)'
    zipalign: false

- task: PublishBuildArtifacts@1
  displayName: "publishing the apk in the output folder"
  inputs:
    PathtoPublish: 'android/app/build/outputs/apk/release'
    ArtifactName: 'drop'
    publishLocation: 'Container'
    
- task: JavaToolInstaller@0
  displayName: 'Use Java 17'
  inputs:
    versionSpec: 17
    jdkArchitectureOption: x64
    jdkSourceOption: PreInstalled
    

- task: Gradle@3
  displayName: 'Firebase app distribution'
  inputs:
    gradleWrapperFile: 'android/gradlew'
    workingDirectory: 'android/'
    tasks: appDistributionUploadRelease