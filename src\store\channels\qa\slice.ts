import { createSlice } from "@reduxjs/toolkit";
import { SLICES_NAMES } from "@src/constants/store";
import { QualityChannelSummary } from "@src/models/channelsSummary/qualityChannelSummary";

export type QaSliceType = {
  features: QualityChannelSummary | null;
  channelStatus: string;
  error: boolean;
  success: boolean;
  loading: boolean;
};

export const initialState: QaSliceType = {
  features: null,
  channelStatus: "PRIMARY",
  error: false,
  success: false,
  loading: false
};
export const slice = createSlice({
  name: SLICES_NAMES.QA_CHANNEL,
  initialState,
  reducers: {
    getQAFeatures: state => {
      return { ...state, loading: true, error: false, success: false };
    },
    getQAFeaturesWithSuccess: (
      state,
      action: {
        payload: {
          features: QualityChannelSummary;
        };
      }
    ) => {
      let finalChannelStatus: any;
      if (action.payload.features.channelStatus !== null) {
        finalChannelStatus = action.payload.features.channelStatus!!;
      } else {
        finalChannelStatus = "PRIMARY";
      }
      return {
        ...state,
        features: action.payload.features,
        channelStatus: finalChannelStatus,
        error: false,
        success: true,
        loading: false
      };
    },
    getQAFeaturesWithError: state => {
      return {
        ...state,
        features: null,
        channelStatus: "PRIMARY",
        error: true,
        success: false,
        loading: false
      };
    },
    cleanUpQa: () => {
      return initialState;
    }
  }
});

export const {
  getQAFeatures,
  getQAFeaturesWithSuccess,
  getQAFeaturesWithError,
  cleanUpQa
} = slice.actions;
export default slice.reducer;
