<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Collab</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSExceptionDomains</key>
		<dict>
			<key>localhost</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>FirebaseAppDelegateProxyEnabled</key>
	<string>true</string>
	<key>NSAppleMusicUsageDescription</key>
	<string>YOUR TEXT</string>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>YOUR TEXT</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>YOUR TEXT</string>
	<key>NSCalendarsUsageDescription</key>
	<string>YOUR TEXT</string>
	<key>NSCameraUsageDescription</key>
	<string>YOUR TEXT</string>
	<key>NSContactsUsageDescription</key>
	<string>YOUR TEXT</string>
	<key>NSFaceIDUsageDescription</key>
	<string>YOUR TEXT</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>YOUR TEXT</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>YOUR TEXT</string>
	<key>NSLocationTemporaryUsageDescriptionDictionary</key>
	<dict>
		<key>YOUR-PURPOSE-KEY</key>
		<string>YOUR TEXT</string>
	</dict>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string></string>
	<key>UIAppFonts</key>
	<array>
		<string>VinaSans-Regular.ttf</string>
		<string>Montserrat-Black.ttf</string>
		<string>Montserrat-BlackItalic.ttf</string>
		<string>Montserrat-Bold.ttf</string>
		<string>Montserrat-BoldItalic.ttf</string>
		<string>Montserrat-ExtraBold.ttf</string>
		<string>Montserrat-ExtraBoldItalic.ttf</string>
		<string>Montserrat-ExtraLight.ttf</string>
		<string>Montserrat-ExtraLightItalic.ttf</string>
		<string>Montserrat-Italic.ttf</string>
		<string>Montserrat-Light.ttf</string>
		<string>Montserrat-LightItalic.ttf</string>
		<string>Montserrat-Medium.ttf</string>
		<string>Montserrat-MediumItalic.ttf</string>
		<string>Montserrat-Regular.ttf</string>
		<string>Montserrat-SemiBold.ttf</string>
		<string>Montserrat-SemiBoldItalic.ttf</string>
		<string>Montserrat-Thin.ttf</string>
		<string>Montserrat-ThinItalic.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
