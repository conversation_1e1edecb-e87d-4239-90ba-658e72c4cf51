import {StyleSheet} from 'react-native';
import layout from '@src/theme/layout';
import {COMMON_COLORS} from '@src/theme/colors';
import {correspondentHeight, correspondentWidth} from '@src/utils/imageUtils';
import typography from '@src/theme/fonts';

export const styles = StyleSheet.create({
  description: {
    ...layout.rowCenter,
    marginVertical: correspondentHeight(12),
  },
  descriptionText: {
    color: COMMON_COLORS.BLUE_20,
  },
  dateContainer: {
    ...layout.rowCenter,
    alignSelf: 'flex-end',
    marginTop: correspondentHeight(8),
  },
  dateIcon: {
    marginEnd: correspondentWidth(14),
  },
  periodValue: {
    color: COMMON_COLORS.BLUE_10,
    fontSize: typography.fontSizes.xs + 1,
    //fontFamily: typography.fontFamily.montserratBold,
    fontWeight: '800',
  },
});
