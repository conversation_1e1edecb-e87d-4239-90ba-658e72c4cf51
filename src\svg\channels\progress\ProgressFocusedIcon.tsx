import React from 'react';
import {G, Path, Svg} from 'react-native-svg';
import {svgChannelIconHeight, svgChannelIconWidth} from '@src/theme/style';

const ProgressFocusedIcon = () => (
  <Svg
    width={svgChannelIconWidth(26.795)}
    height={svgChannelIconHeight(26.861)}
    viewBox="0 0 26.795 26.861">
    <G
      id="Groupe_52555"
      data-name="Groupe 52555"
      transform="translate(0.15 0.15)">
      <Path
        id="Tracé_9225"
        data-name="Tracé 9225"
        d="M793.717,349.926l-5.407-1.52v-.016a.39.39,0,0,0-.779,0v8.088h-.553a.807.807,0,0,0-.806.805v3.531H780.92a.806.806,0,0,0-.805.805v3.53h-5.252a.806.806,0,0,0-.805.805v3.531h-5.252a.806.806,0,0,0-.806.806v3.465a.806.806,0,0,0,.806.805h20.748a.39.39,0,1,0,0-.78h-2.6v-16.5a.026.026,0,0,1,.026-.026H792.2a.026.026,0,0,1,.026.026v16.473a.026.026,0,0,1-.026.026h-.789a.39.39,0,1,0,0,.78h.789a.806.806,0,0,0,.806-.805V357.283a.806.806,0,0,0-.806-.805H788.31V353.5l1.016-.286a.39.39,0,0,0-.211-.75l-.8.226v-3.471l5.2,1.46a.286.286,0,0,1,0,.551l-2.755.774a.39.39,0,1,0,.211.75l2.755-.774a1.066,1.066,0,0,0,0-2.052Zm-24.938,23.83v-3.465a.026.026,0,0,1,.026-.026h5.252v3.516h-5.252a.026.026,0,0,1-.026-.026Zm11.725-5.015a.39.39,0,0,0-.39.39v4.651h-5.278v-7.827a.026.026,0,0,1,.026-.026h5.252v1.414a.39.39,0,1,0,.779,0v-5.724a.026.026,0,0,1,.026-.026h5.252v12.189h-5.278v-4.651a.389.389,0,0,0-.389-.39Zm0,0"
        transform="translate(-768 -348)"
        fill="#007cff"
        stroke="#007cff"
        stroke-width="0.3"
      />
    </G>
  </Svg>
);

export default ProgressFocusedIcon;
