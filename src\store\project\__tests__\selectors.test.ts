import {RootState} from '@src/store/store';
import {initialState, ProjectSliceType} from '@src/store/project/slice';
import {projectSelector} from '@src/store/project/selectors';

describe('projectSelector', () => {
  it('should select the project state from the root state', () => {
    // Arrange
    const projectState: ProjectSliceType = initialState;

    // @ts-ignore
    const mockRootState: RootState = {
      project: projectState,
    };

    // Act
    const selectedState = projectSelector(mockRootState);

    // Assert
    expect(selectedState).toEqual(projectState);
  });
});
