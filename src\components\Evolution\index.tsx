import {Text, View} from 'react-native';
import style from '@src/components/CriticalSOR/style';
import React from 'react';
import layout from '@src/theme/layout';
import {DownArrowEvolution} from '@src/svg/DownArrowEvolution';
import {UpArrowEvolution} from '@src/svg/UpArrowEvolution';

export const Evolution = (props: {
  value: {currentEvolution: number; previousEvolution: number};
}) => {
  return (
    <View style={layout.rowSpaceBetweenCenter}>
      <View style={[layout.rowCenter, style.previousArrow]}>
        <View style={style.arrowStyle}>
          <UpArrowEvolution />
        </View>
        <Text style={style.valueEvolution}>
          {props.value.previousEvolution}
        </Text>
      </View>
      <View style={[layout.rowCenter]}>
        <View style={style.arrowStyle}>
          <DownArrowEvolution />
        </View>
        <Text style={style.valueEvolution}>{props.value.currentEvolution}</Text>
      </View>
    </View>
  );
};
