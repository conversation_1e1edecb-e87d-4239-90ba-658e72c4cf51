import { StyleSheet } from "react-native";
import { windowHeight, windowWidth } from "@src/constants";
import typography from "@src/theme/fonts";
import { COMMON_COLORS } from "@src/theme/colors";

const style = StyleSheet.create({
  container: {
    borderRadius: 16,
    borderBottomWidth: 6,
    backgroundColor: "yellow"
  },
  aspectTitle: {
    marginTop: windowHeight * 0.04,
    marginBottom: windowHeight * 0.026,
    textAlign: "center",
    color: "black"
  },
  svgAspect: {
    alignSelf: "center"
  },
  svgAspectContainer: {
    marginBottom: windowHeight * 0.038
  },
  detailAspectContainer: {
    marginHorizontal: 20,
    marginBottom: windowHeight * 0.019,
    paddingVertical: windowHeight * 0.017,
    paddingHorizontal: windowHeight * 0.021,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#FFFFFF",
    borderRadius: 14,
    borderWidth: 1,
    borderColor: COMMON_COLORS.GREY_10
  },
  label: {
    maxWidth: windowWidth * 0.4,
    color: COMMON_COLORS.BLUE_20,
    fontWeight: "800",
    fontSize: 14
  },
  labelContent: {
    color: "#003493",
    fontWeight: "800"
  },
  value: {
    fontSize: 14,
    fontFamily: typography.fontFamily.montserratBold
  },
  percentage: {
    fontSize: 14,
    fontFamily: typography.fontFamily.montserratRegular,
    color: COMMON_COLORS.BLUE_20
  }
});

export default style;
