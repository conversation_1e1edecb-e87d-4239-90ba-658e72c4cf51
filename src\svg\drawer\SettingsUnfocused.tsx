import {<PERSON>, G, Path, Rect, Svg} from 'react-native-svg';
import React from 'react';

export const SettingsUnfocused = (props: {style: {}}) => {
  return (
    <Svg
      style={props.style}
      id="Settings"
      width="24"
      height="24"
      viewBox="0 0 24 24">
      <G id="Settings-2" data-name="Settings">
        <Rect id="Rectangle" width="24" height="24" fill="none" />
        <Circle
          id="Oval"
          cx="3"
          cy="3"
          r="3"
          transform="translate(9 9)"
          fill="none"
          stroke="#66bcff"
          stroke-width="2"
        />
        <Path
          id="Path"
          d="M10.069,3.363a2.049,2.049,0,0,1,3.862,0,2.05,2.05,0,0,0,2.811,1.164,2.05,2.05,0,0,1,2.731,2.731,2.05,2.05,0,0,0,1.164,2.811,2.049,2.049,0,0,1,0,3.862,2.05,2.05,0,0,0-1.164,2.811,2.05,2.05,0,0,1-2.731,2.731,2.05,2.05,0,0,0-2.811,1.164,2.049,2.049,0,0,1-3.862,0,2.05,2.05,0,0,0-2.811-1.164,2.05,2.05,0,0,1-2.731-2.731,2.05,2.05,0,0,0-1.164-2.811,2.049,2.049,0,0,1,0-3.862A2.05,2.05,0,0,0,4.527,7.258,2.05,2.05,0,0,1,7.258,4.527,2.05,2.05,0,0,0,10.069,3.363Z"
          fill="none"
          stroke="#66bcff"
          stroke-width="2"
          fill-rule="evenodd"
        />
      </G>
    </Svg>
  );
};
