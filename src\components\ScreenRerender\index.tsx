import React, { useMemo } from 'react';
import { View, StyleSheet } from 'react-native';

type Screen = 'Home' | 'Profile' | 'Heavy';

interface ScreenRendererProps {
  currentScreen: Screen;
}

// This component prevents unnecessary re-renders of screens
// by memoizing the screen components and only re-rendering when the screen actually changes
const ScreenRenderer: React.FC<ScreenRendererProps> = React.memo(({ currentScreen }) => {
  // Cache screen components to prevent re-instantiation
  const screens = useMemo(() => ({
    Home: <HomeScreen />,
    Profile: <ProfileScreen />,
    Heavy: <HeavyScreen />,
  }), []);

  // Only re-render when currentScreen actually changes
  const currentScreenComponent = useMemo(() => {
    return screens[currentScreen] || screens.Home;
  }, [currentScreen, screens]);

  return (
    <View style={styles.container}>
      {currentScreenComponent}
    </View>
  );
});

ScreenRenderer.displayName = 'ScreenRenderer';

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default ScreenRenderer;
