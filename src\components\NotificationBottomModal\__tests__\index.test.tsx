import {renderWithProviders} from '@src/utils/utils-for-tests';
import React from 'react';
import {NotificationBottomModal} from '@src/components/NotificationBottomModal';

describe('NotificationBottomModal', function () {
  const handleMarkAsRead = jest.fn();
  it('Should render and match the snapshot', () => {
    const tree = renderWithProviders(
      <NotificationBottomModal
        notification={null}
        handleMarkAsRead={handleMarkAsRead}
      />,
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });

  it('Should render the modal when the notification is not null', () => {
    const {queryByText} = renderWithProviders(
      <NotificationBottomModal
        notification={{}}
        handleMarkAsRead={handleMarkAsRead}
      />,
    );
    const container = queryByText('Close');
    expect(container).toBeDefined();
  });

  it('Should render the modal when the notification is null', () => {
    const {queryByText} = renderWithProviders(
      <NotificationBottomModal
        notification={null}
        handleMarkAsRead={handleMarkAsRead}
      />,
    );
    // Check that the component returns null
    const contentElement = queryByText('No Internet Modal');
    expect(contentElement).toBeNull();
  });
});
