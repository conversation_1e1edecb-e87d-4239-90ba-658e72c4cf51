import React, { useCallback, useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AddComment } from "@src/components/AddComment";
import { ActivityIndicator, Alert, Keyboard, Text, TextInput, View } from "react-native";
import { Discussion } from "@src/models/discussion";
import { createUpdateDiscussion, getDiscussionDetailsByObjectTypeAndObjectId } from "@src/store/discussion/slice";
import { discussionSelector } from "@src/store/discussion/selectors";
import { MAPPING_NOTIFICATION_OBJECT_TYPE_WITH_DISCUSSION_OBJECT_TYPE } from "@src/constants/notification";
import { tagsRegex } from "@src/constants/reegexes";
import ListInteractions from "@src/containers/ListInteractions";
import { MESSAGES, TITLES } from "@src/constants/strings";
import { globalStyles } from "@src/theme/style";
import { authenticationSelector } from "@src/store/authentication/selectors";
import ToastMessage from "src/components/Toast";
import { styles } from "@src/containers/ListInteractions/style";

type ListInteractionsWithAddCommentInputProps = {
  discussion: Discussion;
  objectId: number;
  objectTypeMapped: string;
};

export const ListInteractionsWithAddCommentInput = (
  props: ListInteractionsWithAddCommentInputProps
) => {
  const { objectId, objectTypeMapped } = props;
  const { discussion, loading, errorUsers, errorCreateUpdate } = useSelector(discussionSelector);
  const { user } = useSelector(authenticationSelector);

  const dispatch = useDispatch();

  const textInputRef = useRef<TextInput>(null);
  const [focusedInteraction, setFocusedInteraction] = useState<number | null>(
    null
  );

  useEffect(() => {
    Keyboard.dismiss();
  });

  const getDiscussionDetails = useCallback(() => {
    dispatch(
      getDiscussionDetailsByObjectTypeAndObjectId({
        objectType:
          MAPPING_NOTIFICATION_OBJECT_TYPE_WITH_DISCUSSION_OBJECT_TYPE[
            objectTypeMapped
            ],
        objectId
      })
    );
  }, [dispatch, objectId, objectTypeMapped]);

  const handleUpdateListInteractions = (
    text: string,
    focusedInteractionId: number | null,
    type: string,
    projectId: number
  ) => {
    setFocusedInteraction(null);
    if (text.length === 0) {
      Alert.alert(
        TITLES.TEXT_ERROR_MODAL_INTERACTION,
        MESSAGES.TEXT_INTERACTION_NOT_EMPTY
      );
    } else {
      const mentions = text.match(tagsRegex);
      dispatch(
        createUpdateDiscussion({
          params: {
            type: discussion?.type!!,
            projectId,
            entityId: discussion?.objectId!!
          },
          payload: {
            discussionId: discussion?.objectId,
            comment: text,
            listMemberTags: mentions
          }
        })
      );
      textInputRef?.current?.clear();
      Keyboard.dismiss();
    }
  };

  const handleSettingFocusedInteraction = () => {
    setFocusedInteraction(null);
  };

  const handleClickInteraction = (interactionId: number) => {
    setFocusedInteraction(interactionId);
    textInputRef?.current?.clear();
  };

  const renderListInteractions = () => {
    if (discussion !== null && discussion.interactions.length > 0) {
      return <ListInteractions
        interactions={discussion.interactions}
        handleClickInteraction={handleClickInteraction}
        getDiscussionDetails={getDiscussionDetails}
      />;
    } else {
      return (
        <View style={styles.textNotFoundView}>
          <Text style={styles.textNotFound}>No Interactions found for this discussion</Text>
        </View>
      );
    }
  };

  if (discussion === null) {
    return null;
  } else {
    return (
      <>
        {renderListInteractions()}
        <AddComment
          textInputRef={textInputRef}
          handleUpdateListInteractions={handleUpdateListInteractions}
          focusedInteractionId={focusedInteraction ?? 0}
          handleSettingFocusedInteraction={handleSettingFocusedInteraction}
          userId={user?.azureDirectoryId ?? ""}
        />
        {loading && (
          <View style={globalStyles.loaderContainer}>
            <ActivityIndicator size="large" color="#0000ff" />
          </View>
        )}
        {(errorCreateUpdate || errorUsers) &&
          <ToastMessage message={"Error While Creating a new interaction"} type={"error"} />}
      </>
    );
  }
};
