{"name": "collabmobile", "version": "1.0.9", "private": true, "scripts": {"setRecette": "ENVFILE=.env.recette", "start:dev-debug": "react-native run-android --mode=devDebug --appIdSuffix=dev", "start:preprod-debug": "react-native run-android --mode=preprodDebug --appIdSuffix=preprod", "start:dev-release": "react-native run-android --mode=devRelease --appIdSuffix=dev", "start:recette-debug": "react-native run-android --mode=recetteDebug --appIdSuffix=recette", "ios:recette-ios": "npm run setRecette && react-native run-ios --mode=Debug ", "ios:recette-release-ios": "npm run setRecette && react-native run-ios --mode=Release", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "build-android": "cd android && ./gradlew clean && cd .. && npx react-native run-android", "sonar": "node sonar-project.js", "tests-with-reporters": "jest", "postinstall": "node fix-metro-config.js"}, "dependencies": {"@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.22.11", "@microsoft/microsoft-graph-client": "^3.0.5", "@motify/components": "^0.18.0", "@react-native-async-storage/async-storage": "^1.17.11", "@react-native-community/blur": "4.4.1", "@react-native-community/cli": "^11.3.6", "@react-native-community/datetimepicker": "^7.1.0", "@react-native-community/masked-view": "^0.1.11", "@react-native-community/netinfo": "^9.3.9", "@react-native-firebase/analytics": "^18.1.0", "@react-native-firebase/app": "^18.1.0", "@react-native-firebase/crashlytics": "^18.1.0", "@react-native-firebase/dynamic-links": "^18.1.0", "@react-native-firebase/messaging": "^18.1.0", "@react-native-picker/picker": "^2.4.10", "@react-navigation/drawer": "^6.6.2", "@react-navigation/native": "^6.1.6", "@react-navigation/stack": "^6.3.16", "@reduxjs/toolkit": "^1.9.5", "@shopify/flash-list": "^1.6.4", "@testing-library/react-native": "^12.1.2", "@types/node": "^20.4.1", "axios": "^1.4.0", "base64-js": "^1.5.1", "class-transformer": "^0.5.1", "date-fns": "^2.30.0", "immer": "^10.0.2", "install": "^0.13.0", "jest-junit": "^16.0.0", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "moti": "^0.25.3", "msal": "^1.4.18", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "react": "18.2.0", "react-devtools": "^4.27.7", "react-native": "0.71.7", "react-native-app-auth": "^6.4.3", "react-native-blob-util": "^0.18.3", "react-native-bootsplash": "^4.7.5", "react-native-config": "^1.5.1", "react-native-controlled-mentions": "^2.2.4", "react-native-device-info": "^10.6.0", "react-native-dotenv": "^3.4.8", "react-native-encrypted-storage": "^4.0.3", "react-native-exit-app": "^2.0.0", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "^2.10.0", "react-native-linear-gradient": "^2.6.2", "react-native-logs": "^5.0.1", "react-native-mentions": "^1.1.4", "react-native-modal": "^13.0.1", "react-native-picker-select": "^8.0.4", "react-native-reanimated": "^2.17.0", "react-native-responsive-screen": "^1.4.2", "react-native-restart": "^0.0.27", "react-native-safe-area-context": "^4.5.1", "react-native-select-dropdown": "^3.4.0", "react-native-splash-screen": "^3.3.0", "react-native-svg-transformer": "^1.0.0", "react-native-swipe-list-view": "^3.2.9", "react-native-toast-message": "^2.2.0", "react-native-version-info": "^1.1.1", "react-native-wheel-pick": "^1.2.2", "react-native-wheely": "^0.6.0", "react-redux": "^8.0.5", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "redux-saga": "^1.2.3", "redux-saga-test-plan": "^4.0.6", "reflect-metadata": "^0.1.13", "replace-in-file": "^7.0.2", "sonarqube-scanner": "^3.1.0"}, "devDependencies": {"@babel/core": "^7.22.9", "@babel/plugin-proposal-decorators": "^7.21.0", "@babel/preset-env": "^7.22.14", "@babel/preset-typescript": "^7.22.5", "@babel/runtime": "^7.20.0", "@jest/globals": "^29.6.2", "@notifee/react-native": "^5.7.0", "@react-native-community/eslint-config": "^3.2.0", "@tsconfig/react-native": "^2.0.2", "@types/jest": "^29.2.1", "@types/react": "^18.0.24", "@types/react-native-wheel-pick": "^1.1.2", "@types/react-test-renderer": "^18.0.0", "@types/styled-components": "^5.1.26", "babel-jest": "^29.6.4", "babel-plugin-module-resolver": "^5.0.0", "babel-plugin-root-import": "^6.6.0", "babel-preset-react-native": "^4.0.1", "eslint": "^8.19.0", "jest": "^29.7.0", "jest-react-native": "^18.0.0", "metro-react-native-babel-preset": "^0.73.9", "path": "^0.12.7", "prettier": "2.8.8", "react-native-svg": "^13.13.0", "react-native-svg-path-gradient": "^0.4.0", "react-test-renderer": "18.2.0", "ts-jest": "^29.1.1", "typescript": "4.8.4"}, "reactNativePermissionsIOS": ["AppTrackingTransparency", "BluetoothPeripheral", "Calendars", "Camera", "Contacts", "FaceID", "LocationAccuracy", "LocationAlways", "LocationWhenInUse", "MediaLibrary", "Microphone", "Motion", "Notifications", "PhotoLibrary", "PhotoLibraryAddOnly", "Reminders", "<PERSON><PERSON>", "SpeechRecognition", "StoreKit"]}