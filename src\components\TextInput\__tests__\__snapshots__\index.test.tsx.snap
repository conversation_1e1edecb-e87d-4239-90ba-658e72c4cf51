// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`InputText Should render and match the snapshot 1`] = `
<View
  onLayout={[Function]}
  style={{}}
>
  <Text
    style={
      {
        "color": "gray",
        "fontSize": 12,
        "fontWeight": "bold",
        "marginBottom": 8,
      }
    }
  />
  <TextInput
    numberOfLines={1}
    onChangeText={[MockFunction]}
    placeholder=""
    placeholderTextColor="#BFC3CB"
    style={
      {
        "borderColor": "#5F6A7E3D",
        "borderRadius": 14,
        "borderWidth": 1,
        "color": "black",
        "marginBottom": 8,
        "paddingHorizontal": 18,
        "paddingVertical": 8,
      }
    }
  />
</View>
`;
