// useInternetConnectivity.js

import {useEffect, useState} from 'react';
import NetInfo from '@react-native-community/netinfo';

const useInternetConnectivity = () => {
  const [isConnected, setIsConnected] = useState(true);

  const checkInternetConnectivity = () => {
    NetInfo.fetch().then(state => {
      const connected = state.isConnected && state.isInternetReachable;
      if (connected !== null) {
        setIsConnected(connected);
      }
    });
  };

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      const connected = state.isConnected && state.isInternetReachable;
      if (connected !== null) {
        setIsConnected(connected);
      }
    });

    return () => {
      unsubscribe();
    };
  }, []);

  return {checkInternetConnectivity, isConnected};
};

export default useInternetConnectivity;
