import {call} from 'redux-saga/effects';
import {expectSaga} from 'redux-saga-test-plan';

import {
  setFcmTokenWithError,
  setFcmTokenWithSuccess,
} from '@src/store/authentication/slice';
import {handleFcmTokenSaga} from '@src/store/authentication/saga';
import {AuthenticationWebServices} from '@src/api/login';
import baseRequest from '@src/api/request';

describe('handleAuthenticationSagas', () => {
  it('should yield and api call while calling the update firebase token', () => {
    expectSaga(handleFcmTokenSaga, {
      payload: {fireBaseToken: 'firebaseToken'},
      type: 'type',
    })
      .provide([
        [
          call(
            baseRequest.post,
            AuthenticationWebServices.UPDATE_FIREBASE_TOKEN,
            {},
            {
              params: {
                firebaseToken: 'firebaseToken',
              },
            },
          ),
          {},
        ],
      ])
      .run();
  });

  it('should handle setting fcm token successfully', () => {
    const mockData = {fireBaseToken: 'firebaseToken'};

    expectSaga(handleFcmTokenSaga, {
      payload: {fireBaseToken: 'firebaseToken'},
      type: 'type',
    })
      .provide([
        [
          call(
            baseRequest.post,
            AuthenticationWebServices.UPDATE_FIREBASE_TOKEN,
            {},
            {
              params: {
                firebaseToken: 'firebaseToken',
              },
            },
          ),
          {},
        ],
      ])
      .put(setFcmTokenWithSuccess(mockData))
      .run();
  });

  it('should handle setting fcm token with error', () => {
    expectSaga(handleFcmTokenSaga, {
      payload: {fireBaseToken: 'firebaseToken'},
      type: 'type',
    })
      .provide([
        [
          call(
            baseRequest.post,
            AuthenticationWebServices.UPDATE_FIREBASE_TOKEN,
          ),
          {throwError: true},
        ],
      ])
      .put(setFcmTokenWithError())
      .run();
  });
});
