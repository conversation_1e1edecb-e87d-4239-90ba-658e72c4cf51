import reducer, {
  FilterParams,
  getProjects,
  getProjectsWithError,
  getProjectsWithSuccess,
  initialState,
  resetFilterItem,
  setFilterParams,
  updateProjects,
  updateProjectsWithError,
  updateProjectsWithSuccess,
} from '@src/store/projects/slice';

describe('Projects reducer', () => {
  it('should return the initial state', () => {
    expect(reducer(undefined, {type: undefined})).toEqual(initialState);
  });

  describe('get Projects', () => {
    it('should handle getProjects action', () => {
      const action = getProjects({});
      const newState = reducer(initialState, action);
      expect(newState).toEqual({
        ...initialState,
        loading: true,
        success: false,
        error: false,
      });
    });

    it('should handle getProjectsWithSuccess action', () => {
      const action = getProjectsWithSuccess({
        content: [],
      });
      const newState = reducer(initialState, action);

      expect(newState).toEqual({
        ...initialState,
        ...action.payload,
        projects: action.payload.content,
        loading: false,
        success: true,
        error: false,
      });
    });

    it('should handle getProjectsWithError action', () => {
      const action = getProjectsWithError();
      const newState = reducer(initialState, action);

      expect(newState).toEqual({
        ...initialState,
        projects: [],
        loading: false,
        success: false,
        error: true,
      });
    });
  });

  describe('updateProjects', () => {
    it('should handle updateProjects action', () => {
      const action = updateProjects({});
      const newState = reducer(initialState, action);
      expect(newState).toEqual({
        ...initialState,
        loading: true,
        success: false,
        error: false,
      });
    });

    it('should handle updateProjectsWithSuccess action', () => {
      const action = updateProjectsWithSuccess({
        data: {
          content: [],
        },
      });
      const newState = reducer(initialState, action);

      let projects = [...newState.projects, ...action.payload.data.content];

      expect(newState).toEqual({
        ...initialState,
        ...action.payload.data,
        projects,
        loading: false,
        success: true,
        error: false,
      });
    });

    it('should handle updateProjectsWithError action', () => {
      const action = updateProjectsWithError();
      const newState = reducer(initialState, action);

      expect(newState).toEqual({
        ...initialState,
        projects: [],
        loading: false,
        success: false,
        error: true,
      });
    });
  });

  it('should handle setFilterParams action', () => {
    const payload: FilterParams = {
      bu: undefined,
      page: 0,
      platformCode: '',
      program: undefined,
      projectName: undefined,
      projectNumber: undefined,
      sector: undefined,
      size: 0,
    };
    const action = setFilterParams(payload);
    const newState = reducer(initialState, action);
    expect(newState).toEqual({
      ...initialState,
      filterParams: action.payload,
    });
  });

  it('should handle resetFilterItem action', () => {
    const payload: FilterParams = {
      bu: undefined,
      page: 0,
      platformCode: '',
      program: undefined,
      projectName: undefined,
      projectNumber: undefined,
      sector: undefined,
      size: 0,
    };
    const action = resetFilterItem(payload);
    const newState = reducer(initialState, action);
    expect(newState).toEqual({
      ...initialState,
      filterParams: action.payload,
    });
  });
});
