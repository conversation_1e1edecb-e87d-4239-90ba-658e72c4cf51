import React from "react";
import { renderWithProviders } from "@src/utils/utils-for-tests";
import DiscussionKPIBody from "@src/components/DiscussionSummaryBody/DiscussionKPIBody";

describe("DiscussionKPIBody", () => {
  it("Should render and match the snapshot", () => {
    const initialState = {
      discussion: {
        discussion: null
      }
    };
    const tree = renderWithProviders(<DiscussionKPIBody />, {
      preloadedState: initialState
    }).toJSON();
    expect(tree).toMatchSnapshot();
  });

  it("should render null when discussion is null", () => {
    const initialState = {
      discussion: {
        discussion: null
      }
    };
    const { queryByText } = renderWithProviders(<DiscussionKPIBody />, {
      preloadedState: initialState
    });

    // Check that the component returns null
    const contentElement = queryByText("Discussion content");
    expect(contentElement).toBeNull();
  });

  it("should render null when discussion is not null", () => {
    const initialState = {
      discussion: {
        discussion: {
          periodOfData: {
            month: 2,
            year: 2022
          },
          description: "Description"
        }
      }
    };
    const tree = renderWithProviders(<DiscussionKPIBody />, {
      preloadedState: initialState
    });

    expect(tree).toBeTruthy();
  });
});
