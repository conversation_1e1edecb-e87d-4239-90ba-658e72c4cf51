import {Defs, G, LinearGradient, Path, Rect, Stop, Svg} from 'react-native-svg';
import React from 'react';

export const MiniActionIcon = (props: {style: {}}) => {
  return (
    <Svg style={props.style} width="24" height="24" viewBox="0 0 24 24">
      <Defs>
        <LinearGradient
          id="gradient"
          x1="0.158"
          y1="0.148"
          x2="0.886"
          y2="0.783">
          <Stop offset="0" stopColor="#007cff" />
          <Stop offset="1" stopColor="#0af" />
        </LinearGradient>
      </Defs>
      <G
        id="Groupe_61247"
        data-name="Groupe 61247"
        transform="translate(-24 -135)">
        <Rect
          id="Rectangle_939"
          data-name="Rectangle 939"
          width="24"
          height="24"
          rx="6"
          transform="translate(24 135)"
          fill="url(#gradient)"
        />
        <G id="graph-svgrepo-com" transform="translate(27.317 138.317)">
          <Path
            id="Tracé_52403"
            data-name="Tracé 52403"
            d="M13.907,9.039A.668.668,0,0,0,12.795,8.3L10.94,11.08,8.708,9.573a.869.869,0,0,0-1.245.3L6.084,12.353A.668.668,0,1,0,7.252,13l1.131-2.036,2.2,1.484a.869.869,0,0,0,1.209-.238Z"
            transform="translate(-1.327 -1.99)"
            fill="#fff"
          />
          <Path
            id="Tracé_52404"
            data-name="Tracé 52404"
            d="M5.341,2A3.341,3.341,0,0,0,2,5.341v6.683a3.341,3.341,0,0,0,3.341,3.341h6.683a3.341,3.341,0,0,0,3.341-3.341V5.341A3.341,3.341,0,0,0,12.024,2Zm-2,3.341a2,2,0,0,1,2-2h6.683a2,2,0,0,1,2,2v6.683a2,2,0,0,1-2,2H5.341a2,2,0,0,1-2-2Z"
            fill="#fff"
            fill-rule="evenodd"
          />
        </G>
      </G>
    </Svg>
  );
};
