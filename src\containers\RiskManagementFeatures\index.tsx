import { View } from "react-native";
import { ObjectUtils } from "@src/utils/objectUtils";
import React from "react";
import {
  COLLAB_FEATURES,
  COLLAB_FEATURES_TITLES,
  COLLAB_SUMMARY_FEATURE_KEY_WITH_FEATURES_CODE,
  getSectionValueByKey
} from "@src/constants/channels/features";
import { useSelector } from "react-redux";
import { riskSelector } from "@src/store/channels/risk/selectors";
import { KEYS_CHANNEL } from "@src/constants/channels/modules";
import Loader from "@src/components/Loader";
import { ChannelFeaturesDefaultValues } from "@src/constants/channelFeaturesDefaultValues";
import FeatureContainer, { renderFeatureContainers } from "@src/containers/FeatureContainer";

const RiskManagementFeatures = () => {
  const { features, loading } = useSelector(riskSelector);

  function renderDefaultValues() {
    const riskFlowFeatures: FeatureContainer[] = [
      {
        featureKey: COLLAB_FEATURES.RISK.RISK_BOARD,
        featureTitle: COLLAB_FEATURES_TITLES.RISK.criticalRisks,
        defaultFeatureValue: ChannelFeaturesDefaultValues.RISK.criticalRisks
      },
      {
        featureKey: COLLAB_FEATURES.RISK.RISK_MATRIX_EVOL,
        featureTitle: COLLAB_FEATURES_TITLES.RISK.evolution,
        defaultFeatureValue: ChannelFeaturesDefaultValues.RISK.EVOLUTION
      }
    ];
    return renderFeatureContainers(riskFlowFeatures);
  }

  if (loading) {
    return <Loader show={true} />;
  }

  return (
    <View>
      {features
        ? ObjectUtils.convertObjectToKeyValuesArray(features).map((item) => {
          if (item.key !== KEYS_CHANNEL.COMMON.CHANNEL_STATUS &&
            item.key !== KEYS_CHANNEL.PROGRESS.CLOSED_SOR) {
            return (
              <FeatureContainer
                key={item.key}
                featureKey={
                  getSectionValueByKey(COLLAB_SUMMARY_FEATURE_KEY_WITH_FEATURES_CODE, "RISK", item.key)
                }
                featureTitle={
                  getSectionValueByKey(COLLAB_FEATURES_TITLES, "RISK", item.key)
                }
                featureValue={item.value}
                defaultFeatureValue={
                  getSectionValueByKey(ChannelFeaturesDefaultValues, "RISK", item.key)
                }
              />
            );
          }
          return null;
        })
        : renderDefaultValues()}
    </View>
  );
};

export default RiskManagementFeatures;
