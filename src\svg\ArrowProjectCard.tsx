import {Path, Svg} from 'react-native-svg';
import React from 'react';
import {svgChannelIconHeight, svgChannelIconWidth} from '@src/theme/style';

export const ArrowProjectCard = () => {
  return (
    <Svg
      id="trash"
      width={svgChannelIconWidth(9.161)}
      height={svgChannelIconHeight(16.801)}
      viewBox="0 0 9.161 16.801">
      <Path
        id="Arrow"
        d="M332.335,140.75a.628.628,0,0,0,0,.878l6.4,6.569-6.4,6.568a.628.628,0,0,0,0,.878.6.6,0,0,0,.857,0l6.81-6.987a.657.657,0,0,0,0-.917l-6.81-6.987a.6.6,0,0,0-.857,0Z"
        transform="translate(-331.593 -139.522)"
        fill="#003493"
        stroke="#003493"
        strokeWidth="1"
      />
    </Svg>
  );
};
