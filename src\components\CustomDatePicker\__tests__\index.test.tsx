import CustomDatePicker from '@src/components/CustomDatePicker';
import React from 'react';
import {renderWithProviders} from '@src/utils/utils-for-tests';

describe('Date Picker', () => {
  const handleUpdateDateFilter = jest.fn();
  const years = ['Jan', 'Feb', 'Mar', 'Apr', 'May'];
  const months = ['Jan', 'Feb', 'Mar'];
  const selectedYear = '2022';
  const selectedMonth = '12';
  it('should render and match the snapshot', () => {
    const tree = renderWithProviders(
      <CustomDatePicker
        handleUpdateDateFilter={handleUpdateDateFilter}
        years={years}
        months={months}
        selectedYear={selectedYear}
        selectedMonth={selectedMonth}
      />,
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
