<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="550" failures="0" errors="0" time="33.256">
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:35" time="5.945" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.012">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.005">
    </testcase>
    <testcase classname="Project Card Should render and match the snapshot" name="Project Card Should render and match the snapshot" time="0.733">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:35" time="6.079" tests="3">
    <testcase classname="<PERSON>th<PERSON>ana<PERSON> should sign in and return the access token" name="<PERSON>th<PERSON>ana<PERSON> should sign in and return the access token" time="0.037">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.005">
    </testcase>
    <testcase classname="List Projects should render  and match the snapshot" name="List Projects should render  and match the snapshot" time="0.742">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:35" time="6.123" tests="4">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.01">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.011">
    </testcase>
    <testcase classname="List Interactions should render when interactions are not null and match the snapshot" name="List Interactions should render when interactions are not null and match the snapshot" time="0.676">
    </testcase>
    <testcase classname="List Interactions should render when interactions are  null and match the snapshot" name="List Interactions should render when interactions are  null and match the snapshot" time="0.166">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:34" time="6.267" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.009">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.005">
    </testcase>
    <testcase classname="List Notifications With Header And Overview Container should render  and match the snapshot" name="List Notifications With Header And Overview Container should render  and match the snapshot" time="0.994">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:34" time="6.335" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.011">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.006">
    </testcase>
    <testcase classname="AddComment Should render and match the snapshot" name="AddComment Should render and match the snapshot" time="0.677">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:34" time="6.8" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.009">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.005">
    </testcase>
    <testcase classname="List Discussion Interactions With Header Container should render and match the snapshot" name="List Discussion Interactions With Header Container should render and match the snapshot" time="0.703">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:34" time="7.118" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.012">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.006">
    </testcase>
    <testcase classname="Project Details With Header And Date Modal Container should render  and match the snapshot" name="Project Details With Header And Date Modal Container should render  and match the snapshot" time="0.89">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:41" time="1.452" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.004">
    </testcase>
    <testcase classname="Home Header should render and match the snapshot" name="Home Header should render and match the snapshot" time="0.138">
    </testcase>
  </testsuite>
  <testsuite name="Login Screen Tests" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:41" time="1.485" tests="1">
    <testcase classname="Login Screen Tests Should render and match the snapshot" name="Login Screen Tests Should render and match the snapshot" time="0.182">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:41" time="1.612" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.005">
    </testcase>
    <testcase classname="Project Details Header should render and match the snapshot" name="Project Details Header should render and match the snapshot" time="0.176">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:41" time="1.624" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.01">
    </testcase>
    <testcase classname="VersionNotUpdate Should render and match the snapshot" name="VersionNotUpdate Should render and match the snapshot" time="0.401">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:41" time="1.953" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.004">
    </testcase>
    <testcase classname="List Interactions With Add Comment Input should render when interactions are not null and match the snapshot" name="List Interactions With Add Comment Input should render when interactions are not null and match the snapshot" time="0.087">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:41" time="1.448" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.009">
    </testcase>
    <testcase classname="RiskManagementFeatures should render and match the snapshot" name="RiskManagementFeatures should render and match the snapshot" time="0.111">
    </testcase>
    <testcase classname="RiskManagementFeatures should render null when features are null" name="RiskManagementFeatures should render null when features are null" time="0.206">
    </testcase>
    <testcase classname="RiskManagementFeatures should render null when features are not null" name="RiskManagementFeatures should render null when features are not null" time="0.013">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:42" time="0.907" tests="8">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.004">
    </testcase>
    <testcase classname="projectsSagas handleGetProjects should yield an api call" name="projectsSagas handleGetProjects should yield an api call" time="0.219">
    </testcase>
    <testcase classname="projectsSagas handleGetProjects should handle getProjects successfully" name="projectsSagas handleGetProjects should handle getProjects successfully" time="0.006">
    </testcase>
    <testcase classname="projectsSagas handleGetProjects should handle getProjects with error" name="projectsSagas handleGetProjects should handle getProjects with error" time="0.003">
    </testcase>
    <testcase classname="projectsSagas handleUpdateProjects should yield an api call" name="projectsSagas handleUpdateProjects should yield an api call" time="0.003">
    </testcase>
    <testcase classname="projectsSagas handleUpdateProjects should handle updateProjects successfully" name="projectsSagas handleUpdateProjects should handle updateProjects successfully" time="0.003">
    </testcase>
    <testcase classname="projectsSagas handleUpdateProjects should handle updateProjects with error" name="projectsSagas handleUpdateProjects should handle updateProjects with error" time="0.003">
    </testcase>
  </testsuite>
  <testsuite name="Project reducer" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:43" time="0.685" tests="13">
    <testcase classname="Project reducer should return the initial state" name="Project reducer should return the initial state" time="0.008">
    </testcase>
    <testcase classname="Project reducer should handle getUserProjectWithProjectIdAndUserId should handle getUserProjectWithProjectIdAndUserId action" name="Project reducer should handle getUserProjectWithProjectIdAndUserId should handle getUserProjectWithProjectIdAndUserId action" time="0.002">
    </testcase>
    <testcase classname="Project reducer should handle getUserProjectWithProjectIdAndUserId should handle getUserProjectWithProjectIdAndUserIdWithSuccess action" name="Project reducer should handle getUserProjectWithProjectIdAndUserId should handle getUserProjectWithProjectIdAndUserIdWithSuccess action" time="0.002">
    </testcase>
    <testcase classname="Project reducer should handle getUserProjectWithProjectIdAndUserId should handle getUserProjectWithProjectIdAndUserIdWithError action" name="Project reducer should handle getUserProjectWithProjectIdAndUserId should handle getUserProjectWithProjectIdAndUserIdWithError action" time="0.001">
    </testcase>
    <testcase classname="Project reducer should handle getFlattenKpis should handle getFlattenKpis action" name="Project reducer should handle getFlattenKpis should handle getFlattenKpis action" time="0.001">
    </testcase>
    <testcase classname="Project reducer should handle getFlattenKpis should handle getFlattenKpisWithSuccess action" name="Project reducer should handle getFlattenKpis should handle getFlattenKpisWithSuccess action" time="0.001">
    </testcase>
    <testcase classname="Project reducer should handle getFlattenKpis should handle getFlattenKpisWithError action" name="Project reducer should handle getFlattenKpis should handle getFlattenKpisWithError action" time="0.001">
    </testcase>
    <testcase classname="Project reducer should handle setCurrentProject should handle setCurrentProject action" name="Project reducer should handle setCurrentProject should handle setCurrentProject action" time="0.001">
    </testcase>
    <testcase classname="Project reducer should handle setCurrentProject should handle setCurrentProjectWithSuccess action" name="Project reducer should handle setCurrentProject should handle setCurrentProjectWithSuccess action" time="0.022">
    </testcase>
    <testcase classname="Project reducer should handle setCurrentProject should handle setCurrentProjectWithError action" name="Project reducer should handle setCurrentProject should handle setCurrentProjectWithError action" time="0.001">
    </testcase>
    <testcase classname="Project reducer should handle setCurrentProjectId action" name="Project reducer should handle setCurrentProjectId action" time="0.007">
    </testcase>
    <testcase classname="Project reducer should handle setReportingDate action" name="Project reducer should handle setReportingDate action" time="0.001">
    </testcase>
    <testcase classname="Project reducer should handle cleanUp action" name="Project reducer should handle cleanUp action" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:42" time="1.538" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.135">
    </testcase>
    <testcase classname="NotAuthenticatedModal Should render and match the snapshot" name="NotAuthenticatedModal Should render and match the snapshot" time="0.451">
    </testcase>
    <testcase classname="NotAuthenticatedModal Should render the modal when the show is true" name="NotAuthenticatedModal Should render the modal when the show is true" time="0.009">
    </testcase>
    <testcase classname="NotAuthenticatedModal Should render the modal when the show is false" name="NotAuthenticatedModal Should render the modal when the show is false" time="0.005">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:43" time="0.803" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.006">
    </testcase>
    <testcase classname="humanCap saga should yield an api call" name="humanCap saga should yield an api call" time="0.007">
    </testcase>
    <testcase classname="humanCap saga should handle human cap features successfully" name="humanCap saga should handle human cap features successfully" time="0.007">
    </testcase>
    <testcase classname="humanCap saga should handle human cap features with error" name="humanCap saga should handle human cap features with error" time="0.002">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:43" time="0.841" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.004">
    </testcase>
    <testcase classname="qa saga should yield an api call" name="qa saga should yield an api call" time="0.006">
    </testcase>
    <testcase classname="qa saga should handle qa features successfully" name="qa saga should handle qa features successfully" time="0.005">
    </testcase>
    <testcase classname="qa saga should handle qa features with error" name="qa saga should handle qa features with error" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:43" time="0.901" tests="11">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.005">
    </testcase>
    <testcase classname="projectSagas setCurrentProject should yield an api call" name="projectSagas setCurrentProject should yield an api call" time="0.008">
    </testcase>
    <testcase classname="projectSagas setCurrentProject should handle setCurrentProjectWithSuccess successfully" name="projectSagas setCurrentProject should handle setCurrentProjectWithSuccess successfully" time="0.004">
    </testcase>
    <testcase classname="projectSagas setCurrentProject should handle setCurrentProjectWithError sagas " name="projectSagas setCurrentProject should handle setCurrentProjectWithError sagas " time="0.002">
    </testcase>
    <testcase classname="projectSagas get getFlattenKpis should yield an api call" name="projectSagas get getFlattenKpis should yield an api call" time="0.001">
    </testcase>
    <testcase classname="projectSagas get getFlattenKpis should handle getFlattenKpisWithSuccess successfully" name="projectSagas get getFlattenKpis should handle getFlattenKpisWithSuccess successfully" time="0.002">
    </testcase>
    <testcase classname="projectSagas get getFlattenKpis should handle getFlattenKpisWithError successfully" name="projectSagas get getFlattenKpis should handle getFlattenKpisWithError successfully" time="0.001">
    </testcase>
    <testcase classname="projectSagas handleUserProject should yield an api call" name="projectSagas handleUserProject should yield an api call" time="0.002">
    </testcase>
    <testcase classname="projectSagas handleUserProject should handle getUserProjectWithProjectIdAndUserIdWithSuccess successfully" name="projectSagas handleUserProject should handle getUserProjectWithProjectIdAndUserIdWithSuccess successfully" time="0.002">
    </testcase>
    <testcase classname="projectSagas handleUserProject should handle getUserProjectWithProjectIdAndUserIdWithError successfully" name="projectSagas handleUserProject should handle getUserProjectWithProjectIdAndUserIdWithError successfully" time="0.002">
    </testcase>
  </testsuite>
  <testsuite name="projectSelector" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:43" time="0.529" tests="1">
    <testcase classname="projectSelector should select the project state from the root state" name="projectSelector should select the project state from the root state" time="0.004">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:43" time="0.703" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.005">
    </testcase>
    <testcase classname="handleAuthenticationSagas should yield an api call" name="handleAuthenticationSagas should yield an api call" time="0.013">
    </testcase>
    <testcase classname="handleAuthenticationSagas should handle authentication sagas successfully" name="handleAuthenticationSagas should handle authentication sagas successfully" time="0.002">
    </testcase>
    <testcase classname="handleAuthenticationSagas should handle authentication sagas with error" name="handleAuthenticationSagas should handle authentication sagas with error" time="0.005">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:43" time="1.428" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.004">
    </testcase>
    <testcase classname="DiscussionSummaryHeader Should render and match the snapshot" name="DiscussionSummaryHeader Should render and match the snapshot" time="0.121">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:43" time="2.349" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.006">
    </testcase>
    <testcase classname="List Projects With Header  Container should render and match the snapshot" name="List Projects With Header  Container should render and match the snapshot" time="0.521">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:45" time="1.378" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.013">
    </testcase>
    <testcase classname="DiscussionSummary Should render and match the snapshot" name="DiscussionSummary Should render and match the snapshot" time="0.14">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:45" time="1.613" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.138">
    </testcase>
    <testcase classname="NoInternetModal Should render and match the snapshot" name="NoInternetModal Should render and match the snapshot" time="0.302">
    </testcase>
    <testcase classname="NoInternetModal Should render the modal when the show is true" name="NoInternetModal Should render the modal when the show is true" time="0.033">
    </testcase>
    <testcase classname="NoInternetModal Should render the modal when the show is false" name="NoInternetModal Should render the modal when the show is false" time="0.009">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:46" time="1.418" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.018">
    </testcase>
    <testcase classname="Notification Should render and match the snapshot" name="Notification Should render and match the snapshot" time="0.14">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:47" time="1.586" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.006">
    </testcase>
    <testcase classname="Drawer Should render and match the snapshot" name="Drawer Should render and match the snapshot" time="0.381">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:45" time="4.43" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.01">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.005">
    </testcase>
    <testcase classname="List Notifications should render  and match the snapshot" name="List Notifications should render  and match the snapshot" time="0.241">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:48" time="1.421" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.009">
    </testcase>
    <testcase classname="ErrorModal Should render and match the snapshot" name="ErrorModal Should render and match the snapshot" time="0.277">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:45" time="4.872" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.009">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.004">
    </testcase>
    <testcase classname="BottomCarousel Should Render and match the snapshot" name="BottomCarousel Should Render and match the snapshot" time="0.773">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:45" time="5.068" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.01">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.008">
    </testcase>
    <testcase classname="Main channel carousel and bottom carousel should render  and match the snapshot" name="Main channel carousel and bottom carousel should render  and match the snapshot" time="0.75">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:49" time="1.271" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.01">
    </testcase>
    <testcase classname="CostFeatures should render and match the snapshot" name="CostFeatures should render and match the snapshot" time="0.097">
    </testcase>
    <testcase classname="CostFeatures should render null when features are null" name="CostFeatures should render null when features are null" time="0.201">
    </testcase>
    <testcase classname="CostFeatures should render null when features are not null" name="CostFeatures should render null when features are not null" time="0.004">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:45" time="4.865" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.009">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.005">
    </testcase>
    <testcase classname="CarouselChannels Should render and match the snapshot" name="CarouselChannels Should render and match the snapshot" time="0.671">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:45" time="4.853" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.011">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.004">
    </testcase>
    <testcase classname="Interaction Should render and match the snapshot" name="Interaction Should render and match the snapshot" time="0.411">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:49" time="1.385" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.008">
    </testcase>
    <testcase classname="FilterProjects should render and match the snapshot" name="FilterProjects should render and match the snapshot" time="0.229">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:50" time="1.259" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.009">
    </testcase>
    <testcase classname="SchedulingFeatures should render and match the snapshot" name="SchedulingFeatures should render and match the snapshot" time="0.087">
    </testcase>
    <testcase classname="SchedulingFeatures should render null when features are null" name="SchedulingFeatures should render null when features are null" time="0.024">
    </testcase>
    <testcase classname="SchedulingFeatures should render null when features are not null" name="SchedulingFeatures should render null when features are not null" time="0.005">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:49" time="1.874" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.005">
    </testcase>
    <testcase classname="DSO Should render and match the snapshot" name="DSO Should render and match the snapshot" time="0.664">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:50" time="1.132" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.004">
    </testcase>
    <testcase classname="DeleteNotificationModal Should render and match the snapshot" name="DeleteNotificationModal Should render and match the snapshot" time="0.284">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:50" time="1.203" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.011">
    </testcase>
    <testcase classname="HseFeatures should render and match the snapshot" name="HseFeatures should render and match the snapshot" time="0.097">
    </testcase>
    <testcase classname="HseFeatures should render null when features are null" name="HseFeatures should render null when features are null" time="0.228">
    </testcase>
    <testcase classname="HseFeatures should render null when features are not null" name="HseFeatures should render null when features are not null" time="0.005">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:50" time="1.387" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.005">
    </testcase>
    <testcase classname="Loader Should render and match the snapshot" name="Loader Should render and match the snapshot" time="0.418">
    </testcase>
    <testcase classname="Loader Should render the modal when the show is true" name="Loader Should render the modal when the show is true" time="0.012">
    </testcase>
    <testcase classname="Loader Should render the modal when the show is false" name="Loader Should render the modal when the show is false" time="0.005">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:50" time="1.448" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.008">
    </testcase>
    <testcase classname="ChangeManagementFeatures should render and match the snapshot" name="ChangeManagementFeatures should render and match the snapshot" time="0.11">
    </testcase>
    <testcase classname="ChangeManagementFeatures should render null when features are null" name="ChangeManagementFeatures should render null when features are null" time="0.317">
    </testcase>
    <testcase classname="ChangeManagementFeatures should render null when features are not null" name="ChangeManagementFeatures should render null when features are not null" time="0.006">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:51" time="1.141" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.005">
    </testcase>
    <testcase classname="HumanCapFeatures should render and match the snapshot" name="HumanCapFeatures should render and match the snapshot" time="0.097">
    </testcase>
    <testcase classname="HumanCapFeatures should render null when features are null" name="HumanCapFeatures should render null when features are null" time="0.184">
    </testcase>
    <testcase classname="HumanCapFeatures should render null when features are not null" name="HumanCapFeatures should render null when features are not null" time="0.006">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:51" time="1.113" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.009">
    </testcase>
    <testcase classname="Project Date Bottom Modal Container should render  and match the snapshot" name="Project Date Bottom Modal Container should render  and match the snapshot" time="0.235">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:51" time="1.308" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.006">
    </testcase>
    <testcase classname="SuggestionItem Should render and match the snapshot" name="SuggestionItem Should render and match the snapshot" time="0.125">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:51" time="1.116" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.005">
    </testcase>
    <testcase classname="Testing the custom filter bottom sheet modal Should render and match the snapshot" name="Testing the custom filter bottom sheet modal Should render and match the snapshot" time="0.28">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:51" time="1.349" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.004">
    </testcase>
    <testcase classname="Notifications Header should render and match the snapshot" name="Notifications Header should render and match the snapshot" time="0.133">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:52" time="1.107" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.007">
    </testcase>
    <testcase classname="CashFlowFeatures should render and match the snapshot" name="CashFlowFeatures should render and match the snapshot" time="0.098">
    </testcase>
    <testcase classname="CashFlowFeatures should render null when features are null" name="CashFlowFeatures should render null when features are null" time="0.005">
    </testcase>
    <testcase classname="CashFlowFeatures should render null when features are not null" name="CashFlowFeatures should render null when features are not null" time="0.004">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:52" time="1.115" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.004">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.008">
    </testcase>
    <testcase classname="NotificationBottomModal Should render and match the snapshot" name="NotificationBottomModal Should render and match the snapshot" time="0.103">
    </testcase>
    <testcase classname="NotificationBottomModal Should render the modal when the notification is not null" name="NotificationBottomModal Should render the modal when the notification is not null" time="0.114">
    </testcase>
    <testcase classname="NotificationBottomModal Should render the modal when the notification is null" name="NotificationBottomModal Should render the modal when the notification is null" time="0.003">
    </testcase>
  </testsuite>
  <testsuite name="Global reducer" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:53" time="0.348" tests="6">
    <testcase classname="Global reducer should return the initial state" name="Global reducer should return the initial state" time="0.004">
    </testcase>
    <testcase classname="Global reducer should handle setIsComingFromDrawer action" name="Global reducer should handle setIsComingFromDrawer action" time="0.002">
    </testcase>
    <testcase classname="Global reducer should handle setIsActualScreenScaledAction action" name="Global reducer should handle setIsActualScreenScaledAction action" time="0.001">
    </testcase>
    <testcase classname="Global reducer should handle setIsApplicationVersionUpdated action" name="Global reducer should handle setIsApplicationVersionUpdated action" time="0.001">
    </testcase>
    <testcase classname="Global reducer should handle setIsApplicationVersionUpdatedWithSuccess action" name="Global reducer should handle setIsApplicationVersionUpdatedWithSuccess action" time="0.001">
    </testcase>
    <testcase classname="Global reducer should handle setIsApplicationVersionUpdatedWithError action" name="Global reducer should handle setIsApplicationVersionUpdatedWithError action" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:52" time="1.301" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.007">
    </testcase>
    <testcase classname="Discussion Details Header should render and match the snapshot" name="Discussion Details Header should render and match the snapshot" time="0.169">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:52" time="0.974" tests="4">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.008">
    </testcase>
    <testcase classname="CalendarIconHeader  Should Render and match the snapshot" name="CalendarIconHeader  Should Render and match the snapshot" time="0.161">
    </testcase>
    <testcase classname="CalendarIconHeader  should render without crashing" name="CalendarIconHeader  should render without crashing" time="0.005">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:52" time="1.102" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.019">
    </testcase>
    <testcase classname="FilterProjectsOption Should render and match the snapshot" name="FilterProjectsOption Should render and match the snapshot" time="0.188">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:53" time="1.008" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.004">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.006">
    </testcase>
    <testcase classname="DiscussionKPIBody Should render and match the snapshot" name="DiscussionKPIBody Should render and match the snapshot" time="0.137">
    </testcase>
    <testcase classname="DiscussionKPIBody should render null when discussion is null" name="DiscussionKPIBody should render null when discussion is null" time="0.003">
    </testcase>
    <testcase classname="DiscussionKPIBody should render null when discussion is not null" name="DiscussionKPIBody should render null when discussion is not null" time="0.019">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:53" time="1.065" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.009">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.004">
    </testcase>
    <testcase classname="GetStartedButton Should render and match the snapshot" name="GetStartedButton Should render and match the snapshot" time="0.159">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:53" time="1.133" tests="4">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.016">
    </testcase>
    <testcase classname="CustomDropdown should render and match the snapshot" name="CustomDropdown should render and match the snapshot" time="0.154">
    </testcase>
    <testcase classname="CustomDropdown should render without crashing" name="CustomDropdown should render without crashing" time="0.006">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:53" time="1.11" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.004">
    </testcase>
    <testcase classname="QualityAssuranceFeatures should render and match the snapshot" name="QualityAssuranceFeatures should render and match the snapshot" time="0.104">
    </testcase>
    <testcase classname="QualityAssuranceFeatures should render null when features are null" name="QualityAssuranceFeatures should render null when features are null" time="0.019">
    </testcase>
    <testcase classname="QualityAssuranceFeatures should render null when features are not null" name="QualityAssuranceFeatures should render null when features are not null" time="0.004">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:53" time="1.057" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.005">
    </testcase>
    <testcase classname="Evolution Should render and match the snapshot" name="Evolution Should render and match the snapshot" time="0.135">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:53" time="1.035" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.007">
    </testcase>
    <testcase classname="Channel should render and match the snapshot" name="Channel should render and match the snapshot" time="0.118">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:54" time="0.958" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.008">
    </testcase>
    <testcase classname="SPI Should render and match the snapshot" name="SPI Should render and match the snapshot" time="0.12">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:54" time="1.346" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.009">
    </testcase>
    <testcase classname="Notifications Icon Header should render and match the snapshot" name="Notifications Icon Header should render and match the snapshot" time="0.116">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:54" time="1.079" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.004">
    </testcase>
    <testcase classname="PlannedSOP Should render and match the snapshot" name="PlannedSOP Should render and match the snapshot" time="0.09">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:54" time="0.926" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.005">
    </testcase>
    <testcase classname="TRCFR Should render and match the snapshot" name="TRCFR Should render and match the snapshot" time="0.111">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:55" time="0.935" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.005">
    </testcase>
    <testcase classname="PassDueContractPayment Should render and match the snapshot" name="PassDueContractPayment Should render and match the snapshot" time="0.121">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:55" time="0.999" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.005">
    </testcase>
    <testcase classname="CPI Component renders CPI component with value" name="CPI Component renders CPI component with value" time="0.144">
    </testcase>
    <testcase classname="CPI Component displays the correct value" name="CPI Component displays the correct value" time="0.008">
    </testcase>
    <testcase classname="CPI Component Should render and match the snapshot" name="CPI Component Should render and match the snapshot" time="0.013">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:55" time="1.029" tests="4">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.004">
    </testcase>
    <testcase classname="ClickableImage calls onPress when the image is clicked" name="ClickableImage calls onPress when the image is clicked" time="0.165">
    </testcase>
    <testcase classname="ClickableImage Should render and match the snapshot" name="ClickableImage Should render and match the snapshot" time="0.009">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:55" time="0.98" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.01">
    </testcase>
    <testcase classname="DiscussionReportBody Should render and match the snapshot" name="DiscussionReportBody Should render and match the snapshot" time="0.098">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:55" time="0.95" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.005">
    </testcase>
    <testcase classname="PassDueJESAPayment Should render and match the snapshot" name="PassDueJESAPayment Should render and match the snapshot" time="0.101">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:55" time="1.023" tests="4">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.012">
    </testcase>
    <testcase classname="OverviewProject Should render and match the snapshot" name="OverviewProject Should render and match the snapshot" time="0.12">
    </testcase>
    <testcase classname="OverviewProject Should render the overview project when the current project is not null" name="OverviewProject Should render the overview project when the current project is not null" time="0.012">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:55" time="1.028" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.011">
    </testcase>
    <testcase classname="DeleteNotification Should render and match the snapshot" name="DeleteNotification Should render and match the snapshot" time="0.107">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:56" time="0.899" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.004">
    </testcase>
    <testcase classname="PendingJesaChange Should render and match the snapshot" name="PendingJesaChange Should render and match the snapshot" time="0.113">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:56" time="1.012" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.006">
    </testcase>
    <testcase classname="ForecastSOP Should render and match the snapshot" name="ForecastSOP Should render and match the snapshot" time="0.117">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:56" time="1.092" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.01">
    </testcase>
    <testcase classname="ProgressFeatures should render and match the snapshot" name="ProgressFeatures should render and match the snapshot" time="0.106">
    </testcase>
    <testcase classname="ProgressFeatures should render null when features are null" name="ProgressFeatures should render null when features are null" time="0.012">
    </testcase>
    <testcase classname="ProgressFeatures should render null when features are not null" name="ProgressFeatures should render null when features are not null" time="0.004">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:56" time="0.886" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.004">
    </testcase>
    <testcase classname="PendingContractorChange Should render and match the snapshot" name="PendingContractorChange Should render and match the snapshot" time="0.116">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:56" time="0.88" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.008">
    </testcase>
    <testcase classname="CustomLinearGradient should render and match the snapshot" name="CustomLinearGradient should render and match the snapshot" time="0.102">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:56" time="0.935" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.006">
    </testcase>
    <testcase classname="Planned Should render and match the snapshot" name="Planned Should render and match the snapshot" time="0.131">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:56" time="1.028" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.01">
    </testcase>
    <testcase classname="DiscussionActionBody Should render and match the snapshot" name="DiscussionActionBody Should render and match the snapshot" time="0.098">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:57" time="0.945" tests="4">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.004">
    </testcase>
    <testcase classname="Actual Component renders with the correct value" name="Actual Component renders with the correct value" time="0.106">
    </testcase>
    <testcase classname="Actual Component Should Render and match the snapshot" name="Actual Component Should Render and match the snapshot" time="0.008">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:57" time="0.969" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.004">
    </testcase>
    <testcase classname="InputText Should render and match the snapshot" name="InputText Should render and match the snapshot" time="0.134">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:57" time="0.94" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.006">
    </testcase>
    <testcase classname="ItemBottomCarousel Should render and match the snapshot" name="ItemBottomCarousel Should render and match the snapshot" time="0.104">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:57" time="0.94" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.005">
    </testcase>
    <testcase classname="RevisedBudget Should render and match the snapshot" name="RevisedBudget Should render and match the snapshot" time="0.1">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:57" time="0.919" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.004">
    </testcase>
    <testcase classname="Forecast Should render and match the snapshot" name="Forecast Should render and match the snapshot" time="0.114">
    </testcase>
  </testsuite>
  <testsuite name="notificationsSelector" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:58" time="0.256" tests="1">
    <testcase classname="notificationsSelector should select the notifications state from the root state" name="notificationsSelector should select the notifications state from the root state" time="0.005">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:57" time="0.967" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.006">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.005">
    </testcase>
    <testcase classname="HeadCountContractor Should render and match the snapshot" name="HeadCountContractor Should render and match the snapshot" time="0.116">
    </testcase>
  </testsuite>
  <testsuite name="Projects reducer" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:58" time="0.305" tests="9">
    <testcase classname="Projects reducer should return the initial state" name="Projects reducer should return the initial state" time="0.005">
    </testcase>
    <testcase classname="Projects reducer get Projects should handle getProjects action" name="Projects reducer get Projects should handle getProjects action" time="0.002">
    </testcase>
    <testcase classname="Projects reducer get Projects should handle getProjectsWithSuccess action" name="Projects reducer get Projects should handle getProjectsWithSuccess action" time="0.002">
    </testcase>
    <testcase classname="Projects reducer get Projects should handle getProjectsWithError action" name="Projects reducer get Projects should handle getProjectsWithError action" time="0.009">
    </testcase>
    <testcase classname="Projects reducer updateProjects should handle updateProjects action" name="Projects reducer updateProjects should handle updateProjects action" time="0.002">
    </testcase>
    <testcase classname="Projects reducer updateProjects should handle updateProjectsWithSuccess action" name="Projects reducer updateProjects should handle updateProjectsWithSuccess action" time="0.001">
    </testcase>
    <testcase classname="Projects reducer updateProjects should handle updateProjectsWithError action" name="Projects reducer updateProjects should handle updateProjectsWithError action" time="0.001">
    </testcase>
    <testcase classname="Projects reducer should handle setFilterParams action" name="Projects reducer should handle setFilterParams action" time="0.001">
    </testcase>
    <testcase classname="Projects reducer should handle resetFilterItem action" name="Projects reducer should handle resetFilterItem action" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Cash flow reducer" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:58" time="0.244" tests="6">
    <testcase classname="Cash flow reducer should return the initial state" name="Cash flow reducer should return the initial state" time="0.008">
    </testcase>
    <testcase classname="Cash flow reducer should handle getCashFlowFeatures action" name="Cash flow reducer should handle getCashFlowFeatures action" time="0.001">
    </testcase>
    <testcase classname="Cash flow reducer should handle getCashFlowFeaturesWithSuccess action when the channel status is not null" name="Cash flow reducer should handle getCashFlowFeaturesWithSuccess action when the channel status is not null" time="0.003">
    </testcase>
    <testcase classname="Cash flow reducer should handle getCashFlowFeaturesWithSuccess action when the channel status is null" name="Cash flow reducer should handle getCashFlowFeaturesWithSuccess action when the channel status is null" time="0.001">
    </testcase>
    <testcase classname="Cash flow reducer should handle getCashFlowFeaturesWithError action" name="Cash flow reducer should handle getCashFlowFeaturesWithError action" time="0.001">
    </testcase>
    <testcase classname="Cash flow reducer should handle cleanUp action" name="Cash flow reducer should handle cleanUp action" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:57" time="0.984" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.005">
    </testcase>
    <testcase classname="CriticalRisk Component renders CriticalRisk component with value" name="CriticalRisk Component renders CriticalRisk component with value" time="0.128">
    </testcase>
    <testcase classname="CriticalRisk Component displays the correct value" name="CriticalRisk Component displays the correct value" time="0.005">
    </testcase>
    <testcase classname="CriticalRisk Component Should render and match the snapshot" name="CriticalRisk Component Should render and match the snapshot" time="0.007">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:58" time="0.981" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.004">
    </testcase>
    <testcase classname="Feature Should render and match the snapshot" name="Feature Should render and match the snapshot" time="0.117">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:58" time="0.948" tests="4">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.005">
    </testcase>
    <testcase classname="CriticalSOR should render and match the snapshot" name="CriticalSOR should render and match the snapshot" time="0.116">
    </testcase>
    <testcase classname="CriticalSOR should render without crashing" name="CriticalSOR should render without crashing" time="0.006">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:58" time="0.769" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.006">
    </testcase>
    <testcase classname="globalSagas should yield an api call" name="globalSagas should yield an api call" time="0.024">
    </testcase>
    <testcase classname="globalSagas should handle setIsApplicationVersionUpdated sagas successfully" name="globalSagas should handle setIsApplicationVersionUpdated sagas successfully" time="0.005">
    </testcase>
    <testcase classname="globalSagas should handle setIsApplicationVersionUpdated sagas with error" name="globalSagas should handle setIsApplicationVersionUpdated sagas with error" time="0.004">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:58" time="0.94" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.004">
    </testcase>
    <testcase classname="Date Picker should render and match the snapshot" name="Date Picker should render and match the snapshot" time="0.101">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:58" time="0.983" tests="3">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.006">
    </testcase>
    <testcase classname="HeadCountJesa Should render and match the snapshot" name="HeadCountJesa Should render and match the snapshot" time="0.114">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:59" time="0.823" tests="8">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.004">
    </testcase>
    <testcase classname="handleAuthenticationSagas should yield an api call of checking if the user is admin or not" name="handleAuthenticationSagas should yield an api call of checking if the user is admin or not" time="0.007">
    </testcase>
    <testcase classname="handleAuthenticationSagas should handle authentication sagas successfully" name="handleAuthenticationSagas should handle authentication sagas successfully" time="0.002">
    </testcase>
    <testcase classname="handleAuthenticationSagas should handle authentication sagas with error" name="handleAuthenticationSagas should handle authentication sagas with error" time="0.004">
    </testcase>
    <testcase classname="handleAuthenticationSagas should yield and api call while calling the update firebase token" name="handleAuthenticationSagas should yield and api call while calling the update firebase token" time="0.189">
    </testcase>
    <testcase classname="handleAuthenticationSagas should handle setting fcm token successfully" name="handleAuthenticationSagas should handle setting fcm token successfully" time="0.006">
    </testcase>
    <testcase classname="handleAuthenticationSagas should handle setting fcm token with error" name="handleAuthenticationSagas should handle setting fcm token with error" time="0.005">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:59" time="0.649" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.004">
    </testcase>
    <testcase classname="bu enums saga should yield an api call" name="bu enums saga should yield an api call" time="0.008">
    </testcase>
    <testcase classname="bu enums saga should handle program enums successfully" name="bu enums saga should handle program enums successfully" time="0.005">
    </testcase>
    <testcase classname="bu enums saga should handle program enums with error" name="bu enums saga should handle program enums with error" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:58" time="0.916" tests="14">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.005">
    </testcase>
    <testcase classname="notificationsSagas getNotificationsList should yield an api call" name="notificationsSagas getNotificationsList should yield an api call" time="0.007">
    </testcase>
    <testcase classname="notificationsSagas getNotificationsList should handle getNotificationsListWithSuccess sagas successfully" name="notificationsSagas getNotificationsList should handle getNotificationsListWithSuccess sagas successfully" time="0.011">
    </testcase>
    <testcase classname="notificationsSagas getNotificationsList should handle getNotificationsListWithError sagas successfully" name="notificationsSagas getNotificationsList should handle getNotificationsListWithError sagas successfully" time="0.001">
    </testcase>
    <testcase classname="notificationsSagas get Notifications Existence should yield an api call" name="notificationsSagas get Notifications Existence should yield an api call" time="0.002">
    </testcase>
    <testcase classname="notificationsSagas get Notifications Existence should handle getNotificationsExistenceWithSuccess sagas successfully" name="notificationsSagas get Notifications Existence should handle getNotificationsExistenceWithSuccess sagas successfully" time="0.001">
    </testcase>
    <testcase classname="notificationsSagas get Notifications Existence should handle getNotificationsExistenceWithError sagas successfully" name="notificationsSagas get Notifications Existence should handle getNotificationsExistenceWithError sagas successfully" time="0.001">
    </testcase>
    <testcase classname="notificationsSagas mark as read should yield an api call" name="notificationsSagas mark as read should yield an api call" time="0.216">
    </testcase>
    <testcase classname="notificationsSagas mark as read should handle markAsRead sagas successfully" name="notificationsSagas mark as read should handle markAsRead sagas successfully" time="0.006">
    </testcase>
    <testcase classname="notificationsSagas mark as read should handle getNotificationsExistenceWithError sagas successfully" name="notificationsSagas mark as read should handle getNotificationsExistenceWithError sagas successfully" time="0.006">
    </testcase>
    <testcase classname="notificationsSagas mark as delete should yield an api call" name="notificationsSagas mark as delete should yield an api call" time="0.004">
    </testcase>
    <testcase classname="notificationsSagas mark as delete should handle markAsRead sagas successfully" name="notificationsSagas mark as delete should handle markAsRead sagas successfully" time="0.003">
    </testcase>
    <testcase classname="notificationsSagas mark as delete should handle getNotificationsExistenceWithError sagas successfully" name="notificationsSagas mark as delete should handle getNotificationsExistenceWithError sagas successfully" time="0.004">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:59" time="0.682" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.006">
    </testcase>
    <testcase classname="cost saga should yield an api call" name="cost saga should yield an api call" time="0.007">
    </testcase>
    <testcase classname="cost saga should handle progress features successfully" name="cost saga should handle progress features successfully" time="0.005">
    </testcase>
    <testcase classname="cost saga should handle progress features with error" name="cost saga should handle progress features with error" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:59" time="0.596" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.004">
    </testcase>
    <testcase classname="hse saga should yield an api call" name="hse saga should yield an api call" time="0.008">
    </testcase>
    <testcase classname="hse saga should handle hse features successfully" name="hse saga should handle hse features successfully" time="0.005">
    </testcase>
    <testcase classname="hse saga should handle hse features with error" name="hse saga should handle hse features with error" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:59" time="0.626" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.004">
    </testcase>
    <testcase classname="risk saga should yield an api call" name="risk saga should yield an api call" time="0.006">
    </testcase>
    <testcase classname="risk saga should handle risk features successfully" name="risk saga should handle risk features successfully" time="0.005">
    </testcase>
    <testcase classname="risk saga should handle risk features with error" name="risk saga should handle risk features with error" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:30:59" time="0.734" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.001">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.004">
    </testcase>
    <testcase classname="bu enums saga should yield an api call" name="bu enums saga should yield an api call" time="0.008">
    </testcase>
    <testcase classname="bu enums saga should handle bu enums successfully" name="bu enums saga should handle bu enums successfully" time="0.004">
    </testcase>
    <testcase classname="bu enums saga should handle bu enums with error" name="bu enums saga should handle bu enums with error" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="globalSelector" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:01" time="1.228" tests="1">
    <testcase classname="globalSelector should select the global state from the root state" name="globalSelector should select the global state from the root state" time="0.011">
    </testcase>
  </testsuite>
  <testsuite name="Hse reducer" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:01" time="0.981" tests="6">
    <testcase classname="Hse reducer should return the initial state" name="Hse reducer should return the initial state" time="0.014">
    </testcase>
    <testcase classname="Hse reducer should handle getHseFeatures action" name="Hse reducer should handle getHseFeatures action" time="0.001">
    </testcase>
    <testcase classname="Hse reducer should handle getHseFeaturesWithSuccess action when the channel status is not null" name="Hse reducer should handle getHseFeaturesWithSuccess action when the channel status is not null" time="0.001">
    </testcase>
    <testcase classname="Hse reducer should handle getHseFeaturesWithSuccess action when the channel status is null" name="Hse reducer should handle getHseFeaturesWithSuccess action when the channel status is null" time="0.001">
    </testcase>
    <testcase classname="Hse reducer should handle getHseFeaturesWithError action" name="Hse reducer should handle getHseFeaturesWithError action" time="0.001">
    </testcase>
    <testcase classname="Hse reducer should handle cleanUp action" name="Hse reducer should handle cleanUp action" time="0">
    </testcase>
  </testsuite>
  <testsuite name="Discussion reducer" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:02" time="0.3" tests="11">
    <testcase classname="Discussion reducer should return the initial state" name="Discussion reducer should return the initial state" time="0.004">
    </testcase>
    <testcase classname="Discussion reducer should handle getDiscussionDetailsByObjectTypeAndObjectId action" name="Discussion reducer should handle getDiscussionDetailsByObjectTypeAndObjectId action" time="0.001">
    </testcase>
    <testcase classname="Discussion reducer should handle getDiscussionDetailsByObjectTypeAndObjectIdWithSuccess action" name="Discussion reducer should handle getDiscussionDetailsByObjectTypeAndObjectIdWithSuccess action" time="0.002">
    </testcase>
    <testcase classname="Discussion reducer should handle getDiscussionDetailsByObjectTypeAndObjectIdWithError action" name="Discussion reducer should handle getDiscussionDetailsByObjectTypeAndObjectIdWithError action" time="0.001">
    </testcase>
    <testcase classname="Discussion reducer should handle getDiscussionUsers action" name="Discussion reducer should handle getDiscussionUsers action" time="0.001">
    </testcase>
    <testcase classname="Discussion reducer should handle getDiscussionUsersWithSuccess action" name="Discussion reducer should handle getDiscussionUsersWithSuccess action" time="0.001">
    </testcase>
    <testcase classname="Discussion reducer should handle getDiscussionUsersWithSuccessWithError action" name="Discussion reducer should handle getDiscussionUsersWithSuccessWithError action" time="0.001">
    </testcase>
    <testcase classname="Discussion reducer should handle createUpdate action" name="Discussion reducer should handle createUpdate action" time="0.001">
    </testcase>
    <testcase classname="Discussion reducer should handle createUpdateWithSuccess action when the discussion is not null" name="Discussion reducer should handle createUpdateWithSuccess action when the discussion is not null" time="0.002">
    </testcase>
    <testcase classname="Discussion reducer should handle createUpdateWithSuccess action when the discussion is null" name="Discussion reducer should handle createUpdateWithSuccess action when the discussion is null" time="0">
    </testcase>
    <testcase classname="Discussion reducer should handle createUpdateWithError action" name="Discussion reducer should handle createUpdateWithError action" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Authentication reducer" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:02" time="0.313" tests="15">
    <testcase classname="Authentication reducer should return the initial state" name="Authentication reducer should return the initial state" time="0.011">
    </testcase>
    <testcase classname="Authentication reducer should handle login action" name="Authentication reducer should handle login action" time="0.002">
    </testcase>
    <testcase classname="Authentication reducer should handle logout action" name="Authentication reducer should handle logout action" time="0.001">
    </testcase>
    <testcase classname="Authentication reducer should handle setIsAuthenticated action" name="Authentication reducer should handle setIsAuthenticated action" time="0.001">
    </testcase>
    <testcase classname="Authentication reducer should handle setUserToken action" name="Authentication reducer should handle setUserToken action" time="0.002">
    </testcase>
    <testcase classname="Authentication reducer should handle setIsLoadingCheckingAuth action" name="Authentication reducer should handle setIsLoadingCheckingAuth action" time="0.001">
    </testcase>
    <testcase classname="Authentication reducer should handle setIsAdmin action" name="Authentication reducer should handle setIsAdmin action" time="0.001">
    </testcase>
    <testcase classname="Authentication reducer should handle setIsAdminWithSuccess action" name="Authentication reducer should handle setIsAdminWithSuccess action" time="0.001">
    </testcase>
    <testcase classname="Authentication reducer should handle setIsAdminWithError action" name="Authentication reducer should handle setIsAdminWithError action" time="0">
    </testcase>
    <testcase classname="Authentication reducer should handle setUserId action" name="Authentication reducer should handle setUserId action" time="0">
    </testcase>
    <testcase classname="Authentication reducer should handle setUserIdWithSuccess action" name="Authentication reducer should handle setUserIdWithSuccess action" time="0.001">
    </testcase>
    <testcase classname="Authentication reducer should handle setUserIdWithError action" name="Authentication reducer should handle setUserIdWithError action" time="0.002">
    </testcase>
    <testcase classname="Authentication reducer should handle setFcmToken action" name="Authentication reducer should handle setFcmToken action" time="0.001">
    </testcase>
    <testcase classname="Authentication reducer should handle setFcmTokenWithSuccess action" name="Authentication reducer should handle setFcmTokenWithSuccess action" time="0">
    </testcase>
    <testcase classname="Authentication reducer should handle setFcmTokenWithError action" name="Authentication reducer should handle setFcmTokenWithError action" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="CashFlowSelector" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:02" time="0.261" tests="1">
    <testcase classname="CashFlowSelector should select the cash flow state from the root state" name="CashFlowSelector should select the cash flow state from the root state" time="0.003">
    </testcase>
  </testsuite>
  <testsuite name="projectsSelector" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:02" time="0.314" tests="1">
    <testcase classname="projectsSelector should select the projects state from the root state" name="projectsSelector should select the projects state from the root state" time="0.004">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:01" time="2.177" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.009">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.004">
    </testcase>
    <testcase classname="scheduling saga should yield an api call" name="scheduling saga should yield an api call" time="0.009">
    </testcase>
    <testcase classname="scheduling saga should handle scheduling features successfully" name="scheduling saga should handle scheduling features successfully" time="0.005">
    </testcase>
    <testcase classname="scheduling saga should handle scheduling features with error" name="scheduling saga should handle scheduling features with error" time="0.002">
    </testcase>
  </testsuite>
  <testsuite name="programEnumsSelector" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:03" time="0.298" tests="1">
    <testcase classname="programEnumsSelector should select the program enums state from the root state" name="programEnumsSelector should select the program enums state from the root state" time="0.004">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:01" time="2.324" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.008">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.004">
    </testcase>
    <testcase classname="changeManagement saga should yield an api call" name="changeManagement saga should yield an api call" time="0.008">
    </testcase>
    <testcase classname="changeManagement saga should handle changeManagement flow features successfully" name="changeManagement saga should handle changeManagement flow features successfully" time="0.006">
    </testcase>
    <testcase classname="changeManagement saga should handle changeManagement features with error" name="changeManagement saga should handle changeManagement features with error" time="0.002">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:01" time="2.295" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.036">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.004">
    </testcase>
    <testcase classname="cost saga should yield an api call" name="cost saga should yield an api call" time="0.007">
    </testcase>
    <testcase classname="cost saga should handle cost flow features successfully" name="cost saga should handle cost flow features successfully" time="0.005">
    </testcase>
    <testcase classname="cost saga should handle changeManagement features with error" name="cost saga should handle changeManagement features with error" time="0.002">
    </testcase>
  </testsuite>
  <testsuite name="sectorEnumsSelector" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:03" time="0.277" tests="1">
    <testcase classname="sectorEnumsSelector should select the sector enums state from the root state" name="sectorEnumsSelector should select the sector enums state from the root state" time="0.004">
    </testcase>
  </testsuite>
  <testsuite name="Progress reducer" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:03" time="0.268" tests="6">
    <testcase classname="Progress reducer should return the initial state" name="Progress reducer should return the initial state" time="0.005">
    </testcase>
    <testcase classname="Progress reducer should handle getProgressFeatures action" name="Progress reducer should handle getProgressFeatures action" time="0.002">
    </testcase>
    <testcase classname="Progress reducer should handle getProgressFeaturesWithSuccess action when the channel status is not null" name="Progress reducer should handle getProgressFeaturesWithSuccess action when the channel status is not null" time="0.001">
    </testcase>
    <testcase classname="Progress reducer should handle getProgressFeaturesWithSuccess action when the channel status is null" name="Progress reducer should handle getProgressFeaturesWithSuccess action when the channel status is null" time="0">
    </testcase>
    <testcase classname="Progress reducer should handle getProgressFeaturesWithError action" name="Progress reducer should handle getProgressFeaturesWithError action" time="0.001">
    </testcase>
    <testcase classname="Progress reducer should handle cleanUp action" name="Progress reducer should handle cleanUp action" time="0">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:01" time="2.391" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.012">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.004">
    </testcase>
    <testcase classname="sector enums saga should yield an api call" name="sector enums saga should yield an api call" time="0.015">
    </testcase>
    <testcase classname="sector enums saga should handle sector enums successfully" name="sector enums saga should handle sector enums successfully" time="0.019">
    </testcase>
    <testcase classname="sector enums saga should handle sector enums with error" name="sector enums saga should handle sector enums with error" time="0.002">
    </testcase>
  </testsuite>
  <testsuite name="Cost reducer" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:03" time="0.261" tests="6">
    <testcase classname="Cost reducer should return the initial state" name="Cost reducer should return the initial state" time="0.005">
    </testcase>
    <testcase classname="Cost reducer should handle getCostFeatures action" name="Cost reducer should handle getCostFeatures action" time="0.001">
    </testcase>
    <testcase classname="Cost reducer should handle getQAFeaturesWithSuccess action when the channel status is not null" name="Cost reducer should handle getQAFeaturesWithSuccess action when the channel status is not null" time="0.001">
    </testcase>
    <testcase classname="Cost reducer should handle getQAFeaturesWithSuccess action when the channel status is null" name="Cost reducer should handle getQAFeaturesWithSuccess action when the channel status is null" time="0.002">
    </testcase>
    <testcase classname="Cost reducer should handle getQAFeaturesWithError action" name="Cost reducer should handle getQAFeaturesWithError action" time="0.001">
    </testcase>
    <testcase classname="Cost reducer should handle cleanUp action" name="Cost reducer should handle cleanUp action" time="0">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:01" time="2.418" tests="5">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.009">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.007">
    </testcase>
    <testcase classname="cashFlowChannelSaga should yield an api call" name="cashFlowChannelSaga should yield an api call" time="0.008">
    </testcase>
    <testcase classname="cashFlowChannelSaga should handle cash flow features successfully" name="cashFlowChannelSaga should handle cash flow features successfully" time="0.006">
    </testcase>
    <testcase classname="cashFlowChannelSaga should handle cash flow features with error" name="cashFlowChannelSaga should handle cash flow features with error" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Cost reducer" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:03" time="0.274" tests="6">
    <testcase classname="Cost reducer should return the initial state" name="Cost reducer should return the initial state" time="0.007">
    </testcase>
    <testcase classname="Cost reducer should handle getCostFeatures action" name="Cost reducer should handle getCostFeatures action" time="0.002">
    </testcase>
    <testcase classname="Cost reducer should handle getCostFeaturesWithSuccess action when the channel status is not null" name="Cost reducer should handle getCostFeaturesWithSuccess action when the channel status is not null" time="0.001">
    </testcase>
    <testcase classname="Cost reducer should handle getCostFeaturesWithSuccess action when the channel status is null" name="Cost reducer should handle getCostFeaturesWithSuccess action when the channel status is null" time="0.001">
    </testcase>
    <testcase classname="Cost reducer should handle getCostFeaturesWithError action" name="Cost reducer should handle getCostFeaturesWithError action" time="0.001">
    </testcase>
    <testcase classname="Cost reducer should handle cleanUp action" name="Cost reducer should handle cleanUp action" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Sector Enums reducer" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:04" time="0.259" tests="5">
    <testcase classname="Sector Enums reducer should return the initial state" name="Sector Enums reducer should return the initial state" time="0.004">
    </testcase>
    <testcase classname="Sector Enums reducer should handle getSectorEnums action" name="Sector Enums reducer should handle getSectorEnums action" time="0.001">
    </testcase>
    <testcase classname="Sector Enums reducer should handle getSectorEnumsWithSuccess action " name="Sector Enums reducer should handle getSectorEnumsWithSuccess action " time="0.001">
    </testcase>
    <testcase classname="Sector Enums reducer should handle getSectorEnumsWithError action" name="Sector Enums reducer should handle getSectorEnumsWithError action" time="0.001">
    </testcase>
    <testcase classname="Sector Enums reducer should handle cleanUp action" name="Sector Enums reducer should handle cleanUp action" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Program Enums reducer" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:04" time="0.295" tests="5">
    <testcase classname="Program Enums reducer should return the initial state" name="Program Enums reducer should return the initial state" time="0.004">
    </testcase>
    <testcase classname="Program Enums reducer should handle getProgramEnums action" name="Program Enums reducer should handle getProgramEnums action" time="0.002">
    </testcase>
    <testcase classname="Program Enums reducer should handle getProgramEnumsWithSuccess action " name="Program Enums reducer should handle getProgramEnumsWithSuccess action " time="0.002">
    </testcase>
    <testcase classname="Program Enums reducer should handle getProgramEnumsWithError action" name="Program Enums reducer should handle getProgramEnumsWithError action" time="0.001">
    </testcase>
    <testcase classname="Program Enums reducer should handle cleanUp action" name="Program Enums reducer should handle cleanUp action" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Notifications reducer" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:04" time="0.295" tests="13">
    <testcase classname="Notifications reducer should return the initial state" name="Notifications reducer should return the initial state" time="0.019">
    </testcase>
    <testcase classname="Notifications reducer get notifications Existence  should handle getNotificationsExistence action" name="Notifications reducer get notifications Existence  should handle getNotificationsExistence action" time="0.002">
    </testcase>
    <testcase classname="Notifications reducer get notifications Existence  should handle getNotificationsExistenceWithSuccess action" name="Notifications reducer get notifications Existence  should handle getNotificationsExistenceWithSuccess action" time="0.002">
    </testcase>
    <testcase classname="Notifications reducer get notifications Existence  should handle getNotificationsExistenceWithError action" name="Notifications reducer get notifications Existence  should handle getNotificationsExistenceWithError action" time="0.001">
    </testcase>
    <testcase classname="Notifications reducer get notifications list  should handle getNotificationsList action" name="Notifications reducer get notifications list  should handle getNotificationsList action" time="0.002">
    </testcase>
    <testcase classname="Notifications reducer get notifications list  should handle getNotificationsListWithSuccess action" name="Notifications reducer get notifications list  should handle getNotificationsListWithSuccess action" time="0.001">
    </testcase>
    <testcase classname="Notifications reducer markAsRead should handle markAsRead action" name="Notifications reducer markAsRead should handle markAsRead action" time="0.001">
    </testcase>
    <testcase classname="Notifications reducer markAsRead should handle markAsReadWithSuccess action" name="Notifications reducer markAsRead should handle markAsReadWithSuccess action" time="0.001">
    </testcase>
    <testcase classname="Notifications reducer markAsRead should handle markAsReadWithError action" name="Notifications reducer markAsRead should handle markAsReadWithError action" time="0.001">
    </testcase>
    <testcase classname="Notifications reducer delete notification should handle delete action" name="Notifications reducer delete notification should handle delete action" time="0.001">
    </testcase>
    <testcase classname="Notifications reducer delete notification should handle delete with success action" name="Notifications reducer delete notification should handle delete with success action" time="0.001">
    </testcase>
    <testcase classname="Notifications reducer delete notification should handle delete with error action" name="Notifications reducer delete notification should handle delete with error action" time="0.001">
    </testcase>
    <testcase classname="Notifications reducer should handle cleanUp action" name="Notifications reducer should handle cleanUp action" time="0">
    </testcase>
  </testsuite>
  <testsuite name="HumanCap reducer" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:04" time="0.282" tests="6">
    <testcase classname="HumanCap reducer should return the initial state" name="HumanCap reducer should return the initial state" time="0.004">
    </testcase>
    <testcase classname="HumanCap reducer should handle getHumanCapFeatures action" name="HumanCap reducer should handle getHumanCapFeatures action" time="0.001">
    </testcase>
    <testcase classname="HumanCap reducer should handle getHumanCapFeaturesWithSuccess action when the channel status is not null" name="HumanCap reducer should handle getHumanCapFeaturesWithSuccess action when the channel status is not null" time="0.001">
    </testcase>
    <testcase classname="HumanCap reducer should handle getHumanCapFeaturesWithSuccess action when the channel status is null" name="HumanCap reducer should handle getHumanCapFeaturesWithSuccess action when the channel status is null" time="0.001">
    </testcase>
    <testcase classname="HumanCap reducer should handle getHumanCapFeaturesWithError action" name="HumanCap reducer should handle getHumanCapFeaturesWithError action" time="0.001">
    </testcase>
    <testcase classname="HumanCap reducer should handle cleanUp action" name="HumanCap reducer should handle cleanUp action" time="0">
    </testcase>
  </testsuite>
  <testsuite name="authenticationSelector" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:04" time="0.266" tests="1">
    <testcase classname="authenticationSelector should select the authentication state from the root state" name="authenticationSelector should select the authentication state from the root state" time="0.004">
    </testcase>
  </testsuite>
  <testsuite name="Bu Enums reducer" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:04" time="0.243" tests="5">
    <testcase classname="Bu Enums reducer should return the initial state" name="Bu Enums reducer should return the initial state" time="0.006">
    </testcase>
    <testcase classname="Bu Enums reducer should handle getBuEnums action" name="Bu Enums reducer should handle getBuEnums action" time="0.001">
    </testcase>
    <testcase classname="Bu Enums reducer should handle getBuEnumsWithSuccess action " name="Bu Enums reducer should handle getBuEnumsWithSuccess action " time="0.002">
    </testcase>
    <testcase classname="Bu Enums reducer should handle getBuEnumsWithError action" name="Bu Enums reducer should handle getBuEnumsWithError action" time="0.001">
    </testcase>
    <testcase classname="Bu Enums reducer should handle cleanUp action" name="Bu Enums reducer should handle cleanUp action" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Notification reducer" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:04" time="0.26" tests="4">
    <testcase classname="Notification reducer should return the initial state" name="Notification reducer should return the initial state" time="0.005">
    </testcase>
    <testcase classname="Notification reducer should handle setNotification action" name="Notification reducer should handle setNotification action" time="0.002">
    </testcase>
    <testcase classname="Notification reducer should handle setOpenedFromNotification action" name="Notification reducer should handle setOpenedFromNotification action" time="0.001">
    </testcase>
    <testcase classname="Notification reducer should handle cleanUp action" name="Notification reducer should handle cleanUp action" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="carouselProjectDetails reducer" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:05" time="0.276" tests="3">
    <testcase classname="carouselProjectDetails reducer should return the initial state" name="carouselProjectDetails reducer should return the initial state" time="0.005">
    </testcase>
    <testcase classname="carouselProjectDetails reducer should handle update carousel position action" name="carouselProjectDetails reducer should handle update carousel position action" time="0.002">
    </testcase>
    <testcase classname="carouselProjectDetails reducer should handle clean up action" name="carouselProjectDetails reducer should handle clean up action" time="0">
    </testcase>
  </testsuite>
  <testsuite name="notificationSelector" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:05" time="0.261" tests="1">
    <testcase classname="notificationSelector should select the notification state from the root state" name="notificationSelector should select the notification state from the root state" time="0.004">
    </testcase>
  </testsuite>
  <testsuite name="RiskSelector" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:05" time="0.238" tests="1">
    <testcase classname="RiskSelector should select the risk state from the root state" name="RiskSelector should select the risk state from the root state" time="0.004">
    </testcase>
  </testsuite>
  <testsuite name="carouselProjectDetails selector" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:05" time="0.282" tests="1">
    <testcase classname="carouselProjectDetails selector should select the carouselProjectDetails state from the root state" name="carouselProjectDetails selector should select the carouselProjectDetails state from the root state" time="0.004">
    </testcase>
  </testsuite>
  <testsuite name="Scheduling reducer" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:04" time="0.986" tests="6">
    <testcase classname="Scheduling reducer should return the initial state" name="Scheduling reducer should return the initial state" time="0.011">
    </testcase>
    <testcase classname="Scheduling reducer should handle getSchedulingFeatures action" name="Scheduling reducer should handle getSchedulingFeatures action" time="0.001">
    </testcase>
    <testcase classname="Scheduling reducer should handle getSchedulingFeatures action when the channel status is not null" name="Scheduling reducer should handle getSchedulingFeatures action when the channel status is not null" time="0.001">
    </testcase>
    <testcase classname="Scheduling reducer should handle getSchedulingFeaturesWithSuccess action when the channel status is null" name="Scheduling reducer should handle getSchedulingFeaturesWithSuccess action when the channel status is null" time="0.001">
    </testcase>
    <testcase classname="Scheduling reducer should handle getSchedulingFeaturesWithError action" name="Scheduling reducer should handle getSchedulingFeaturesWithError action" time="0.001">
    </testcase>
    <testcase classname="Scheduling reducer should handle cleanUp action" name="Scheduling reducer should handle cleanUp action" time="0">
    </testcase>
  </testsuite>
  <testsuite name="QASelector" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:05" time="0.892" tests="1">
    <testcase classname="QASelector should select the qa state from the root state" name="QASelector should select the qa state from the root state" time="0.013">
    </testcase>
  </testsuite>
  <testsuite name="Cost reducer" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:05" time="0.948" tests="6">
    <testcase classname="Cost reducer should return the initial state" name="Cost reducer should return the initial state" time="0.012">
    </testcase>
    <testcase classname="Cost reducer should handle getCostFeatures action" name="Cost reducer should handle getCostFeatures action" time="0.002">
    </testcase>
    <testcase classname="Cost reducer should handle getCostFeaturesWithSuccess action when the channel status is not null" name="Cost reducer should handle getCostFeaturesWithSuccess action when the channel status is not null" time="0.001">
    </testcase>
    <testcase classname="Cost reducer should handle getCostFeaturesWithSuccess action when the channel status is null" name="Cost reducer should handle getCostFeaturesWithSuccess action when the channel status is null" time="0.001">
    </testcase>
    <testcase classname="Cost reducer should handle getCostFeaturesWithError action" name="Cost reducer should handle getCostFeaturesWithError action" time="0.001">
    </testcase>
    <testcase classname="Cost reducer should handle cleanUp action" name="Cost reducer should handle cleanUp action" time="0">
    </testcase>
  </testsuite>
  <testsuite name="Scheduling Selector" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:05" time="0.231" tests="1">
    <testcase classname="Scheduling Selector should select the scheduling state from the root state" name="Scheduling Selector should select the scheduling state from the root state" time="0.005">
    </testcase>
  </testsuite>
  <testsuite name="ChangeManagementSelector" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:05" time="0.236" tests="1">
    <testcase classname="ChangeManagementSelector should select the change management state from the root state" name="ChangeManagementSelector should select the change management state from the root state" time="0.004">
    </testcase>
  </testsuite>
  <testsuite name="HumanCapSelector" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:05" time="0.939" tests="1">
    <testcase classname="HumanCapSelector should select the human cap state from the root state" name="HumanCapSelector should select the human cap state from the root state" time="0.014">
    </testcase>
  </testsuite>
  <testsuite name="HseSelector" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:05" time="0.28" tests="1">
    <testcase classname="HseSelector should select the hse state from the root state" name="HseSelector should select the hse state from the root state" time="0.004">
    </testcase>
  </testsuite>
  <testsuite name="ProgressSelector" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:05" time="0.274" tests="1">
    <testcase classname="ProgressSelector should select the progress state from the root state" name="ProgressSelector should select the progress state from the root state" time="0.005">
    </testcase>
  </testsuite>
  <testsuite name="buEnumsSelector" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:05" time="0.969" tests="1">
    <testcase classname="buEnumsSelector should select the bu enums state from the root state" name="buEnumsSelector should select the bu enums state from the root state" time="0.01">
    </testcase>
  </testsuite>
  <testsuite name="CostSelector" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:06" time="0.263" tests="1">
    <testcase classname="CostSelector should select the cost state from the root state" name="CostSelector should select the cost state from the root state" time="0.003">
    </testcase>
  </testsuite>
  <testsuite name="Change Management reducer" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:06" time="0.295" tests="6">
    <testcase classname="Change Management reducer should return the initial state" name="Change Management reducer should return the initial state" time="0.003">
    </testcase>
    <testcase classname="Change Management reducer should handle getChangeManagementFeatures action" name="Change Management reducer should handle getChangeManagementFeatures action" time="0.001">
    </testcase>
    <testcase classname="Change Management reducer should handle getChangeManagementFeaturesWithSuccess action when the channel status is not null" name="Change Management reducer should handle getChangeManagementFeaturesWithSuccess action when the channel status is not null" time="0.001">
    </testcase>
    <testcase classname="Change Management reducer should handle getChangeManagementFeaturesWithSuccess action when the channel status is null" name="Change Management reducer should handle getChangeManagementFeaturesWithSuccess action when the channel status is null" time="0.004">
    </testcase>
    <testcase classname="Change Management reducer should handle getChangeManagementFeaturesWithError action" name="Change Management reducer should handle getChangeManagementFeaturesWithError action" time="0.001">
    </testcase>
    <testcase classname="Change Management reducer should handle cleanUp action" name="Change Management reducer should handle cleanUp action" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="authenticationSelector" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:06" time="0.199" tests="1">
    <testcase classname="authenticationSelector should select the authentication state from the root state" name="authenticationSelector should select the authentication state from the root state" time="0.003">
    </testcase>
  </testsuite>
  <testsuite name="AuthManager" errors="0" failures="0" skipped="0" timestamp="2023-10-03T16:31:06" time="0.847" tests="2">
    <testcase classname="AuthManager should sign in and return the access token" name="AuthManager should sign in and return the access token" time="0.002">
    </testcase>
    <testcase classname="AuthManager should sign out and clear the secure storage" name="AuthManager should sign out and clear the secure storage" time="0.003">
    </testcase>
  </testsuite>
</testsuites>