import {call, put, select, takeEvery} from 'redux-saga/effects';

import {projectSelector} from '@src/store/project/selectors';
import baseRequest from '@src/api/request';
import {ChannelsWebServices} from '@src/api/channels';
import {
  getSchedulingFeatures,
  getSchedulingFeaturesWithError,
  getSchedulingFeaturesWithSuccess,
} from '@src/store/channels/scheduling/slice';

export function* handleSchedulingFeatures() {
  try {
    const {projectId, reportingDateFilter} = yield select(projectSelector);
    const {data} = yield call(
      baseRequest.get,
      ChannelsWebServices.WS_GET_SCHEDULING_SUMMARY(projectId),
      {params: reportingDateFilter},
    );
    yield put(getSchedulingFeaturesWithSuccess({features: data}));
  } catch (e) {
    yield put(getSchedulingFeaturesWithError());
  }
}

export default function* QaSaga() {
  // same as incrementNumber.type or incrementNumber
  yield takeEvery(getSchedulingFeatures, handleSchedulingFeatures);
}
