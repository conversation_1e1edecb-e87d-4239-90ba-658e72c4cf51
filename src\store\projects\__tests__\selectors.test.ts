import {RootState} from '@src/store/store';
import {initialState, ProjectsSliceType} from '@src/store/projects/slice';
import {projectsSelector} from '@src/store/projects/selectors';

describe('projectsSelector', () => {
  it('should select the projects state from the root state', () => {
    // Arrange
    const projectsState: ProjectsSliceType = initialState;

    // @ts-ignore
    const mockRootState: RootState = {
      projects: projectsState,
    };

    // Act
    const selectedState = projectsSelector(mockRootState);

    // Assert
    expect(selectedState).toEqual(projectsState);
  });
});
