import { call, put, takeEvery } from "redux-saga/effects";
import {
  setIsApplicationVersionUpdated,
  setIsApplicationVersionUpdatedWithError,
  setIsApplicationVersionUpdatedWithSuccess
} from "@src/store/global/slice";
import baseRequest, { STATIC_TOKEN_FOR_CURRENT_VERSION } from "@src/api/request";
import { GlobalWebServices } from "@src/api/global";
import { CURRENT_APPLICATION_VERSION } from "@src/constants";

export function* handleApplicationVersionUpdated() {
  try {
    const params = { token: STATIC_TOKEN_FOR_CURRENT_VERSION };
    const { data } = yield call(
      baseRequest.get,
      GlobalWebServices.CHECK_APPLICATION_VERSION,
      { params }
    );
    const { currentVersion: currentVersionFromServer } = data;
    const isEqual = currentVersionFromServer === CURRENT_APPLICATION_VERSION;
    yield put(setIsApplicationVersionUpdatedWithSuccess(isEqual));
  } catch (e) {
    yield put(setIsApplicationVersionUpdatedWithError());
  }
}

export default function* globalSaga() {
  yield takeEvery(
    setIsApplicationVersionUpdated,
    handleApplicationVersionUpdated
  );
}
