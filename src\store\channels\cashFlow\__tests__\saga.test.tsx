import {call} from 'redux-saga/effects';
import {expectSaga} from 'redux-saga-test-plan';
import baseRequest from '@src/api/request';
import {handleCashFlowFeatures} from '@src/store/channels/cashFlow/saga';
import {ChannelsWebServices} from '@src/api/channels';
import {
  getCashFlowFeaturesWithError,
  getCashFlowFeaturesWithSuccess,
} from '@src/store/channels/cashFlow/slice';
import {CashFlowChannelSummary} from '@src/models/channelsSummary/cashChannelSummary';

describe('cashFlowChannelSaga', () => {
  const projectId = 22;
  const reportingDateFilter = {};
  const features: CashFlowChannelSummary = {
    channelStatus: 'PRIMARY',
    dso: 23,
    passDueContractorPayment: 23,
    passDueJesaPayment: 23,
  };
  it('should yield an api call', () => {
    expectSaga(handleCashFlowFeatures)
      .provide([
        [
          call(
            baseRequest.get,
            ChannelsWebServices.WS_GET_CASH_SUMMARY(projectId),
          ),
          {params: reportingDateFilter},
        ],
      ])
      .run();
  });

  it('should handle cash flow features successfully', () => {
    expectSaga(handleCashFlowFeatures)
      .provide([
        [
          call(
            baseRequest.get,
            ChannelsWebServices.WS_GET_CASH_SUMMARY(projectId),
          ),
          {params: reportingDateFilter},
        ],
      ])
      .put(getCashFlowFeaturesWithSuccess({features}))
      .run();
  });

  it('should handle cash flow features with error', () => {
    expectSaga(handleCashFlowFeatures)
      .provide([
        [
          call(
            baseRequest.get,
            ChannelsWebServices.WS_GET_CASH_SUMMARY(projectId),
          ),
          {throw: true},
        ],
      ])
      .put(getCashFlowFeaturesWithError())
      .run();
  });
});
