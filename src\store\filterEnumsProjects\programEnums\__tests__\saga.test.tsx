import {call} from 'redux-saga/effects';
import {expectSaga} from 'redux-saga-test-plan';
import baseRequest from '@src/api/request';
import {ProjectWebServices} from '@src/api/project';
import {handleProgramEnums} from '@src/store/filterEnumsProjects/programEnums/saga';
import ProgramSector from '@src/models/programSector';
import {
  getProgramEnumsWithError,
  getProgramEnumsWithSuccess,
} from '@src/store/filterEnumsProjects/programEnums/slice';

const programEnumsExamples: ProgramSector[] = [
  {
    type: {
      type: 'array',
      code: 'code',
      label: 'label',
      id: 2,
    },
    creationDate: '2015-12-12',
    name: 'name',
    updateDate: '2015-12-12',
    id: 2,
  },
];
describe('bu enums saga', () => {
  it('should yield an api call', () => {
    // @ts-ignore
    return expectSaga(handleProgramEnums)
      .provide([
        [call(baseRequest.get, ProjectWebServices.WS_GET_PROGRAM_ENUMS), {}],
      ])
      .run();
  });

  it('should handle program enums successfully', () => {
    // @ts-ignore
    expectSaga(handleProgramEnums)
      .provide([
        [call(baseRequest.get, ProjectWebServices.WS_GET_PROGRAM_ENUMS), {}],
      ])
      .put(getProgramEnumsWithSuccess(programEnumsExamples))
      .run();
  });

  it('should handle program enums with error', () => {
    // @ts-ignore
    expectSaga(handleProgramEnums)
      .provide([
        [
          call(baseRequest.get, ProjectWebServices.WS_GET_PROGRAM_ENUMS),
          {throw: true},
        ],
      ])
      .put(getProgramEnumsWithError())
      .run();
  });
});
