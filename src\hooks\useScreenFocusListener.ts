import React, {useEffect} from 'react';
import {useIsFocused, useNavigation} from '@react-navigation/core';
import {useDispatch} from 'react-redux';
import {ActionCreatorWithPayload} from '@reduxjs/toolkit';

type Action = {
  action: ActionCreatorWithPayload<any, any> | null;
  params?: any;
};

const useScreenFocusAndBlurListener = (
  focusActions: Action[],
  blurActions?: Action[],
) => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const isFocused = useIsFocused();

  const isDrawerOpen = false;

  // Only dispatch actions when screen is focused and drawer is closed
  // Use a ref to track if actions have been dispatched to prevent infinite loops
  const actionsDispatched = React.useRef(false);

  useEffect(() => {
    // Only dispatch actions if the screen is focused, drawer is closed, and actions haven't been dispatched yet
    if (isFocused && !isDrawerOpen && !actionsDispatched.current) {
      focusActions.forEach(({action, params}) => {
        if (action) {
          if (params) {
            dispatch(action(params));
          } else {
            dispatch(action({}));
          }
        }
      });
      // Mark actions as dispatched
      actionsDispatched.current = true;
    }

    // Reset the flag when screen is unfocused or drawer is opened
    if (!isFocused || isDrawerOpen) {
      actionsDispatched.current = false;
    }
  }, [isFocused, isDrawerOpen, dispatch, focusActions]);

  useEffect(() => {
    const blurSubscription = navigation.addListener('blur', () => {
      if (blurActions) {
        blurActions.forEach(({action, params}) => {
          if (action !== null) {
            dispatch(action(params));
          }
        });
      }
    });
    return () => {
      blurSubscription();
    };
  }, [navigation, blurActions, dispatch]);
};

export default useScreenFocusAndBlurListener;
