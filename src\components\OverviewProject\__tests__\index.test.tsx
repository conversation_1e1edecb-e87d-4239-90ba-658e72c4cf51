import {renderWithProviders} from '@src/utils/utils-for-tests';
import React from 'react';
import OverviewProject from '@src/components/OverviewProject';
import {Project} from '@src/models/project';
import {TEST_IDS} from '@src/constants/strings';

describe('OverviewProject', function () {
  const toggleDrawer = jest.fn();

  const projectExample: Project = {
    bu: '',
    clientLogo: '',
    clientName: '',
    forecastDate: '',
    id: 0,
    name: '',
    number: '',
    overallStatus: 'PRIMARY',
    picture: '',
    program: '',
    projectSize: '',
    reportingDate: '',
    sector: '',
    startDate: '',
  };
  it('Should render and match the snapshot', () => {
    const tree = renderWithProviders(
      <OverviewProject
        toggleDrawer={toggleDrawer}
        currentProject={projectExample}
        period={{
          year: 2022,
          month: 7,
        }}
      />,
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });

  it('Should render the overview project when the current project is not null', () => {
    const {getByTestId} = renderWithProviders(
      <OverviewProject
        toggleDrawer={toggleDrawer}
        currentProject={projectExample}
        period={{
          year: 0,
          month: 0,
        }}
      />,
    );
    const container = getByTestId(TEST_IDS.VIEW_OVERVIEW_PROJECT);
    expect(container).toBeDefined();
  });
});
