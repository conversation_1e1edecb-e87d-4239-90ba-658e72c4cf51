import { ROUTE_NAMES } from "@src/constants/navigation";
import { CommonActions, createNavigationContainerRef } from "@react-navigation/native";
import { MobileAlertType } from "@src/constants/notification";
import NavigationService from "@src/services/NavigationService";

/**
 * Navigation reference used by React Navigation
 */
export const navigationRef = createNavigationContainerRef();

/**
 * Navigate to a specific screen with optional parameters
 * @param name Screen name to navigate to
 * @param params Optional parameters to pass to the screen
 */
export const navigate = (name: string, params?: Record<string, unknown>) => {
  NavigationService.navigate(name, params);
};

/**
 * Reset the navigation stack to a specific list of routes.
 * This function creates a complete navigation stack for proper back navigation.
 *
 * @param params Navigation parameters including projectId and notification type
 */
export const resetStack = (params: {
  projectId: number | null | undefined;
  mobileAlertType: string | undefined;
  objectType?: string;
  objectId?: number;
}) => {
  let routes = [];

  // Always include HOME as the base route
  routes.push({
    name: ROUTE_NAMES.HOME
  });

  // Add PROJECT_HEALTH_DETAILS if we have a projectId
  if (params?.projectId) {
    routes.push({
      name: ROUTE_NAMES.PROJECT_HEALTH_DETAILS,
      params: { projectId: params.projectId }
    });
  }

  // Add NOTIFICATIONS for context
  routes.push({
    name: ROUTE_NAMES.NOTIFICATIONS
  });

  // Add DISCUSSION_DETAILS for redirection notifications
  if (params?.mobileAlertType === MobileAlertType.REDIRECTION) {
    routes.push({
      name: ROUTE_NAMES.DISCUSSION_DETAILS,
      params: {
        objectType: params.objectType,
        objectId: params.objectId,
        fromNotification: true // Flag to indicate this came from a notification
      }
    });
  }

  // Reset the navigation stack with our new routes
  navigationRef?.current?.dispatch(
    CommonActions.reset({
      index: routes.length - 1,
      routes: routes
    })
  );
};

/**
 * Navigate back to the previous screen
 */
export const navigatePop = (routeName?: string) => {
  NavigationService.goBack();
};
