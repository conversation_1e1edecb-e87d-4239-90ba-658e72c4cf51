import React from "react";
import { renderWithProviders } from "@src/utils/utils-for-tests";
import { ListInteractionsWithAddCommentInput } from "@src/containers/ListInteractionsWithAddCommentInput";
import { Discussion } from "@src/models/discussion";

describe("List Interactions With Add Comment Input", () => {
  it("should render when interactions are not null and match the snapshot", () => {
    const discussion: Discussion = {
      interactions: [],
      discussionId: 2,
      objectId: 2,
      type: "TYPE",
      id: 2,
      description: "description",
      title: "title",
      periodOfData: {
        month: 2,
        year: 2
      }
    };
    const tree = renderWithProviders(
      <ListInteractionsWithAddCommentInput
        objectId={2}
        discussion={discussion}
        objectTypeMapped={"TYPE"}
      />,
      {}
    );
    expect(tree).toMatchSnapshot();
  });
});
