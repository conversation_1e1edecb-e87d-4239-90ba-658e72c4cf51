import { View } from "react-native";
import { styles } from "./style";
import React from "react";
import { useSelector } from "react-redux";
import { discussionSelector } from "@src/store/discussion/selectors";
import { DISCUSSION_OBJECT_TYPES } from "@src/constants/notification";
import DiscussionActionBody from "@src/components/DiscussionSummaryBody/DiscussionActionBody";
import DiscussionKPIBody from "@src/components/DiscussionSummaryBody/DiscussionKPIBody";

const renderNotificationSummaryBody = (type: string) => {
  switch (type) {
    case DISCUSSION_OBJECT_TYPES.ACTION:
      return <DiscussionActionBody />;
    case DISCUSSION_OBJECT_TYPES.RISK:
      return <DiscussionActionBody />;
    case DISCUSSION_OBJECT_TYPES.PROGRESS:
      return <DiscussionKPIBody />;
    default:
      return <DiscussionActionBody />;
  }
};
const DiscussionSummaryBody = () => {
  const { discussion } = useSelector(discussionSelector);
  return (
    <View style={styles.notificationSummaryInfo}>
      {renderNotificationSummaryBody(discussion?.type ?? DISCUSSION_OBJECT_TYPES.ACTION)}
    </View>
  );
};

export default DiscussionSummaryBody;
