import {useEffect} from 'react';
import {useDispatch} from 'react-redux';
import {useIsFocused} from '@react-navigation/core';
import {AnyAction} from 'redux';

export const useGetChannelFeatures = (props: {
  focusAction: AnyAction;
  blurAction: AnyAction;
}) => {
  const dispatch = useDispatch();
  const {focusAction, blurAction} = props;
  const isFocused = useIsFocused();
  useEffect(() => {
    if (isFocused) {
      dispatch(focusAction);
    }
    return () => {
      dispatch(blurAction);
    };
  }, []);
};
