import styles from '@src/components/Drawer/style';
import {AllProjects} from '@src/svg/drawer/AllProjects';
import React from 'react';
import {Notification} from '@src/svg/drawer/Notification';
import {AllProjectsUnfocused} from '@src/svg/drawer/AllProjectsUnfocused';
import {NotificationUnfocused} from '@src/svg/drawer/NotificationUnfocused';

export const ROUTE_NAMES = {
  HOME: 'All Projects',
  NOTIFICATIONS: 'Notifications',
  DISCUSSION_DETAILS: 'Discussion Details',
  ACTIONS: 'Actions',
  DISCUSSIONS: 'Discussions',
  SETTINGS: 'Settings',
  SPLASH_SCREEN: 'Splash',
  PROJECT_HEALTH_DETAILS: 'Project Health',
  LOGIN_SCREEN: 'Login Screen',
};

export type DrawerItem = {
  name: string;
  destination: string;
  iconFocused: any;
  iconUnfocused: any;
};

export const drawerItems: DrawerItem[] = [
  {
    name: ROUTE_NAMES.HOME,
    destination: ROUTE_NAMES.HOME,
    iconFocused: <AllProjects style={styles.iconImage} />,
    iconUnfocused: <AllProjectsUnfocused style={styles.iconImage} />,
  },
  {
    name: ROUTE_NAMES.PROJECT_HEALTH_DETAILS,
    destination: ROUTE_NAMES.PROJECT_HEALTH_DETAILS,
    iconFocused: <AllProjects style={styles.iconImage} />,
    iconUnfocused: <AllProjectsUnfocused style={styles.iconImage} />,
  },
  {
    name: ROUTE_NAMES.NOTIFICATIONS,
    destination: ROUTE_NAMES.NOTIFICATIONS,
    iconFocused: <Notification style={styles.iconImage} />,
    iconUnfocused: <NotificationUnfocused style={styles.iconImage} />,
  },
];
