import { createSlice } from "@reduxjs/toolkit";
import { SLICES_NAMES } from "@src/constants/store";
import { HumanCapitalChannelSummary } from "@src/models/channelsSummary/humanCapitalChannelSummary";

export type HumanCapSliceType = {
  features: HumanCapitalChannelSummary | null;
  channelStatus: string;
  error: boolean;
  success: boolean;
  loading: boolean;
};

export const initialState: HumanCapSliceType = {
  features: null,
  channelStatus: "PRIMARY",
  error: false,
  success: false,
  loading: false
};
export const slice = createSlice({
  name: SLICES_NAMES.HUMAN_CAPITAL_CHANNEL,
  initialState,
  reducers: {
    getHumanCapFeatures: state => {
      return {
        ...state,
        loading: true,
        error: false,
        success: false
      };
    },
    getHumanCapFeaturesWithSuccess: (
      state,
      action: {
        payload: {
          features: HumanCapitalChannelSummary;
        };
      }
    ) => {
      let finalChannelStatus: any;
      if (action.payload.features.channelStatus !== null) {
        finalChannelStatus = action.payload.features.channelStatus!!;
      } else {
        finalChannelStatus = "PRIMARY";
      }
      return {
        ...state,
        features: action.payload.features,
        channelStatus: finalChannelStatus,
        loading: false,
        error: false,
        success: true
      };
    },
    getHumanCapFeaturesWithError: state => {
      return {
        ...state,
        features: null,
        channelStatus: "PRIMARY",
        loading: false,
        error: true,
        success: false
      };
    },
    cleanUpHumanCap: () => {
      return initialState;
    }
  }
});

export const {
  getHumanCapFeatures,
  getHumanCapFeaturesWithSuccess,
  getHumanCapFeaturesWithError,
  cleanUpHumanCap
} = slice.actions;
export default slice.reducer;
