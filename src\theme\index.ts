import {DefaultTheme} from '@react-navigation/native';
import {CustomColors, darkColors, lightColors} from '@src/theme/colors';
import typography, {Typography} from '@src/theme/fonts';

export type CustomTheme = typeof DefaultTheme & {
  fonts: Typography;
  colors: CustomColors;
};
export const lightTheme: CustomTheme = {
  ...DefaultTheme,
  fonts: {
    ...typography,
  },
  colors: {
    ...DefaultTheme.colors,
    ...lightColors,
  },
  dark: false,
};

export const darkTheme: CustomTheme = {
  ...DefaultTheme,
  fonts: {
    ...typography,
  },
  colors: {
    ...DefaultTheme.colors,
    ...darkColors,
  },
  dark: true,
};

export interface StyledComponentTheme {
  theme: CustomTheme;
}
