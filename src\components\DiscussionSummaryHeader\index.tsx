import {Text, View} from 'react-native';
import {styles} from './style';
import {discussionHeaderIcon} from '@src/utils/imageUtils';
import React from 'react';
import {useSelector} from 'react-redux';
import {discussionSelector} from '@src/store/discussion/selectors';
import {DiscussionUtils} from '@src/utils/discussionUtils';
import {DISCUSSION_OBJECT_TYPES} from '@src/constants/notification';

const DiscussionSummaryHeader = () => {
  const {discussion} = useSelector(discussionSelector);
  const IconNotification = discussionHeaderIcon(
    discussion?.type ?? DISCUSSION_OBJECT_TYPES.RISK,
  );
  const subTitle = DiscussionUtils.discussionSubTitle(
    discussion?.type ?? DISCUSSION_OBJECT_TYPES.RISK,
  );
  return (
    <View style={styles.channelIconAndTitle}>
      <View style={styles.icon}>{IconNotification}</View>
      <Text style={styles.typeDiscussion}>
        {discussion?.title ?? 'No title'}
      </Text>
      <Text style={styles.typeDiscussionTitle}>{subTitle}</Text>
    </View>
  );
};

export default DiscussionSummaryHeader;
