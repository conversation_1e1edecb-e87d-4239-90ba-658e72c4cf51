import {PixelRatio} from 'react-native';

const fontScale = PixelRatio.getFontScale();

export type FontWeight =
  | 'normal'
  | 'bold'
  | '100'
  | '200'
  | '300'
  | '400'
  | '500'
  | '600'
  | '700'
  | '800'
  | '900'
  | undefined;

export interface Typography {
  letterSpacings: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    '2xl': string;
  };
  lineHeights: {
    xxs: string;
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    '2xl': string;
    '3xl': string;
    '4xl': string;
    '5xl': string;
  };
  fontFamily: {
    vinaSansRegular: string;
    montserratRegular: string;
    montserratBlack: string;
    montserratBold: string;
    montserratSemiBold: string;
  };
  fontWeight: {
    bold: string;
    italic: string;
    normal: string;
    100: string;
    200: string;
    300: string;
    400: string;
    500: string;
    600: string;
    700: string;
    800: string;
    900: string;
  };
  fonts: {
    heading: string;
    body: string;
    mono: string;
  };
  fontSizes: {
    xxs: number;
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
    '2xl': number;
    '3xl': number;
    '4xl': number;
    '5xl': number;
    '6xl': number;
    '7xl': number;
    '8xl': number;
    '9xl': number;
  };
}

const typography: Typography = {
  letterSpacings: {
    xs: '-0.05em',
    sm: '-0.025em',
    md: '0',
    lg: '0.025em',
    xl: '0.05em',
    '2xl': '0.1em',
  },
  lineHeights: {
    xxs: '1em',
    xs: '1.125em',
    sm: '1.25em',
    md: '1.375em',
    lg: '1.5em',
    xl: '1.75em',
    '2xl': '2em',
    '3xl': '2.5em',
    '4xl': '3em',
    '5xl': '4em',
  },
  fontFamily: {
    vinaSansRegular: 'VinaSans-Regular',
    montserratRegular: 'Montserrat-Regular',
    montserratBlack: 'Montserrat-Black',
    montserratBold: 'Montserrat-Bold',
    montserratSemiBold: 'Montserrat-SemiBold',
  },
  fontWeight: {
    bold: 'bold',
    normal: 'normal',
    italic: 'italic',
    '100': '100',
    '200': '200',
    '300': '300',
    '400': '400',
    '500': '500',
    '600': '600',
    '700': '700',
    '800': '800',
    '900': '900',
  },
  fonts: {
    heading: 'Lato',
    body: 'Lato',
    mono: 'Lato',
  },
  fontSizes: {
    xxs: 10 * fontScale,
    xs: 12 * fontScale,
    sm: 14 * fontScale,
    md: 16 * fontScale,
    lg: 18 * fontScale,
    xl: 20 * fontScale,
    '2xl': 24 * fontScale,
    '3xl': 30 * fontScale,
    '4xl': 36 * fontScale,
    '5xl': 48 * fontScale,
    '6xl': 60 * fontScale,
    '7xl': 72 * fontScale,
    '8xl': 96 * fontScale,
    '9xl': 128 * fontScale,
  },
};

export default typography;
