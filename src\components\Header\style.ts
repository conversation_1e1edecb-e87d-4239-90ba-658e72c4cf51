import {StyleSheet} from 'react-native';
import layout from '@src/theme/layout';
import {correspondentHeight, correspondentWidth} from '@src/utils/imageUtils';
import typography from '@src/theme/fonts';
import {globalStyles} from '@src/theme/style';

const style = StyleSheet.create({
  headerContainer: {
    ...layout.rowSpaceBetweenCenter,
    marginBottom: correspondentHeight(10),
    ...globalStyles.screenContainerPaddingTop,
  },
  backButton: {
    padding: correspondentHeight(20),
  },
  centerContent: {
    flex: 1,
    alignItems: 'center',
  },
  routeName: {
    fontSize: typography.fontSizes.lg,
    fontWeight: 'bold',
  },
  rightContent: {
    ...layout.rowCenter,
  },
  routeContainer: {
    ...layout.rowSpaceBetweenCenter,
  },
  logoImage: {
    width: correspondentWidth(38),
    marginEnd: correspondentWidth(15),
    aspectRatio: 1,
  },
});

export default style;
