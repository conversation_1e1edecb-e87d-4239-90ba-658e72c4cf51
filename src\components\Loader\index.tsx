import React, {memo} from 'react';
import {Animated, Modal, View} from 'react-native';
import {styles} from '@src/components/Loader/style';
import {BlurView} from '@react-native-community/blur';
import {Easing} from 'react-native-reanimated';
import {TEST_IDS} from '@src/constants/strings';
import {CollabLogo} from '@src/svg/CollabLogo';

const Loader = (props: {show: boolean}) => {
  const {show} = props;
  const bounceValue = React.useRef(new Animated.Value(1)).current;

  React.useEffect(() => {
    const animateBounce = () => {
      Animated.loop(
        Animated.sequence([
          Animated.timing(bounceValue, {
            toValue: 1.2, // Scale up to 1.2
            duration: 1000,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
          Animated.timing(bounceValue, {
            toValue: 1, // Scale back to 1
            duration: 1000,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
        ]),
      ).start();
    };

    animateBounce();
  }, [bounceValue]);
  return (
    <Modal transparent visible={show} animationType="fade">
      <View style={styles.container} testID={TEST_IDS.VIEW_LOADER}>
        <BlurView
          style={styles.blurContainer}
          blurType="light"
          blurAmount={10}
        />
        <Animated.View
          style={[styles.image, {transform: [{scale: bounceValue}]}]}>
          <CollabLogo style={styles.image} />
        </Animated.View>
      </View>
    </Modal>
  );
};

export default memo(Loader);
