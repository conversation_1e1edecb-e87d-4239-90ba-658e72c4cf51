import React from 'react';
import BottomCarousel from '@src/components/BottomCarousel';
import {renderWithProviders} from '@src/utils/utils-for-tests';
import {ChannelModel} from '@src/components/CarouselChannels';
import {IMAGES} from '@src/constants/images';

describe('BottomCarousel', () => {
  const updateCarouselPosition = jest.fn();
  const allowedChannels: ChannelModel[] = [
    {
      title: 'Sample Channel',
      isDisplayed: true,
      uri: 'https://example.com',
      content: {
        description: 'Sample channel description',
        category: 'Sample category',
      },
      smallIconFocused: IMAGES.USER_AVATAR,
      smallIconUnFocused: IMAGES.USER_AVATAR,
      selector: {id: 1, name: 'Sample Selector'},
    },
  ];
  const currentPosition = 0;
  it('Should Render and match the snapshot', () => {
    const tree = renderWithProviders(
      <BottomCarousel
        allowedChannels={allowedChannels}
        updateCarouselPosition={updateCarouselPosition}
        currentPosition={currentPosition}
      />,
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
