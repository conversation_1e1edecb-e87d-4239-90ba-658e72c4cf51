import ReactNativeBlobUtil from 'react-native-blob-util';
import {useSelector} from 'react-redux';
import {authenticationSelector} from '@src/store/authentication/selectors';
import {useEffect, useState} from 'react';
import {userAvatarUrl} from '@src/api/request';

type UserGetUserAvatarProps = {
  userIdProps?: string;
};
const useGetUserAvatar = (props: UserGetUserAvatarProps) => {
  const {userIdProps} = props;
  const {userToken, userId} = useSelector(authenticationSelector);
  const [userAvatar, setUserAvatar] = useState<string>('');

  let finalUserId = '';
  if (userIdProps !== undefined) {
    finalUserId = userIdProps;
  } else {
    finalUserId = userId!!;
  }

  const loadUserAvatar = async () => {
    try {
      const reactNativeBlobUtil = await ReactNativeBlobUtil.fetch(
        'GET',
        userAvatarUrl(finalUserId),
        {
          Authorization: `Bearer ${userToken}`,
        },
      );
      try {
        JSON.parse(reactNativeBlobUtil.data);
        setUserAvatar('');
      } catch (e) {
        setUserAvatar(reactNativeBlobUtil.data);
      }
    } catch (err) {
      setUserAvatar('');
    }
  };

  useEffect(() => {
    loadUserAvatar();
  }, []);

  return userAvatar;
};

export default useGetUserAvatar;
