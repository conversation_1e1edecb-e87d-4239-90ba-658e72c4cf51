import {authenticationSelector} from '@src/store/authentication/selectors';
import {RootState} from '@src/store/store';

describe('authenticationSelector', () => {
  it('should select the authentication state from the root state', () => {
    // Arrange
    const authenticationState = {
      isAuthenticated: true,
      user: {
        id: 1,
        name: '<PERSON>',
      },
    };

    // @ts-ignore
    const mockRootState: RootState = {
      authentication: authenticationState,
      // Add other state properties if needed
    };

    // Act
    const selectedState = authenticationSelector(mockRootState);

    // Assert
    expect(selectedState).toEqual(authenticationState);
  });
});
