import {globalStyles} from '@src/theme/style';
import ClickableImage from '@src/components/ClickableImage';
import React from 'react';
import {TEST_IDS} from '@src/constants/strings';
import {CalendarIcon} from '@src/svg/CalendarIcon';

type CalendarIconHeaderProps = {
  onPress: () => void;
};
export const CalendarIconHeader = (props: CalendarIconHeaderProps) => {
  const {onPress} = props;
  return (
    <ClickableImage
      testID={TEST_IDS.CALENDAR_ICON}
      onPress={onPress}
      url={null}
      imageStyle={globalStyles.calendarIcon}
      SvgComponent={<CalendarIcon style={globalStyles.calendarIcon} />}
    />
  );
};
