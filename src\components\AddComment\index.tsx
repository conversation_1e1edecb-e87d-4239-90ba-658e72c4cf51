import { KeyboardAvoidingView, Platform, TextInput, View } from "react-native";
import { styles } from "./style";
import { IMAGES } from "@src/constants/images";
import React, { FC, useState } from "react";
import ClickableImage from "@src/components/ClickableImage";
import layout from "@src/theme/layout";
import { PLACEHOLDERS, TEST_IDS } from "@src/constants/strings";
import { MentionInput, MentionSuggestionsProps } from "react-native-controlled-mentions";
import { useSelector } from "react-redux";
import { discussionSelector } from "@src/store/discussion/selectors";
import { projectSelector } from "@src/store/project/selectors";
import Loader from "@src/components/Loader";
import { ObjectUtils } from "@src/utils/objectUtils";
import Image from "@src/components/ScalableImage";
import { FlatList } from "react-native-gesture-handler";
import { SuggestionItem } from "@src/components/SuggestionItem";
import { Send } from "@src/svg/Send";
import { COMMON_COLORS } from "@src/theme/colors";
import { windowHeight } from "@src/constants";
import useGetUserAvatar from "@src/hooks/useGetUserAvatar";
import { returnFinalBase64String } from "@src/utils/imageUtils";
import ToastMessage from "@src/components/Toast";

type AddCommentProps = {
  handleUpdateListInteractions: (
    text: string,
    focusedInteractionId: number | null,
    type: string,
    projectId: number
  ) => void;
  textInputRef: React.RefObject<TextInput>;
  focusedInteractionId: number;
  handleSettingFocusedInteraction: () => void;
  userId: string;
};
export const AddComment = (props: AddCommentProps) => {
  const {
    handleUpdateListInteractions,
    textInputRef,
    focusedInteractionId,
    userId
  } = props;

  const { discussion, users } = useSelector(discussionSelector);
  const { projectId } = useSelector(projectSelector);


  const userAvatarFromHook = useGetUserAvatar({
    userIdProps: userId
  });

  const renderSuggestions: FC<MentionSuggestionsProps> = ({
                                                            keyword,
                                                            onSuggestionPress
                                                          }) => {
    if (keyword == null || users === null) {
      return null;
    }

    const usersFiltered = users.filter(one =>
      one.userProject.name?.toLocaleLowerCase().includes(keyword.toLocaleLowerCase())
    );

    const number_users_filtered = usersFiltered.length;

    const max_height_list_tagged_users = windowHeight * 0.236;

    const one_user_filtered_height = windowHeight * 0.045;

    const visible_users_filtered = 5;

    const height_or_top_value =
      number_users_filtered > visible_users_filtered
        ? max_height_list_tagged_users
        : number_users_filtered * one_user_filtered_height;

    if (usersFiltered.length == 0) {
      return (
        <ToastMessage type={"info"} message={"No Users Found in this conversation"} />
      );
    }

    return (
      <FlatList
        contentContainerStyle={
          styles.flat_list_filtered_users_content_container_style
        }
        style={{
          top: -height_or_top_value,
          height: height_or_top_value,
          ...styles.flat_list_filtered_users
        }}
        keyExtractor={(item, index) => (item.userProject.userId.toString() + index)}
        data={usersFiltered}
        removeClippedSubviews={false}
        renderItem={({ item: user }) => {
          const newSuggestionObject = ObjectUtils.mapToSuggestion(user);
          return (
            <SuggestionItem
              newSuggestionObject={newSuggestionObject}
              onSuggestionPress={onSuggestionPress}
              userId={user.userProject.userId}
              displayName={user.userProject.name}
            />
          );
        }}
      />
    );
  };

  const [value, setValue] = useState("");
  const handleInputChange = (text: string) => {
    console.log(text);
    // Check if text length is decreasing (i.e., user is deleting)
    if (text.length < value.length) {
      const regex = /@\[.*?\]\(.*?\)/g;
      const matches = value.match(regex);

      if (matches) {
        const lastMatch = matches[matches.length - 1];
        // Find the position of the last mention in the value
        const startIndex = value.indexOf(lastMatch);
        const endIndex = startIndex + lastMatch.length;

        // Check if the cursor is within the mention (especially near the closing parenthesis)
        if (text.length < endIndex && text.length > startIndex) {
          // Remove the entire mention
          text = value.substring(0, startIndex) + value.substring(endIndex);
        }
      }
    }
    setValue(text);
  };

  if (users === null) {
    return <Loader show={true} />;
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={styles.container}>
      <View style={layout.rowHCenter}>
        <View style={styles.imageContainer}>
          {userAvatarFromHook.length > 0 ? (
            <Image
              style={styles.profileImage}
              source={{ uri: returnFinalBase64String(userAvatarFromHook) }}
            />
          ) : (
            <Image style={styles.profileImage} source={IMAGES.USER_AVATAR} />
          )}
        </View>
        <View style={styles.width_text_input}>
          <MentionInput
            testID={TEST_IDS.COMMENT_INPUT}
            placeholder={PLACEHOLDERS.MENTION_INPUT_TEXT}
            placeholderTextColor={COMMON_COLORS.GREY}
            style={{ color: COMMON_COLORS.BLACK }}
            inputRef={textInputRef}
            value={value}
            onChange={handleInputChange}
            collapsable={false}
            showSoftInputOnFocus={true}
            focusable={false}
            blurOnSubmit={true}
            autoFocus={false}
            enablesReturnKeyAutomatically={true}
            autoCorrect={true}
            partTypes={[
              {
                trigger: "@",
                renderSuggestions,
                textStyle: {
                  fontWeight: "bold",
                  color: COMMON_COLORS.BLUE
                },
                isInsertSpaceAfterMention: true
              }
            ]}
          />
        </View>
      </View>

      <View style={layout.rowHCenter}>
        <ClickableImage
          imageStyle={styles.iconContainer}
          onPress={() =>
            handleUpdateListInteractions(
              value,
              focusedInteractionId,
              discussion?.type ?? "",
              projectId ?? 0
            )
          }
          url={null}
          testID={TEST_IDS.SEND_ICON}
          SvgComponent={<Send style={styles.iconContainer} />}
        />
      </View>
    </KeyboardAvoidingView>
  );
};
