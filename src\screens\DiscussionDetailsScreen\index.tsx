import {
  ListDiscussionInteractionsWithHeaderContainer
} from "@src/containers/ListDiscussionInteractionsWithHeaderContainer";
import React, { useEffect } from "react";
import { useIsFocused } from "@react-navigation/core";
import { useRoute } from "@react-navigation/native";
import { View } from "react-native";
import { ActivityIndicatorLoader } from "@src/components/ActivityIndicatorLoader";
import { styles } from "./style";

/**
 * DiscussionDetailsScreen displays detailed information about a discussion
 * and its interactions. It's designed to work seamlessly with the navigation
 * stack when opened from push notifications.
 *
 * The navigation stack is maintained as:
 * [HOME] -> [PROJECT_HEALTH_DETAILS] -> [NOTIFICATIONS] -> [DISCUSSION_DETAILS]
 *
 * This ensures users can navigate back through the logical hierarchy of screens.
 */
type DiscussionDetailsScreenProps = {
  toggleDrawer: () => void;
};

const DiscussionDetailsScreen: React.FC<DiscussionDetailsScreenProps> = ({
  toggleDrawer
}) => {
  const isFocused = useIsFocused();
  const route = useRoute();

  // Track when the screen comes into focus for analytics or other side effects
  useEffect(() => {
    if (isFocused) {
      // Could add analytics tracking or other focus-related logic here
    }
  }, [isFocused]);

  // Always render the component structure, but conditionally render content
  return (
    <View style={styles.container}>
        {!isFocused ? (
          <ActivityIndicatorLoader error={true} />
        ) : (
          <ListDiscussionInteractionsWithHeaderContainer
            toggleDrawer={toggleDrawer}
            useRouteHooks={route}
          />
        )}
    </View>
  );
};

export default DiscussionDetailsScreen;
