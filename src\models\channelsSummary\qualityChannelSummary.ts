import {Type} from 'class-transformer';
import 'reflect-metadata';

export interface QualityChannelSummary {
  clientSatisfaction?: number;
  channelStatus?: string | null;
  capasRiskLevels?: CapasRiskLevels;
  averageOpenDays?: number;
}

export class CapasRiskLevels {
  public id?: number;
  public low?: number;
  public medium?: number;
  public high?: number;
  public veryHigh?: number;
  public creationDate?: string;
  public updateDate?: string;
}

export class PinDetails {
  public id?: number;
  public openPins?: number;
  public closedPins?: number;
  @Type(() => PinsSource)
  public pinsSource?: PinsSource;
  public creationDate?: string;
  public updateDate?: string;
}

export class PinsSource {
  public id?: number;
  public type?: string;
  public code?: string;
  public label?: string;
  public creationDate?: string;
  public updateDate?: string;
}
