import reducer, {
  cleanUpCost,
  getCostFeatures,
  getCostFeaturesWithError,
  getCostFeaturesWithSuccess,
  initialState,
} from '@src/store/channels/cost/slice';
import {CostChannelSummary} from '@src/models/channelsSummary/costChannelSummary';

const features: CostChannelSummary = {
  channelStatus: 'PRIMARY',
};
describe('Cost reducer', () => {
  it('should return the initial state', () => {
    expect(reducer(undefined, {type: undefined})).toEqual(initialState);
  });

  it('should handle getCostFeatures action', () => {
    const action = getCostFeatures();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      loading: true,
      error: false,
      success: false,
    });
  });

  it('should handle getCostFeaturesWithSuccess action when the channel status is not null', () => {
    const action = getCostFeaturesWithSuccess({features});
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      features: action.payload.features,
      channelStatus: action.payload.features.channelStatus,
      loading: false,
      error: false,
      success: true,
    });
  });

  it('should handle getCostFeaturesWithSuccess action when the channel status is null', () => {
    const action = getCostFeaturesWithSuccess({
      features: {
        ...features,
        channelStatus: null,
      },
    });
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      features: action.payload.features,
      loading: false,
      error: false,
      success: true,
    });
  });

  it('should handle getCostFeaturesWithError action', () => {
    const action = getCostFeaturesWithError();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      loading: false,
      error: true,
      success: false,
    });
  });

  it('should handle cleanUp action', () => {
    const action = cleanUpCost();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
    });
  });
});
