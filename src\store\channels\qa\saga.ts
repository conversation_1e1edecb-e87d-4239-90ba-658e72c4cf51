import {call, put, select, takeEvery} from 'redux-saga/effects';

import {projectSelector} from '@src/store/project/selectors';
import baseRequest from '@src/api/request';
import {ChannelsWebServices} from '@src/api/channels';
import {
  getQAFeatures,
  getQAFeaturesWithError,
  getQAFeaturesWithSuccess,
} from '@src/store/channels/qa/slice';

export function* handleQaFeatures() {
  try {
    const {projectId, reportingDateFilter} = yield select(projectSelector);
    const {data} = yield call(
      baseRequest.get,
      ChannelsWebServices.WS_GET_QUALITY_SUMMARY(projectId),
      {params: reportingDateFilter},
    );
    yield put(getQAFeaturesWithSuccess({features: data}));
  } catch (e) {
    yield put(getQAFeaturesWithError());
  }
}

export default function* QaSaga() {
  // same as incrementNumber.type or incrementNumber
  yield takeEvery(getQAFeatures, handleQaFeatures);
}
