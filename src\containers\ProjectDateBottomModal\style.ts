import {StyleSheet} from 'react-native';
import {PERCENTAGES} from '@src/constants';
import typography from '@src/theme/fonts';
import {COMMON_COLORS} from '@src/theme/colors';
import layout from '@src/theme/layout';
import {correspondentHeight, correspondentWidth} from '@src/utils/imageUtils';

export const bottomSheetStyles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  twoButtonsContainer: {
    marginTop: correspondentHeight(30),
    ...layout.rowVCenterSpaceBetween,
  },
  content: {
    backgroundColor: COMMON_COLORS.WHITE,
    padding: 20,
    paddingTop: correspondentHeight(10),
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  closeButton: {
    alignSelf: 'flex-end',
    marginBottom: correspondentHeight(10),
  },
  closeText: {
    fontSize: typography.fontSizes.lg,
    color: COMMON_COLORS.BLACK,
  },
  button: {
    flex: 1,
    padding: 12,
    paddingHorizontal: correspondentWidth(26),
    alignItems: 'center',
    borderRadius: 14,
    width: PERCENTAGES['50'],
    backgroundColor: COMMON_COLORS.GREY_10,
  },
  buttonText: {
    color: COMMON_COLORS.WHITE,
    fontSize: typography.fontSizes.sm - 1,
    fontWeight: '700',
  },
  line: {
    width: '28%',
    height: correspondentHeight(4),
    backgroundColor: COMMON_COLORS.GREY_15,
    alignSelf: 'center',
    marginVertical: correspondentHeight(12),
    borderRadius: 11,
  },
  filterFieldsTitle: {
    fontSize: typography.fontSizes.lg,
    color: COMMON_COLORS.BLUE_40,
    fontWeight: '700',
    fontFamily: typography.fontFamily.montserratSemiBold,
  },
  filterFieldsSubTitle: {
    fontSize: typography.fontSizes.sm,
    color: COMMON_COLORS.SEMI_LIGHT_BLUE,
    fontWeight: '700',
    fontFamily: typography.fontFamily.montserratRegular,
  },
  applyButton: {
    flex: 1,
    padding: 12,
    paddingHorizontal: correspondentWidth(26),
    alignItems: 'center',
    borderRadius: 14,
    width: PERCENTAGES['50'],
    marginLeft: correspondentWidth(16),
    backgroundColor: COMMON_COLORS.PRIMARY,
  },
});
