# Collab Mobile

Collab mobile project using the react native cli instead of expo

## 👋🏽Getting started

```
cd existing_repo
git remote add origin https://git.neoxia-maroc.net/yessine.jaoua/collabmobilecli.git
git branch -M main
git push -uf origin main
```

## 🛣️Development Environment

There are three environments available in the project: `dev`, `recette`, and `preprod`. Each flavor
represents the same core features and functionalities, with the only difference being the required build keys specific
to each environment.

To start your development journey you can switch to `devDebug`

## Installation

- Request `.env.correponding_env` like `.env.dev` while using the dev env ,file from your supervisor and put it in the
  project root directory:
  ~\collabmobilecli\.
  `.env.correponding_env` should contain the following variables:

```
AZURE_AD_TENANT_ID=
API_BASE_URL
PROXY_COLLAB=
COLLAB_MOBILE_API_TOKEN=
COLLAB_MOBILE_FIREBASE_TESTERS_GROUP=
COLLAB_MOBILE_VERSION_MIN=
COLLAB_MOBILE_VERSION_NAME=
COLLAB_MOBILE_VERSION_PATCH=
KEY_ALIAS=
KEY_PASSWORD=
KEYSTORE_PASSWORD=
```

## Versioning

To handle versions visit versions.properties file
VERSION_NAME=
VERSION_MIN=
VERSION_PATCH=

## Test and Deploy

## Authors and acknowledgment

Show your appreciation to those who have contributed to the project.
