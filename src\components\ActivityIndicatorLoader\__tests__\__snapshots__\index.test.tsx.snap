// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Loader Should render and match the snapshot 1`] = `
<Modal
  animationType="fade"
  hardwareAccelerated={false}
  transparent={true}
  visible={true}
>
  <View
    style={
      {
        "alignItems": "center",
        "backgroundColor": "white",
        "flex": 1,
        "justifyContent": "center",
        "opacity": 0.9,
      }
    }
    testID="View Loader"
  >
    <BlurView
      blurAmount={10}
      blurType="light"
      reducedTransparencyFallbackColor="white"
      style={
        [
          {
            "backgroundColor": "transparent",
          },
          {
            "bottom": 0,
            "left": 0,
            "position": "absolute",
            "right": 0,
            "top": 0,
          },
        ]
      }
    />
    <Image
      collapsable={false}
      source={
        {
          "testUri": "../../../assets/images/collab_logo_image.png",
        }
      }
      style={
        {
          "borderRadius": 40,
          "height": 80,
          "transform": [
            {
              "scale": 1,
            },
          ],
          "width": 80,
        }
      }
    />
  </View>
</Modal>
`;
