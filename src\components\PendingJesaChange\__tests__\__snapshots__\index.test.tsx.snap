// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`PendingJesaChange Should render and match the snapshot 1`] = `
<View
  style={
    {
      "flexDirection": "row",
    }
  }
>
  <Text
    style={
      {
        "color": "COMMON_COLORS.SEMI_LIGHT_BLUE",
        "fontSize": 14,
        "fontWeight": "bold",
      }
    }
  >
    2
  </Text>
  <Text
    style={
      {
        "color": "#5C6A80",
        "fontFamily": "Montserrat-Regular",
        "fontSize": 14,
      }
    }
  >
     MMAD
  </Text>
</View>
`;
