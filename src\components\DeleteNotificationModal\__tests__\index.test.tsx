import React from 'react';
import DeleteNotificationModal from '@src/components/DeleteNotificationModal';
import {renderWithProviders} from '@src/utils/utils-for-tests';

describe('DeleteNotificationModal', () => {
  const onClose = jest.fn();
  const onDelete = jest.fn();
  it('Should render and match the snapshot', () => {
    const tree = renderWithProviders(
      <DeleteNotificationModal
        visible={false}
        onClose={onClose}
        onDelete={onDelete}
      />,
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
