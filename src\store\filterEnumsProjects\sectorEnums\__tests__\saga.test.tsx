import {call} from 'redux-saga/effects';
import {expectSaga} from 'redux-saga-test-plan';
import baseRequest from '@src/api/request';
import {ProjectWebServices} from '@src/api/project';
import ProgramSector from '@src/models/programSector';
import {handleSectorEnums} from '@src/store/filterEnumsProjects/sectorEnums/saga';
import {
  getSectorEnumsWithError,
  getSectorEnumsWithSuccess,
} from '@src/store/filterEnumsProjects/sectorEnums/slice';

const sectorEnumsExamples: ProgramSector[] = [
  {
    type: {
      type: 'array',
      code: 'code',
      label: 'label',
      id: 2,
    },
    creationDate: '2015-12-12',
    name: 'name',
    updateDate: '2015-12-12',
    id: 2,
  },
];
describe('sector enums saga', () => {
  it('should yield an api call', () => {
    // @ts-ignore
    return expectSaga(handleSectorEnums)
      .provide([
        [call(baseRequest.get, ProjectWebServices.WS_GET_SECTOR_ENUMS), {}],
      ])
      .run();
  });

  it('should handle sector enums successfully', () => {
    // @ts-ignore
    expectSaga(handleSectorEnums)
      .provide([
        [call(baseRequest.get, ProjectWebServices.WS_GET_SECTOR_ENUMS), {}],
      ])
      .put(getSectorEnumsWithSuccess(sectorEnumsExamples))
      .run();
  });

  it('should handle sector enums with error', () => {
    // @ts-ignore
    expectSaga(handleSectorEnums)
      .provide([
        [
          call(baseRequest.get, ProjectWebServices.WS_GET_SECTOR_ENUMS),
          {throw: true},
        ],
      ])
      .put(getSectorEnumsWithError())
      .run();
  });
});
