import reducer, {
  cleanUpChangeManagement,
  getChangeManagementFeatures,
  getChangeManagementFeaturesWithError,
  getChangeManagementFeaturesWithSuccess,
  initialState,
} from '@src/store/channels/changeManagement/slice';
import {ChangeManagementChannelSummary} from '@src/models/channelsSummary/changeManagementChannelSummary';

const features: ChangeManagementChannelSummary = {
  channelStatus: 'PRIMARY',
};
describe('Change Management reducer', () => {
  it('should return the initial state', () => {
    expect(reducer(undefined, {type: undefined})).toEqual(initialState);
  });

  it('should handle getChangeManagementFeatures action', () => {
    const action = getChangeManagementFeatures();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      loading: true,
      error: false,
      success: false,
    });
  });

  it('should handle getChangeManagementFeaturesWithSuccess action when the channel status is not null', () => {
    const action = getChangeManagementFeaturesWithSuccess({features});
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      features: action.payload.features,
      channelStatus: action.payload.features.channelStatus,
      loading: false,
      error: false,
      success: true,
    });
  });

  it('should handle getChangeManagementFeaturesWithSuccess action when the channel status is null', () => {
    const action = getChangeManagementFeaturesWithSuccess({
      features: {
        ...features,
        channelStatus: null,
      },
    });
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      features: action.payload.features,
      loading: false,
      error: false,
      success: true,
    });
  });

  it('should handle getChangeManagementFeaturesWithError action', () => {
    const action = getChangeManagementFeaturesWithError();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      loading: false,
      error: true,
      success: false,
    });
  });

  it('should handle cleanUp action', () => {
    const action = cleanUpChangeManagement();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
    });
  });
});
