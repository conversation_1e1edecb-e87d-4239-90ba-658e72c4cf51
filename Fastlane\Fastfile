platform :ios do
    private_lane :update_version do
        app_store_version = get_app_store_version_number(bundle_id: 'com.jesagroup.collab')
        plist_version = get_version_number_from_plist(xcodeproj: './ios/CollabMobile.xcodeproj')
        if Gem::Version.new(plist_version.to_f) == Gem::Version.new(app_store_version.to_f)
            UI.message "bumping minor"
            increment_version_number_in_plist(xcodeproj: './ios/CollabMobile.xcodeproj', bump_type: 'minor')
        else
            UI.message "bumping patch"
            increment_version_number_in_plist(xcodeproj: './ios/CollabMobile.xcodeproj', bump_type: 'patch')
        end
    end

    private_lane :staging_build do
        increment_build_number_in_plist(xcodeproj: './ios/CollabMobile.xcodeproj', target: 'CollabMobile')
        # to check the app schema !
        gym(scheme: 'CollabMobile', workspace: './ios/CollabMobile.xcworkspace')
    end

    lane :beta do
        staging_build
        upload_to_testflight(username: '<EMAIL>', app_identifier: 'com.jesagroup.collab')
        # commit_version_bump(message: 'bump build')
        # push_to_git_remote
    end
end
