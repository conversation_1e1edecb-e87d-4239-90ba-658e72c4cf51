import { createSlice } from "@reduxjs/toolkit";
import { SLICES_NAMES } from "@src/constants/store";
import { CostChannelSummary } from "@src/models/channelsSummary/costChannelSummary";

export type CostSliceType = {
  features: CostChannelSummary | null;
  channelStatus: string;
  loading: boolean;
  error: boolean;
  success: boolean;
};

export const initialState: CostSliceType = {
  features: null,
  channelStatus: "PRIMARY",
  loading: false,
  error: false,
  success: false
};
export const slice = createSlice({
  name: SLICES_NAMES.COST_CHANNEL,
  initialState,
  reducers: {
    getCostFeatures: state => {
      return {
        ...state,
        loading: true,
        error: false,
        success: false
      };
    },
    getCostFeaturesWithSuccess: (
      state,
      action: {
        payload: {
          features: CostChannelSummary;
        };
      }
    ) => {
      let finalChannelStatus: any;
      if (action.payload.features.channelStatus !== null) {
        finalChannelStatus = action.payload.features.channelStatus!!;
      } else {
        finalChannelStatus = "PRIMARY";
      }
      return {
        ...state,
        features: action.payload.features,
        channelStatus: finalChannelStatus,
        loading: false,
        error: false,
        success: true
      };
    },
    getCostFeaturesWithError: state => {
      return {
        ...state,
        features: null,
        channelStatus: "PRIMARY",
        error: true,
        loading: false,
        success: false
      };
    },
    cleanUpCost: () => {
      return initialState;
    }
  }
});

export const {
  getCostFeaturesWithSuccess,
  getCostFeaturesWithError,
  getCostFeatures,
  cleanUpCost
} = slice.actions;
export default slice.reducer;
