import { createSlice } from "@reduxjs/toolkit";
import { SLICES_NAMES } from "@src/constants/store";
import { ChangeManagementChannelSummary } from "@src/models/channelsSummary/changeManagementChannelSummary";

export type ChangeManagementSliceType = {
  features: ChangeManagementChannelSummary | null;
  channelStatus: string;
  loading: boolean;
  error: boolean;
  success: boolean;
};

export const initialState: ChangeManagementSliceType = {
  features: null,
  channelStatus: "PRIMARY",
  loading: false,
  error: false,
  success: false
};
export const slice = createSlice({
  name: SLICES_NAMES.CHANGE_MANAGEMENT_CHANNEL,
  initialState,
  reducers: {
    getChangeManagementFeatures: state => {
      return {
        ...state,
        loading: true,
        error: false,
        success: false
      };
    },
    getChangeManagementFeaturesWithSuccess: (
      state,
      action: {
        payload: { features: ChangeManagementChannelSummary };
      }
    ) => {
      let finalChannelStatus: any;
      if (action.payload.features.channelStatus !== null) {
        finalChannelStatus = action.payload.features.channelStatus!!;
      } else {
        finalChannelStatus = "PRIMARY";
      }
      return {
        ...state,
        features: action.payload.features,
        channelStatus: finalChannelStatus,
        error: false,
        success: true,
        loading: false
      };
    },
    getChangeManagementFeaturesWithError: state => {
      return {
        ...state,
        features: null,
        channelStatus: "PRIMARY",
        loading: false,
        error: true,
        success: false
      };
    },
    cleanUpChangeManagement: () => {
      return initialState;
    }
  }
});

export const {
  getChangeManagementFeatures,
  getChangeManagementFeaturesWithSuccess,
  getChangeManagementFeaturesWithError,
  cleanUpChangeManagement
} = slice.actions;
export default slice.reducer;
