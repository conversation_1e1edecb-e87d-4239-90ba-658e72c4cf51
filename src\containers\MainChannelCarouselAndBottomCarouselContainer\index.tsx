import CarouselChannels from "@src/components/CarouselChannels";
import { View } from "react-native";
import BottomCarousel from "@src/components/BottomCarousel";
import React, { useState } from "react";
import { styles } from "@src/containers/MainChannelCarouselAndBottomCarouselContainer/style";
import Loader from "@src/components/Loader";

type MainChannelCarouselAndBottomCarouselContainerProps = {
  mainCarouselChannels: any[];
  bottomCarouselChannels: any[];
  handleRefreshPage: () => void;
};
export const MainChannelCarouselAndBottomCarouselContainer = (
  props: MainChannelCarouselAndBottomCarouselContainerProps
) => {
  const [currentPosition, setCurrentPosition] = useState(0);

  const {
    mainCarouselChannels,
    bottomCarouselChannels,
    handleRefreshPage
  } = props;

  const MAIN_CHANNEL_EMPTY_WITH_ONLY_RIGHT_AND_LEFT_EMPTY_CONTAINER_TO_ADJUST_FLAT_LIST = 2;
  const BOTTOM_CAROUSEL_CHANNELS_EMPTY = 0;

  if (
    mainCarouselChannels.length ===
    MAIN_CHANNEL_EMPTY_WITH_ONLY_RIGHT_AND_LEFT_EMPTY_CONTAINER_TO_ADJUST_FLAT_LIST &&
    bottomCarouselChannels.length === BOTTOM_CAROUSEL_CHANNELS_EMPTY
  ) {
    return <Loader show={false} />;
  }

  return (
    <View style={styles.container}>
      <CarouselChannels
        handleRefreshPage={handleRefreshPage}
        currentPosition={currentPosition}
        channels={mainCarouselChannels}
        updateCarouselPosition={setCurrentPosition}
      />
      <View style={styles.bottomCarousel}>
        <BottomCarousel
          currentPosition={currentPosition}
          allowedChannels={bottomCarouselChannels}
          updateCarouselPosition={setCurrentPosition}
        />
      </View>
    </View>
  );
};
