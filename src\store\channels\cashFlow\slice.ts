import { createSlice } from "@reduxjs/toolkit";
import { SLICES_NAMES } from "@src/constants/store";
import { CashFlowChannelSummary } from "@src/models/channelsSummary/cashChannelSummary";

export type CashFlowSliceType = {
  features: CashFlowChannelSummary | null;
  channelStatus: string;
  loading: boolean;
  error: boolean;
  success: boolean;
};

export const initialState: CashFlowSliceType = {
  features: null,
  channelStatus: "PRIMARY",
  loading: false,
  error: false,
  success: false
};
export const slice = createSlice({
  name: SLICES_NAMES.CASH_FLOW_CHANNEL,
  initialState,
  reducers: {
    getCashFlowFeatures: state => {
      return {
        ...state,
        loading: true,
        error: false,
        success: false
      };
    },
    getCashFlowFeaturesWithSuccess: (
      state,
      action: {
        payload: { features: CashFlowChannelSummary };
      }
    ) => {
      let finalChannelStatus: any;
      if (action.payload.features.channelStatus !== null) {
        finalChannelStatus = action.payload.features.channelStatus!!;
      } else {
        finalChannelStatus = "PRIMARY";
      }
      return {
        ...state,
        features: action.payload.features,
        channelStatus: finalChannelStatus,
        loading: false,
        error: false,
        success: true
      };
    },
    getCashFlowFeaturesWithError: state => {
      return {
        ...state,
        features: null,
        channelStatus: "PRIMARY",
        loading: false,
        error: true,
        success: false
      };
    },
    cleanUpCash: () => {
      return initialState;
    }
  }
});

export const {
  getCashFlowFeatures,
  getCashFlowFeaturesWithSuccess,
  getCashFlowFeaturesWithError,
  cleanUpCash
} = slice.actions;
export default slice.reducer;
