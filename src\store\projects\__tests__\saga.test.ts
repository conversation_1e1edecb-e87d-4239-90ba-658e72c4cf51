import {expectSaga} from 'redux-saga-test-plan';
import {call} from 'redux-saga/effects';
import baseRequest from '@src/api/request';
import {handleProject} from '@src/store/project/saga';
import {ProjectWebServices} from '@src/api/project';
import {
  handleGetProjects,
  handleUpdateProjects,
} from '@src/store/projects/saga';
import {
  FilterParams,
  getProjectsWithError,
  getProjectsWithSuccess,
  updateProjectsWithError,
  updateProjectsWithSuccess,
} from '@src/store/projects/slice';

describe('projectsSagas', () => {
  const filterParam: FilterParams = {
    projectName: undefined,
    projectNumber: undefined,
    sector: undefined,
    bu: undefined,
    program: undefined,
    platformCode: '',
    page: 0,
    size: 0,
  };
  const params = {
    ...filterParam,
    bu: filterParam.bu?.code ? filterParam.bu.code : null,
    sector: filterParam.sector?.name ? filterParam.sector.name : null,
    program: filterParam.program?.name ? filterParam.program : null,
  };
  describe('handleGetProjects', () => {
    it('should yield an api call', () => {
      expectSaga(handleGetProjects, {payload: filterParam})
        .provide([
          [call(baseRequest.get, ProjectWebServices.WS_GET_PROJECTS), {params}],
        ])
        .run();
    });

    it('should handle getProjects successfully', () => {
      expectSaga(handleProject, {payload: 22})
        .provide([
          [call(baseRequest.get, ProjectWebServices.WS_GET_PROJECTS), {params}],
        ])
        .put(getProjectsWithSuccess([]))
        .run();
    });

    it('should handle getProjects with error', () => {
      expectSaga(handleProject, {payload: 22})
        .provide([
          [call(baseRequest.get, ProjectWebServices.WS_GET_PROJECTS), {params}],
        ])
        .put(getProjectsWithError())
        .run();
    });
  });

  describe('handleUpdateProjects', () => {
    it('should yield an api call', () => {
      expectSaga(handleUpdateProjects, {payload: filterParam})
        .provide([
          [call(baseRequest.get, ProjectWebServices.WS_GET_PROJECTS), {params}],
        ])
        .run();
    });

    it('should handle updateProjects successfully', () => {
      expectSaga(handleProject, {payload: 22})
        .provide([
          [call(baseRequest.get, ProjectWebServices.WS_GET_PROJECTS), {params}],
        ])
        .put(updateProjectsWithSuccess([]))
        .run();
    });

    it('should handle updateProjects with error', () => {
      expectSaga(handleProject, {payload: 22})
        .provide([
          [call(baseRequest.get, ProjectWebServices.WS_GET_PROJECTS), {params}],
        ])
        .put(updateProjectsWithError())
        .run();
    });
  });
});
