// module.exports = function (api) {
//   api.cache(true);
//   return {
//     presets: ['module:metro-react-native-babel-preset'],
//     plugins: [
//       [
//         'module-resolver',
//         {
//           extensions: [
//             '.ios.ts',
//             '.android.ts',
//             '.ts',
//             '.ios.tsx',
//             '.android.tsx',
//             '.tsx',
//             '.jsx',
//             '.js',
//             '.json',
//           ],
//           alias: {
//             '@screens': './src/screens',
//             // '@navigation': './src/navigation',
//             // '@__tests__': './src/__tests__',
//             // '@common-__tests__': './src/common-__tests__',
//             // '@state': './src/state',
//           },
//         },
//       ],
//     ],
//   };
// };

module.exports = {
  presets: ['module:metro-react-native-babel-preset'],
  plugins: [
    '@babel/plugin-syntax-dynamic-import',
    '@babel/plugin-transform-modules-commonjs',
    [
      'module-resolver',
      {
        root: ['.'],
        extensions: [
          '.ios.ts',
          '.android.ts',
          '.ts',
          '.ios.tsx',
          '.android.tsx',
          '.tsx',
          '.jsx',
          '.js',
          '.json',
        ],
        alias: {
          '@src': './src',
          '@containers': './src/containers',
          '@components': './src/__tests__',
          '@screens': './src/screens',
          '@store': './src/store',
          '@api': './src/api',
          '@models': './src/models',
        },
      },
    ],
    'react-native-reanimated/plugin',
  ],
};
