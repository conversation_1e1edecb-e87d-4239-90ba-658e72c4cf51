import { View } from "react-native";
import { ObjectUtils } from "@src/utils/objectUtils";
import React from "react";
import {
  COLLAB_FEATURES,
  COLLAB_FEATURES_TITLES,
  COLLAB_SUMMARY_FEATURE_KEY_WITH_FEATURES_CODE,
  getSectionValueByKey
} from "@src/constants/channels/features";
import { useSelector } from "react-redux";
import { schedulingSelector } from "@src/store/channels/scheduling/selectors";
import { ChannelFeaturesDefaultValues } from "@src/constants/channelFeaturesDefaultValues";
import { KEYS_CHANNEL } from "@src/constants/channels/modules";
import Loader from "@src/components/Loader";
import FeatureContainer, { renderFeatureContainers } from "@src/containers/FeatureContainer";

const SchedulingFeatures = () => {
  const { features, loading } = useSelector(schedulingSelector);

  // Use the reusable function for specific features
  function renderDefaultValues() {
    const schedulingFlowFeatures: FeatureContainer[] = [
      {
        featureKey: COLLAB_FEATURES.SCHEDULING.SCHED_QA_SCORE,
        featureTitle: COLLAB_FEATURES_TITLES.SCHEDULING.spi,
        featureValue: null,
        defaultFeatureValue: ChannelFeaturesDefaultValues.SCHEDULING.spi
      },
      {
        featureKey: COLLAB_FEATURES.SCHEDULING.SCHED_MILESTONES,
        featureTitle: COLLAB_FEATURES_TITLES.SCHEDULING.forecastSop,
        featureValue: null,
        defaultFeatureValue: ChannelFeaturesDefaultValues.SCHEDULING.forecastSop
      },
      {
        featureKey: COLLAB_FEATURES.SCHEDULING.SCHED_MILESTONES,
        featureTitle: COLLAB_FEATURES_TITLES.SCHEDULING.plannedSop,
        featureValue: null,
        defaultFeatureValue: ChannelFeaturesDefaultValues.SCHEDULING.plannedSop
      }
    ];

    return renderFeatureContainers(schedulingFlowFeatures);
  }

  if (loading) {
    return <Loader show={true} />;
  }
  return (
    <View>
      {features
        ? ObjectUtils.convertObjectToKeyValuesArray(features).map(
          (item, index) => {
            if (item.key !== KEYS_CHANNEL.COMMON.CHANNEL_STATUS) {
              return (
                <FeatureContainer
                  key={item.key}
                  featureKey={
                    getSectionValueByKey(COLLAB_SUMMARY_FEATURE_KEY_WITH_FEATURES_CODE, "SCHEDULING", item.key)
                  }
                  featureTitle={
                    getSectionValueByKey(COLLAB_FEATURES_TITLES, "SCHEDULING", item.key)
                  }
                  featureValue={item.value}
                  defaultFeatureValue={
                    getSectionValueByKey(ChannelFeaturesDefaultValues, "SCHEDULING", item.key)
                  }
                />
              );
            }
            return null;
          }
        )
        : renderDefaultValues()}
    </View>
  );
};

export default SchedulingFeatures;
