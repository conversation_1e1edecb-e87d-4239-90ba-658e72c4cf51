import { CPI } from "@src/components/CPI";
import React from "react";
import { renderTest } from "@src/utils/utils-for-tests";

// Mock the correspondentWidth function
jest.mock("@src/utils/imageUtils", () => ({
  correspondentWidth: jest.fn()
}));

jest.mock("@src/utils/imageUtils", () => ({
  correspondentHeight: jest.fn()
}));

describe("CPI Component", () => {
  test("renders CPI component with value", () => {
    const { getByText } = renderTest(<CPI value={42} />);
    const valueElement = getByText("42"); // Adjust the expected text if needed
    expect(valueElement).toBeTruthy();
  });

  test("displays the correct value", () => {
    const { getByText } = renderTest(<CPI value={100} />);
    const valueElement = getByText("100");
    expect(valueElement).toBeTruthy();
  });

  it("Should render and match the snapshot", () => {
    const tree = renderTest(<CPI value={100} />).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
