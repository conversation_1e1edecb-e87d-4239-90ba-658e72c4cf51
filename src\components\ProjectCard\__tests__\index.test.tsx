import React from 'react';
import {renderWithProviders} from '@src/utils/utils-for-tests';
import ProjectCard from '@src/components/ProjectCard';

describe('Project Card', function () {
  const onClick = jest.fn();
  it('Should render and match the snapshot', () => {
    const tree = renderWithProviders(
      <ProjectCard
        onClick={onClick}
        projectId={0}
        clientLogo={''}
        title={'title'}
        reference={'reference'}
        colorName={'red'}
        linearColors={{firstColor: 'red', secondColor: 'blue'}}
      />,
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
