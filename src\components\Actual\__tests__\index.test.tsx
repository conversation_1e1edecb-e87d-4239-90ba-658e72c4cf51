import React from 'react';
import {Actual} from '@src/components/Actual';
import {renderWithProviders} from '@src/utils/utils-for-tests';

describe('Actual Component', () => {
  test('renders with the correct value', () => {
    const value = 75;
    const {getByText} = renderWithProviders(<Actual value={value} />);
    const valueText = getByText(`${value} %`);
    expect(valueText).toBeTruthy();
  });
  test('Should Render and match the snapshot', () => {
    const value = 75;
    const tree = renderWithProviders(<Actual value={value} />).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
