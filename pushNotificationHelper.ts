import messaging, {
  FirebaseMessagingTypes,
} from '@react-native-firebase/messaging';
import notifee, {
  AndroidImportance,
  AndroidStyle,
  AndroidVisibility,
} from '@notifee/react-native';
//import NotificationSounds from 'react-native-notification-sounds';
import {NotificationModel} from '@src/models/notification';
import {ObjectUtils} from '@src/utils/objectUtils';
import RNEncryptedStorage from 'react-native-encrypted-storage';
import {Notification} from '@notifee/react-native/src/types/Notification';
import crashlytics from '@react-native-firebase/crashlytics';

export async function requestUserPermission() {
  const authStatus = await messaging().requestPermission();
  const enabled =
    authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
    authStatus === messaging.AuthorizationStatus.PROVISIONAL;

  if (enabled) {
    await getFcmTokenPushNotificationsHelper();
  }
}

export const getFcmTokenPushNotificationsHelper = async () => {
  let fcmToken;
  try {
    fcmToken = await RNEncryptedStorage.getItem('fcmToken');
  } catch (e: any) {
    crashlytics().recordError(e);
    console.log('this is the error in getting get item storage !', e);
  }
  if (!fcmToken) {
    try {
      const fcmTokenFromFirebase = await messaging().getToken();
      if (fcmTokenFromFirebase) {
        await RNEncryptedStorage.setItem('fcmToken', fcmTokenFromFirebase);
      }
    } catch (err: any) {
      crashlytics().recordError(err);
      // TODO To delete
      await RNEncryptedStorage.removeItem('fcmToken');
    }
  }
};

// Function to display a notification
export async function onDisplayNotification(
  title: string,
  body: string,
  notificationModel: string | null,
) {
  try {
    const channelId = await notifee.createChannel({
      id: 'default',
      name: 'Default Channel',
      importance: AndroidImportance.DEFAULT,
      visibility: AndroidVisibility.PUBLIC,
    });

    const notificationOptions: Notification = {
      title: title,
      body: body,
      android: {
        channelId,
        smallIcon: 'collab_logo_image',
        style: {
          type: AndroidStyle.BIGTEXT,
          text: body,
        },
      },
    };

    if (notificationModel) {
      notificationOptions.data = {notificationModel};
    }

    await notifee.displayNotification(notificationOptions);
  } catch (error: any) {
    crashlytics().recordError(error);
    console.error('Error displaying notification:', error);
  }
}

// Function to handle incoming messages
export const onMessageHandler = async (
  remoteMessage: FirebaseMessagingTypes.RemoteMessage,
) => {
  try {
    const obj: NotificationModel =
      ObjectUtils.convertRemoteNotificationObjectToNotificationModel(
        remoteMessage,
      );

    // Required for iOS
    // See https://notifee.app/react-native/docs/ios/permissions
    await notifee.requestPermission();

    const notificationDto = remoteMessage.data?.notificationDto || null;

    await onDisplayNotification(obj.title!!, obj.message!!, notificationDto);
  } catch (error: any) {
    crashlytics().recordError(error);
    console.error('Error processing incoming message:', error);
  }
};
