import {call, put, select, takeEvery} from 'redux-saga/effects';
import {
  getHseFeatures,
  getHseFeaturesWithError,
  getHseFeaturesWithSuccess,
} from './slice';
import {projectSelector} from '@src/store/project/selectors';
import baseRequest from '@src/api/request';
import {ChannelsWebServices} from '@src/api/channels';

export function* handleHseFeatures() {
  try {
    const {projectId, reportingDateFilter} = yield select(projectSelector);
    const {data} = yield call(
      baseRequest.get,
      ChannelsWebServices.WS_GET_HSE_SUMMARY(projectId),
      {params: reportingDateFilter},
    );

    yield put(getHseFeaturesWithSuccess({features: data}));
  } catch (e) {
    yield put(getHseFeaturesWithError());
  }
}

export default function* saga() {
  yield takeEvery(getHseFeatures, handleHseFeatures);
}
