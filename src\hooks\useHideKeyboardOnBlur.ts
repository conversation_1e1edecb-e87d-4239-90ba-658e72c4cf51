import {useEffect} from 'react';
import {Keyboard} from 'react-native';

// @ts-ignore
const useHideKeyboardOnBlur = _ => {
  useEffect(() => {
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        // Remove focus from the active text input
        //handleSettingFocusedInteraction();
        Keyboard.dismiss();
      },
    );

    return () => {
      keyboardDidHideListener.remove();
    };
  }, []);
};

export default useHideKeyboardOnBlur;
