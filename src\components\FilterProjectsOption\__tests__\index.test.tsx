import {renderWithProviders} from '@src/utils/utils-for-tests';
import React from 'react';
import FilterProjectsOption from '@src/components/FilterProjectsOption';

describe('FilterProjectsOption', function () {
  it('Should render and match the snapshot', () => {
    const tree = renderWithProviders(
      <FilterProjectsOption value={''} />,
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
