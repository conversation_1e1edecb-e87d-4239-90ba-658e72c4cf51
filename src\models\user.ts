export interface User {
  id: number;
  status: true;
  name: string;
  avatar: string;
  userProject: {
    deleted: boolean;
    external: boolean;
    id: number;
    email: string;
    name: string;
    userId: number;
  },
  companies: [
    {
      id: string;
      name: string;
      type: string;
      creationDate: string;
      updateDate: string;
      managementNotificationAddress: string;
    },
  ];
  specificRole: {
    id: number;
    type: string;
    code: string;
    label: string;
  };
  displayName: string;
  mail: string;
  azureDirectoryId: string;
  profileName: string;
  externalUserRole: string;
  userMgtId: number;
  deleted: true;
  admin: true;
  external: true;
  linkedToAllCompanies: true;
  dpe: true;
}
