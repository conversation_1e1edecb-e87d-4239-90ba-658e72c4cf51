import { correspondentHeight, correspondentWidth } from "@src/utils/imageUtils";
import "react-native-gesture-handler/jestSetup";

jest.mock("@src/utils/imageUtils", () => ({
  correspondentWidth: jest.fn(),
  correspondentHeight: jest.fn(),
}));

beforeEach(() => {
  // Default implementation for all tests
  correspondentWidth.mockImplementation(value => value * 2);
  correspondentHeight.mockImplementation(value => value * 2);
});
