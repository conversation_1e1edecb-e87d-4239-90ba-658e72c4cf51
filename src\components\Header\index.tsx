import React, { ReactNode } from "react";
import { Pressable, Text, View } from "react-native";
import style from "./style";
import ClickableImage from "@src/components/ClickableImage";
import { globalStyles } from "@src/theme/style";
import { TEST_IDS } from "@src/constants/strings";
import { navigatePop } from "@src/utils/navigationUtils";
import { CollabLogo } from "@src/svg/CollabLogo";
import { Back } from "@src/svg/Back";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { windowHeight } from "@src/constants";

interface HeaderProps {
  rightContent?: ReactNode;
  leftContentAction?: () => void;
  showBackButton: boolean;
  routeName: string;
}

const Header: React.FC<HeaderProps> = ({
                                         rightContent,
                                         showBackButton,
                                         routeName,
                                         leftContentAction
                                       }) => {
  const leftContent = !showBackButton ? (
    <View style={style.routeContainer}>
      <CollabLogo style={style.logoImage} />
      <Text style={globalStyles.routeName}>{routeName}</Text>
    </View>
  ) : (
    <Pressable
      onPress={() => {
        if (leftContentAction) {
          leftContentAction();
        }
        navigatePop(routeName);
      }}
      style={style.routeContainer}>
      <ClickableImage
        onPress={() => navigatePop(routeName)}
        imageStyle={globalStyles.backImage}
        testID={TEST_IDS.BACK_BUTTON}
        url={null}
        SvgComponent={<Back style={style.logoImage} />}
      />
      <Pressable onPress={() => navigatePop(routeName)}>
        <Text style={globalStyles.routeName}>{routeName}</Text>
      </Pressable>
    </Pressable>
  );

  const insets = useSafeAreaInsets();

  return (
    <View style={[style.headerContainer, { paddingTop: insets.top + windowHeight * 0.02 }]}>
      {leftContent}
      <View style={style.rightContent}>{rightContent}</View>
    </View>
  );
};
export default Header;
