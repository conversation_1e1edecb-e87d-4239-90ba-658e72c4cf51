import { createSlice } from "@reduxjs/toolkit";
import { SLICES_NAMES } from "@src/constants/store";
import { User } from "@src/models/user";

export type AuthSliceType = {
  success?: boolean;
  error?: boolean;
  userToken: string | null;
  fcmToken?: string | null;
  successFcmToken: boolean;
  errorFcmToken: boolean;
  user: User | null;
  successUser: boolean;
  errorUser: boolean;
};

export const initialState: AuthSliceType = {
  success: false,
  error: false,
  fcmToken: null,
  userToken: null,
  successFcmToken: false,
  errorFcmToken: false,
  user: null,
  errorUser: false,
  successUser: false
};

const slice = createSlice({
  name: SLICES_NAMES.AUTHENTICATION_SLICE,
  initialState,
  reducers: {
    login: (
      state,
      action: {
        payload: {
          userToken: string;
        };
      }
    ) => {
      state.userToken = action.payload.userToken;
    },
    logOut: _ => {
      return initialState;
    },
    setUserToken: (state, action) => {
      state.userToken = action.payload;
    },
    setFcmToken: (state, action) => {
      return {
        ...state,
        loading: true,
        errorFcmToken: false,
        successFcmToken: false
      };
    },
    setFcmTokenWithSuccess: (state, action) => {
      return {
        ...state,
        fcmToken: action.payload,
        errorFcmToken: false,
        successFcmToken: true,
        loading: false
      };
    },
    setFcmTokenWithError: state => {
      return {
        ...state,
        fcmToken: null,
        errorFcmToken: true,
        successFcmToken: false,
        loading: false
      };
    },
    setUser: state => {
      return {
        ...state,
        loading: true,
        successUser: false,
        errorUser: false
      };
    },
    setUserWithSuccess: (state, action: { payload: User }) => {
      return {
        ...state,
        loading: false,
        successUser: true,
        errorUser: false,
        user: action.payload
      };
    },
    setUserWithError: state => {
      return {
        ...state,
        loading: false,
        successUser: false,
        errorUser: true,
        user: null
      };
    }
  }
});

export const {
  logOut,
  login,
  setUserToken,
  setFcmToken,
  setFcmTokenWithSuccess,
  setFcmTokenWithError,
  setUserWithSuccess,
  setUser,
  setUserWithError
} = slice.actions;
export default slice.reducer;
