import {call, put, select, takeEvery} from 'redux-saga/effects';

import {ChannelsWebServices} from '@src/api/channels';
import {projectSelector} from '@src/store/project/selectors';
import baseRequest from '@src/api/request';
import {
  getCashFlowFeatures,
  getCashFlowFeaturesWithError,
  getCashFlowFeaturesWithSuccess,
} from '@src/store/channels/cashFlow/slice';

export function* handleCashFlowFeatures() {
  try {
    const {projectId, reportingDateFilter} = yield select(projectSelector);
    const {data} = yield call(
      baseRequest.get,
      ChannelsWebServices.WS_GET_CASH_SUMMARY(projectId),
      {params: reportingDateFilter},
    );
    yield put(getCashFlowFeaturesWithSuccess({features: data}));
  } catch (e) {
    yield put(getCashFlowFeaturesWithError());
  }
}

export default function* saga() {
  yield takeEvery(getCashFlowFeatures, handleCashFlowFeatures);
}
