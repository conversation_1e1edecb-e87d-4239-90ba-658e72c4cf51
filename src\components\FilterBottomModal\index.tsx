import React, { memo, useEffect, useState } from "react";
import { Text, TouchableOpacity, TouchableWithoutFeedback, View } from "react-native";
import { LABELS, PLACEHOLDERS, TEST_IDS } from "@src/constants/strings";
import { FilterParams } from "@src/store/projects/slice";
import { bottomSheetStyles } from "./style";
import { useDispatch, useSelector } from "react-redux";
import { buEnumsSelector } from "@src/store/filterEnumsProjects/buEnums/selectors";

import InputText from "@src/components/TextInput";
import { sectorEnumsSelector } from "@src/store/filterEnumsProjects/sectorEnums/selectors";
import { programEnumsSelector } from "@src/store/filterEnumsProjects/programEnums/selectors";
import { getBuEnums } from "@src/store/filterEnumsProjects/buEnums/slice";
import { getSectorEnums } from "@src/store/filterEnumsProjects/sectorEnums/slice";
import { getProgramEnums } from "@src/store/filterEnumsProjects/programEnums/slice";
import Modal from "react-native-modal";
import { globalStyles } from "@src/theme/style";
import { COMMON_COLORS } from "@src/theme/colors";
import CustomDropDownSelect from "@src/components/CustomDropDownSelect";
import { EnumerationsFilterProjectTypes } from "@src/constants/types";
import {
  BU_LABEL,
  PROGRAM_LABEL,
  PROJECT_NAME_LABEL,
  PROJECT_NUMBER_LABEL,
  SECTOR_LABEL
} from "@src/constants/projects";
import { Filter } from "@src/models/projectFilter";
import { correspondentWidth } from "@src/utils/imageUtils";
import { ScrollView } from "react-native-gesture-handler";

type FilterBottomModalProps = {
  children?: React.ReactNode;
  filterParams: FilterParams;
  onClose?: () => void;
  visible?: boolean;
  onApplyFilter?: any;
};

export const FilterBottomModal = (props: FilterBottomModalProps) => {
  const { visible, onClose, onApplyFilter, filterParams } = props;
  const { buEnums } = useSelector(buEnumsSelector);
  const { sectorEnums } = useSelector(sectorEnumsSelector);
  const { programEnums } = useSelector(programEnumsSelector);
  const [filterBottomSheetState, setFilterBottomSheetState] =
    useState<FilterParams>(filterParams ?? {});
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(getBuEnums({ types: EnumerationsFilterProjectTypes.BU }));
    dispatch(getSectorEnums({ types: EnumerationsFilterProjectTypes.SECTOR }));
    dispatch(getProgramEnums({ types: EnumerationsFilterProjectTypes.PROGRAM }));
  }, []);

  useEffect(() => {
    if (!onClose) {
      return;
    }
  }, [onClose]);

  const handleFilterBottomSheetChangeInProjectFilter = (
    label: string,
    code: string
  ) => {
    let labelInFilterParams: string;
    let finalObject: Filter | null | undefined;

    labelInFilterParams = LABELS.BUSINESS_UNIT;

    switch (label) {
      case LABELS.BUSINESS_UNIT:
        labelInFilterParams = BU_LABEL;
        finalObject = code.length === 0 ? null : findEnumItem(buEnums, code);
        break;
      case LABELS.SECTOR:
        labelInFilterParams = SECTOR_LABEL;
        finalObject =
          code.length === 0 ? null : findEnumItem(sectorEnums, code);
        break;
      case LABELS.PROGRAM:
        labelInFilterParams = PROGRAM_LABEL;
        finalObject =
          code.length === 0 ? null : findEnumItem(programEnums, code);
        break;
      default:
        break;
    }

    updateFilterBottomSheet(labelInFilterParams, finalObject);
  };

  const handleFilterTextInputsBottomSheetChange = (
    label: string,
    object: string
  ) => {
    updateFilterBottomSheet(label, object);
  };

  const findEnumItem = (enumList: Filter[], code: string) => {
    return enumList.find(item => item.code === code);
  };

  const updateFilterBottomSheet = (label: string, value: any) => {
    setFilterBottomSheetState(prevState => ({
      ...prevState,
      [label]: value
    }));
  };

  return (
    <>
      <Modal
        statusBarTranslucent={false}
        isVisible={visible}
        onBackButtonPress={onClose}
        style={bottomSheetStyles.modal}>
        <TouchableWithoutFeedback
          onPress={() => {
            if (onClose) {
              onClose();
            }
          }}>
          <View style={globalStyles.blurViewModal} />
        </TouchableWithoutFeedback>
        <View style={bottomSheetStyles.container}>
          <View style={bottomSheetStyles.content}>
            <ScrollView
              showsVerticalScrollIndicator={false}
            >
              <View style={bottomSheetStyles.line} />
              <Text style={bottomSheetStyles.filterFieldsTitle}>
                Filter Fields
              </Text>
              <InputText
                onChangeText={text =>
                  handleFilterTextInputsBottomSheetChange(
                    PROJECT_NAME_LABEL,
                    text
                  )
                }
                value={filterBottomSheetState?.projectName}
                placeholder={PLACEHOLDERS.PROJECT_NAME}
                label={LABELS.PROJECT_NAME}
              />

              <InputText
                onChangeText={text =>
                  handleFilterTextInputsBottomSheetChange(
                    PROJECT_NUMBER_LABEL,
                    text
                  )
                }
                value={filterBottomSheetState?.projectNumber}
                placeholder={PLACEHOLDERS.PROJECT_NUMBER}
                label={LABELS.PROJECT_NUMBER}
              />

              <CustomDropDownSelect
                selectedEnumInFilterParams={filterBottomSheetState.bu ?? null}
                enums={buEnums}
                label={LABELS.BUSINESS_UNIT}
                setCurrentEnum={handleFilterBottomSheetChangeInProjectFilter}
              />
              <CustomDropDownSelect
                selectedEnumInFilterParams={filterBottomSheetState.sector ?? null}
                enums={sectorEnums}
                label={LABELS.SECTOR}
                setCurrentEnum={handleFilterBottomSheetChangeInProjectFilter}
              />
              <CustomDropDownSelect
                selectedEnumInFilterParams={
                  filterBottomSheetState.program ?? null
                }
                enums={programEnums}
                label={LABELS.PROGRAM}
                setCurrentEnum={handleFilterBottomSheetChangeInProjectFilter}
              />

              <View style={bottomSheetStyles.twoButtonsContainer}>
                <TouchableOpacity
                  style={{ ...bottomSheetStyles.button }}
                  onPress={onClose}>
                  <Text
                    style={{
                      ...bottomSheetStyles.buttonText,
                      color: COMMON_COLORS.BLACK
                    }}>
                    Cancel
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  testID={TEST_IDS.APPLY_BUTTON}
                  style={{
                    ...bottomSheetStyles.button,
                    backgroundColor: COMMON_COLORS.PRIMARY,
                    marginLeft: correspondentWidth(16)
                  }}
                  onPress={() => {
                    onApplyFilter(filterBottomSheetState);
                  }}>
                  <Text style={bottomSheetStyles.buttonText}>Apply</Text>
                </TouchableOpacity>
              </View>
            </ScrollView>
          </View>
        </View>
      </Modal>
    </>
  );
};

export default memo(FilterBottomModal);
