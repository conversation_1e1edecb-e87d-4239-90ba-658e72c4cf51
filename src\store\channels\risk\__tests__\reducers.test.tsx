import reducer, {
  getSchedulingFeatures,
  getSchedulingFeaturesWithError,
  getSchedulingFeaturesWithSuccess,
  initialState,
} from '@src/store/channels/scheduling/slice';
import {SchedulingChannelSummary} from '@src/models/channelsSummary/schedulingChannelSummary';
import {cleanUpRisk} from '@src/store/channels/risk/slice';

const features: SchedulingChannelSummary = {
  channelStatus: 'PRIMARY',
};
describe('Scheduling reducer', () => {
  it('should return the initial state', () => {
    expect(reducer(undefined, {type: undefined})).toEqual(initialState);
  });

  it('should handle getSchedulingFeatures action', () => {
    const action = getSchedulingFeatures();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      error: false,
      success: false,
    });
  });

  it('should handle getSchedulingFeatures action when the channel status is not null', () => {
    const action = getSchedulingFeaturesWithSuccess({features});
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      features: action.payload.features,
      channelStatus: action.payload.features.channelStatus,
      error: false,
      success: true,
    });
  });

  it('should handle getSchedulingFeaturesWithSuccess action when the channel status is null', () => {
    const action = getSchedulingFeaturesWithSuccess({
      features: {
        ...features,
        channelStatus: null,
      },
    });
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      features: action.payload.features,
      error: false,
      success: true,
    });
  });

  it('should handle getSchedulingFeaturesWithError action', () => {
    const action = getSchedulingFeaturesWithError();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      error: true,
      success: false,
    });
  });

  it('should handle cleanUp action', () => {
    const action = cleanUpRisk();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
    });
  });
});
