import {View} from 'react-native';
import React from 'react';

export const TouchableOpacity = ({children}) => <View>{children}</View>;
export const ScrollView = ({children}) => <View>{children}</View>;
export const GestureHandlerRootView = ({children}) => <View>{children}</View>;
export const GestureDetector = ({children}) => <View>{children}</View>;
export const Gesture = {
  Pan: jest.fn(() => ({
    onBegin: () => ({
      onChange: () => ({
        onEnd: jest.fn(),
      }),
    }),
  })),
};
