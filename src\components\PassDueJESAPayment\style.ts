import {StyleSheet} from 'react-native';
import typography from '@src/theme/fonts';
import {COMMON_COLORS} from '@src/theme/colors';

const style = StyleSheet.create({
  MMAD: {
    fontSize: 14,
    fontFamily: typography.fontFamily.montserratRegular,
    color: '#5C6A80',
  },
  valuePassDueJesaPayment: {
    color: COMMON_COLORS.SEMI_LIGHT_BLUE,
    fontSize: 14,
    fontFamily: typography.fontFamily.montserratRegular,
    fontWeight: 'bold',
  },
});

export default style;
