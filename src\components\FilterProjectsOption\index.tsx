import {Text, View} from 'react-native';
import React from 'react';
import styles from './styles';
import {Close} from '@src/svg/Close';

type FilterProjectsOptionProps = {
  value: string;
};

const FilterProjectsOption = (props: FilterProjectsOptionProps) => {
  const {value} = props;
  return (
    <View style={styles.container}>
      <Text style={styles.text}>{value}</Text>
      <View style={styles.closeButton}>
        <Close />
      </View>
    </View>
  );
};

export default FilterProjectsOption;
