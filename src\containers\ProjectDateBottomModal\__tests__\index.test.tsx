import React from 'react';
import {renderWithProviders} from '@src/utils/utils-for-tests';
import {NavigationContainer} from '@react-navigation/native';
import ProjectDateBottomModal from '@src/containers/ProjectDateBottomModal';
import {View} from 'react-native';

describe('Project Date Bottom Modal Container', () => {
  it('should render  and match the snapshot', () => {
    const handleApplyFilter = jest.fn();
    const handleCancelFilter = jest.fn();
    const tree = renderWithProviders(
      <NavigationContainer>
        <ProjectDateBottomModal
          visible={true}
          children={<View />}
          handleApplyFilter={handleApplyFilter}
          handleCancelFilter={handleCancelFilter}
          reportingDate={'reporting Date'}
          reportingDateParams={{
            month: 2,
            year: 1,
          }}
          startDate={'start Date'}
        />
      </NavigationContainer>,
      {},
    );
    expect(tree).toMatchSnapshot();
  });
});
