import {StyleSheet} from 'react-native';
import {GLOBAL_MARGIN_HORIZONTAL, windowWidth} from '@src/constants';
import layout from '@src/theme/layout';
import {COMMON_COLORS} from '@src/theme/colors';
import {correspondentHeight} from '@src/utils/imageUtils';

const style = StyleSheet.create({
  bottomCarousel: {
    position: 'absolute',
    bottom: 0,
    width: windowWidth - 2 * GLOBAL_MARGIN_HORIZONTAL,
    marginBottom: correspondentHeight(22),
  },
  container: {
    marginHorizontal: GLOBAL_MARGIN_HORIZONTAL,
  },
  textNotFoundView: {
    ...layout.center,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    marginTop: correspondentHeight(400),
  },
  textNotFound: {
    color: COMMON_COLORS.BLACK,
  },
});

export default style;
