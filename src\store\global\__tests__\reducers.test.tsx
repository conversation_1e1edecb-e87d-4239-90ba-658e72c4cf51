import reducer, {
  initialState,
  setIsApplicationVersionUpdated,
  setIsApplicationVersionUpdatedWithError,
  setIsApplicationVersionUpdatedWithSuccess,
} from '@src/store/global/slice';

describe('Global reducer', () => {
  it('should return the initial state', () => {
    expect(reducer(undefined, {type: undefined})).toEqual(initialState);
  });

  it('should handle setIsApplicationVersionUpdated action', () => {
    const action = setIsApplicationVersionUpdated();
    const newState = reducer(initialState, action);
    expect(newState).toEqual({
      ...initialState,
    });
  });

  it('should handle setIsApplicationVersionUpdatedWithSuccess action', () => {
    const payload = true; // Replace with a valid FCM token
    const action = setIsApplicationVersionUpdatedWithSuccess(payload);
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      isApplicationVersionUpdated: action.payload,
    });
  });

  it('should handle setIsApplicationVersionUpdatedWithError action', () => {
    const action = setIsApplicationVersionUpdatedWithError();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
    });
  });
});
