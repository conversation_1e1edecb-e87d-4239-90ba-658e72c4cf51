import { AppRegistry, Platform } from "react-native";
import App from "./App";
import messaging from "@react-native-firebase/messaging";
import notifee, { EventType } from "@notifee/react-native";
import { ObjectUtils } from "@src/utils/objectUtils";
import "react-native-gesture-handler";
import { onDisplayNotification } from "./pushNotificationHelper";

/*
  * This file serves as the entry point for the React Native application.
  * The file defines functions for handling background messages and events, as well as a component for checking if the app was launched in the background.
  * Additionally, it registers the main component of the app with AppRegistry and exports configuration from native modules for further use.
  * File Structure:
  * - Imports: Import necessary modules and components
  * - Background Message Handling: Define functions to handle background messages from Firebase Cloud Messaging and Notifee
  * - Headless Check: Define a component to check if the app was launched in the background
  * - App Registration: Register the main component of the app with AppRegistry
  * - Export: Export configuration from native modules
*/

messaging().setBackgroundMessageHandler(async remoteMessage => {
  const obj = ObjectUtils.convertRemoteNotificationObjectToNotificationModel(remoteMessage);
  // TODO we cannot display the notification because the background notification will be handled by the phone not by the application so we must change it directly from the sender the backend in our use case !
  await onDisplayNotification(obj.title, obj.message, remoteMessage.data?.notificationDto ?? "test");
});

notifee.onBackgroundEvent(async ({ type, detail }) => {
  const { notification, pressAction } = detail;
  if (type === EventType.PRESS) {
    // Decrement the count by 1
    await notifee.decrementBadgeCount();

    // Remove the notification
    await notifee.cancelNotification(notification.id);
  }
});

const isIosVersion = Platform.OS === "ios";
let name = "";
if (isIosVersion) {
  name = "CollabMobile";
} else {
  name = "Collab";
}

// Check if app was launched in the background and conditionally render null if so
function HeadlessCheck({ isHeadless }) {
  if (isHeadless) {
    // App has been launched in the background by iOS, ignore
    return null;
  }

  // Render the app component on foreground launch
  return <App />;
}

AppRegistry.registerComponent(name, () => HeadlessCheck);
