import { Animated } from "react-native";
import React from "react";

interface DrawerShadowProps {
  marginTop: Animated.AnimatedInterpolation<number>;
  marginEnd: Animated.AnimatedInterpolation<number>;
  borderRadius: Animated.AnimatedInterpolation<number>;
  translateX: Animated.AnimatedInterpolation<number>;
  scale: Animated.AnimatedInterpolation<number>;
}

export const DrawerShadow: React.FC<DrawerShadowProps> = (props) => {
  // Only use transform properties with native driver
  const animatedStyle = {
    transform: [{ scale: props.scale }, { translateX: props.translateX }]
  };

  // Use a regular View with fixed styles for non-transform properties
  return (
    <Animated.View
      style={[
        {
          // Use fixed values instead of animated values for these properties
          marginTop: 50, // Fixed value instead of animated
          borderRadius: 30, // Fixed value instead of animated
        },
        animatedStyle
      ]}
    />
  );
};
