export enum COLLAB_MODULES {
  RISK_MGMT = 'RISK_MGMT',
  HSE = 'HSE',
  QA = 'QA',
  SCHEDULING = 'SCHEDULING',
  COST = 'COST',
  CASH = 'CASH',
  PROGRESS = 'PROGRESS',
  CHANGE_MGMT = 'CHANGE_MGMT',
  HUMAN_CAP = 'HUMAN_CAP',
  PROJECT_MANAGEMENT = 'PROJECT_MANAGEMENT',
  JPASS_SETTINGS = 'JPASS_SETTINGS',
}

export const KEYS_CHANNEL = {
  COMMON: {
    CHANNEL_STATUS: 'channelStatus',
  },
  QA: {
    AVERAGE_OPEN_DAYS: 'averageOpenDays',
    CLIENT_SATISFACTION: 'clientSatisfaction',
    OVERDUE_PINS_NUMBER: 'overduePinsNumber',
  },
  RISK: {
    PREVIOUS_EVOLUTION: 'previousEvolution',
    CURRENT_EVOLUTION: 'currentEvolution',
    CRITICAL_RISKS: 'criticalRisks',
  },
  PROGRESS: {
    CLOSED_SOR: 'closedSor',
  },
  HSE: {
    CLOSED_SOR: 'closedSor',
    OPEN_SOR: 'openSor',
    TRIR: 'trir',
  },
  HUMAN_CAP: {
    CONTRACTOR_HEAD_COUNT: 'contractorHeadCount',
    JESA_HEAD_COUNT: 'jesaHeadCount',
  },
  COST: {
    CPI: 'cpi',
    FORECAST: 'forcast',
    REVISED_BUDGET_TIC: 'revisedBudgetTic',
  },
  CHANGE_MANAGEMENT: {
    PENDING_JESA_CHANGE: 'pendingJesaChange',
    PENDING_CONTRACTOR_CHANGE: 'pendingContractorChange',
    CLOSED_SOR: 'closedSor',
  },
  CASH_FLOW: {
    PASS_DUE_CONTRACTOR_PAYMENT: 'passDueContractorPayment',
    PASS_DUE_JESA_PAYMENT: 'passDueJesaPayment',
    DSO: 'dso',
  },
};

export const COLLAB_MODULES_TITLES = {
  RISK_MGMT: 'Risk Management',
  HSE: 'HSE',
  QA: 'Quality Assurance',
  SCHEDULING: 'Scheduling',
  COST: 'Cost',
  CASH: 'CashFlow',
  PROGRESS: 'Progress',
  CHANGE_MGMT: 'Change Management',
  HUMAN_CAP: 'Human Capital',
  PROJECT_MANAGEMENT: 'Project Management',
  JPASS_SETTINGS: 'JPASS_SETTINGS',
};
