import { createSlice } from "@reduxjs/toolkit";
import { SLICES_NAMES } from "@src/constants/store";
import { Project } from "@src/models/project";
import { PLATFORM } from "@src/models/enum";
import { Filter } from "@src/models/projectFilter";

export type FilterParams = {
  projectName: string | null;
  projectNumber: string | null;
  sector: null | Filter;
  bu: null | Filter;
  program: null | Filter;
  platformCode: string;
  page: number;
  size: number;
  [key: string]: string | null | Filter | number;
};

export type ProjectsSliceType = {
  projects: Project[];
  filterParams: FilterParams;
  pageable: {
    sort: {
      unsorted: boolean;
      sorted: boolean;
      empty: boolean;
    };
    offset: number;
    pageNumber: number;
    pageSize: number;
    paged: boolean;
    unpaged: boolean;
  };
  totalElements: number;
  totalPages: number;
  last: boolean;
  size: number;
  number: number;
  sort: {
    unsorted: boolean;
    sorted: boolean;
    empty: boolean;
  };
  first: boolean;
  numberOfElements: number;
  empty: boolean;
  loading: boolean;
  success: boolean;
  error: boolean;
};

export const initialState: ProjectsSliceType = {
  projects: [],
  filterParams: {
    page: 0,
    size: 10,
    sector: null,
    bu: null,
    projectName: null,
    projectNumber: null,
    program: null,
    platformCode: PLATFORM.COLLAB
  },
  error: false,
  success: false,
  loading: false,
  pageable: {
    sort: {
      unsorted: false,
      sorted: false,
      empty: false
    },
    offset: 0,
    pageNumber: 0,
    pageSize: 0,
    paged: false,
    unpaged: false
  },
  totalElements: 0,
  totalPages: 0,
  last: false,
  size: 0,
  number: 0,
  sort: {
    unsorted: false,
    sorted: false,
    empty: false
  },
  first: false,
  numberOfElements: 0,
  empty: false
};

const slice = createSlice({
  name: SLICES_NAMES.PROJECTS_SLICE,
  initialState,
  reducers: {
    getProjects: (state, action) => {
      console.log("this is the value of filter parameters ", action, "state", state.filterParams);
      return {
        ...state,
        loading: true,
        success: false,
        error: false
      };
    },
    getProjectsWithSuccess: (
      state,
      action: { payload: { content: Project[] } }
    ) => {
      return {
        ...state,
        ...action.payload,
        projects: action.payload.content,
        loading: false,
        success: true,
        error: false
      };
    },
    getProjectsWithError: state => {
      return {
        ...state,
        projects: [],
        loading: false,
        success: false,
        error: true
      };
    },
    updateProjects: (state, action) => {
      return {
        ...state,
        loading: true,
        success: false,
        error: false
      };
    },
    updateProjectsWithSuccess: (state, action) => {
      let projects = [...state.projects, ...action.payload.data.content];
      return {
        ...state,
        ...action.payload.data,
        projects,
        loading: false,
        success: true,
        error: false
      };
    },

    updateProjectsWithError: state => {
      return {
        ...state,
        projects: [],
        loading: false,
        success: false,
        error: true
      };
    },
    setFilterParams: (state, action: { payload: FilterParams }) => {
      return {
        ...state,
        filterParams: action.payload
      };
    },
    resetFilterItem: (state) => {
      return {
        ...initialState
      };
    }
  }
});

export const {
  setFilterParams,
  resetFilterItem,
  updateProjectsWithSuccess,
  updateProjectsWithError,
  updateProjects,
  getProjectsWithSuccess,
  getProjectsWithError,
  getProjects
} = slice.actions;
export default slice.reducer;
