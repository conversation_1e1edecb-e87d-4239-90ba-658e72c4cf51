import {Defs, LinearGradient, Path, Stop, Svg} from 'react-native-svg';
import React from 'react';

export const UnReadNotifications = (props: {style: {}}) => {
  return (
    <Svg
      style={props.style}
      width="21.998"
      height="25.8"
      viewBox="0 0 21.998 25.8">
      <Defs>
        <LinearGradient
          id="linear-gradient"
          x1="-0.218"
          y1="0.395"
          x2="1"
          y2="0.405">
          <Stop offset="0" stopColor="#ff7675" />
          <Stop offset="1" stopColor="#e64748" />
        </LinearGradient>
      </Defs>
      <Path
        d="M418.528-16302.456a4.708,4.708,0,0,1-1.442-.742,3.774,3.774,0,0,1-1.008-1.159.912.912,0,0,1,.889-1.349c.229.025,2.037.2,3.213.2s2.983-.176,3.213-.2a.914.914,0,0,1,.889,1.349,3.829,3.829,0,0,1-1.008,1.159,4.8,4.8,0,0,1-1.442.742,5.354,5.354,0,0,1-1.651.254A5.355,5.355,0,0,1,418.528-16302.456Zm-4.749-4.512a5.1,5.1,0,0,1-3.782-2.844,4.769,4.769,0,0,1,.164-4.434l.27-.483a7.955,7.955,0,0,0,1.024-3.894v-1.528a7.428,7.428,0,0,1,1.192-4.024,7.862,7.862,0,0,1,3.217-2.818,10.383,10.383,0,0,1,4.458-1.009,10.227,10.227,0,0,1,4,.808,5.968,5.968,0,0,0-2.8,5.073,6.007,6.007,0,0,0,6,6,6.007,6.007,0,0,0,2.184-.41,8.119,8.119,0,0,0,.6,1.688l.32.66a4.915,4.915,0,0,1-.053,4.4,5.238,5.238,0,0,1-3.807,2.774l-.193.037a36.737,36.737,0,0,1-6.4.562A36.748,36.748,0,0,1,413.778-16306.968Zm9.749-15.153a4,4,0,0,1,4-4,4,4,0,0,1,4,4,4,4,0,0,1-4,4A4,4,0,0,1,423.527-16322.121Z"
        transform="translate(-409.528 16328.002)"
        fill="url(#linear-gradient)"
      />
    </Svg>
  );
};
