import {renderWithProviders} from '@src/utils/utils-for-tests';
import CustomDropDownSelect from '@src/components/CustomDropDownSelect';
import React from 'react';
import ProjectFilter from '@src/models/projectFilter';

describe('CustomDropdown', () => {
  const setCurrentEnum = jest.fn();
  const enums: ProjectFilter[] = [
    {label: 'label', code: 'code', type: 'type', id: 1},
  ];
  const label = 'label';
  const selectedEnumInFilterParams = 'selectedEnumInFilterParams';

  it('should render and match the snapshot', () => {
    const tree = renderWithProviders(
      <CustomDropDownSelect
        enums={enums}
        selectedEnumInFilterParams={selectedEnumInFilterParams}
        label={label}
        setCurrentEnum={setCurrentEnum}
      />,
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });

  it('should render without crashing', () => {
    const tree = renderWithProviders(
      <CustomDropDownSelect
        enums={enums}
        selectedEnumInFilterParams={selectedEnumInFilterParams}
        label={label}
        setCurrentEnum={setCurrentEnum}
      />,
    ).toJSON();
    expect(tree).toBeDefined();
  });
});
