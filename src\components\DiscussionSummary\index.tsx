import {View} from 'react-native';
import {styles} from './style';
import React from 'react';
import DiscussionSummaryHeader from '@src/components/DiscussionSummaryHeader';
import DiscussionSummaryBody from 'src/components/DiscussionSummaryBody';

const DiscussionSummary = () => {
  return (
    <View style={styles.container}>
      <DiscussionSummaryHeader />
      <DiscussionSummaryBody />
    </View>
  );
};

export default DiscussionSummary;
