import {Text, View} from 'react-native';
import style from './style';
import React, {ReactNode} from 'react';
import {globalStyles} from '@src/theme/style';

type FeatureProps = {
  label: string;
  value: ReactNode;
  labelStyle?: Object;
};
const Feature = ({label, value, labelStyle}: FeatureProps) => {
  return (
    <View style={style.detailFeatureContainer}>
      <Text style={[globalStyles.featureKey, labelStyle]}>{label}</Text>
      {value}
    </View>
  );
};

export default Feature;
