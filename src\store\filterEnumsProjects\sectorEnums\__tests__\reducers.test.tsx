import reducer, {
  cleanUp,
  getSectorEnums,
  getSectorEnumsWithError,
  getSectorEnumsWithSuccess,
  initialState,
} from '@src/store/filterEnumsProjects/sectorEnums/slice';
import ProgramSector from '@src/models/programSector';

const programEnumsExamples: ProgramSector[] = [
  {
    type: {
      type: 'array',
      code: 'code',
      label: 'label',
      id: 2,
    },
    creationDate: '2015-12-12',
    name: 'name',
    updateDate: '2015-12-12',
    id: 2,
  },
];
describe('Sector Enums reducer', () => {
  it('should return the initial state', () => {
    expect(reducer(undefined, {type: undefined})).toEqual(initialState);
  });

  it('should handle getSectorEnums action', () => {
    const action = getSectorEnums();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      loading: true,
      error: false,
      success: false,
    });
  });

  it('should handle getSectorEnumsWithSuccess action ', () => {
    const action = getSectorEnumsWithSuccess(programEnumsExamples);
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      sectorEnums: action.payload,
      loading: false,
      error: false,
      success: true,
    });
  });

  it('should handle getSectorEnumsWithError action', () => {
    const action = getSectorEnumsWithError();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      loading: false,
      error: true,
      success: false,
    });
  });

  it('should handle cleanUp action', () => {
    const action = cleanUp();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
    });
  });
});
