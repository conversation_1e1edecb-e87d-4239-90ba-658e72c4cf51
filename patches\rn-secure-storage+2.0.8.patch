diff --git a/node_modules/rn-secure-storage/ios/RNSecureStorage.podspec b/node_modules/rn-secure-storage/RNSecureStorage.podspec
similarity index 50%
rename from node_modules/rn-secure-storage/ios/RNSecureStorage.podspec
rename to node_modules/rn-secure-storage/RNSecureStorage.podspec
index 72468f7..d3ff914 100644
--- a/node_modules/rn-secure-storage/ios/RNSecureStorage.podspec
+++ b/node_modules/rn-secure-storage/RNSecureStorage.podspec
@@ -1,23 +1,20 @@
+require 'json'
+
+package = JSON.parse(File.read(File.join(__dir__, 'package.json')))
 
 Pod::Spec.new do |s|
   s.name         = "RNSecureStorage"
-  s.version      = "1.0.82"
+  s.version      = package["version"]
   s.summary      = "RNSecureStorage"
-  s.description  = <<-DESC
-                    Secure Storage for React Native (Android & iOS)
-                   DESC
-  s.homepage     = "https://github.com/talut/rn-secure-storage"
-  s.license      = { :type => "MIT", :file => "../LICENSE.md" }
+  s.description  = package["description"]
+  s.homepage     = package["homepage"]
+  s.license      = { :type => package["license"], :file => "./LICENSE.md" }
   s.author       = { "Talut Tasgiran" => "<EMAIL>" }
   s.platform     = :ios, "7.0"
-  s.source       = { :git => "https://github.com/talut/rn-secure-storage.git", :tag => "1.0.82" }
+  s.source       = { :git => "https://github.com/talut/rn-secure-storage.git", :tag => "v#{s.version}" }
   s.source_files  = "**/*.{h,m}"
   s.requires_arc = true
 
-
   s.dependency "React"
-  #s.dependency "others"
 
 end
\ No newline at end of file
-
-  
\ No newline at end of file
