import {call} from 'redux-saga/effects';
import {expectSaga} from 'redux-saga-test-plan';
import baseRequest from '@src/api/request';
import {ChannelsWebServices} from '@src/api/channels';
import {handleCostFeatures} from '@src/store/channels/cost/saga';
import {handleRiskFeatures} from '@src/store/channels/risk/saga';
import {
  getRiskFeaturesWithError,
  getRiskFeaturesWithSuccess,
} from '@src/store/channels/risk/slice';
import {RiskChannelSummary} from '@src/models/channelsSummary/riskChannelSummary';

describe('risk saga', () => {
  const projectId = 22;
  const reportingDateFilter = {};
  const features: RiskChannelSummary = {
    channelStatus: 'PRIMARY',
  };
  it('should yield an api call', () => {
    expectSaga(handleCostFeatures)
      .provide([
        [
          call(
            baseRequest.get,
            ChannelsWebServices.WS_GET_RISK_SUMMARY(projectId),
          ),
          {params: reportingDateFilter},
        ],
      ])
      .run();
  });

  it('should handle risk features successfully', () => {
    expectSaga(handleCostFeatures)
      .provide([
        [
          call(
            baseRequest.get,
            ChannelsWebServices.WS_GET_RISK_SUMMARY(projectId),
          ),
          {params: reportingDateFilter},
        ],
      ])
      .put(getRiskFeaturesWithSuccess({features}))
      .run();
  });

  it('should handle risk features with error', () => {
    expectSaga(handleRiskFeatures)
      .provide([
        [
          call(
            baseRequest.get,
            ChannelsWebServices.WS_GET_RISK_SUMMARY(projectId),
          ),
          {throw: true},
        ],
      ])
      .put(getRiskFeaturesWithError())
      .run();
  });
});
