import { <PERSON><PERSON><PERSON><PERSON>, ThemeProvider } from "@react-navigation/native";

import { AuthStack } from "./stacks/AuthenticationStack";
import { AppStack } from "./stacks/ApplicationStack";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { navigationRef } from "@src/utils/navigationUtils";
import SplashScreen from "react-native-splash-screen";
import messaging from "@react-native-firebase/messaging";
import { onMessageHandler, requestUserPermission } from "../../pushNotificationHelper";
import notifee, { EventType } from "@notifee/react-native";
import { AuthManager } from "@src/auth/AuthManager";
import VersionNotUpdateStack from "src/components/VersionNotUpdate";
import { setNotification, setOpenedFromNotification } from "@src/store/notification/slice";
import useCheckApplicationVersion from "@src/hooks/useCheckApplicationVersion";
import { setCurrentProjectId } from "@src/store/project/slice";
import { NotificationModel } from "@src/models/notification";
import useInternetConnectivity from "@src/hooks/useInternetConnectivity";
import { globalSelector } from "@src/store/global/selectors";
import { setUserToken } from "@src/store/authentication/slice";
import { authenticationSelector } from "@src/store/authentication/selectors";
import Toast from "react-native-toast-message";
import NoInternetModal from "@src/components/NoInternalModal";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { SafeAreaProvider } from "react-native-safe-area-context";

/**
 * Router component manages the navigation flow of the application.
 * It determines whether to display authentication screens or the main application screens based on the user's authentication status and app version.
 * It handles initial setup such as hiding the splash screen, requesting user permission for push notifications,
 * checking application version, and initializing necessary data.
 * It checks if the application is opened with a push notification , if yes it will trigger handlingResettingStackAndActionsWhenReceivingNotifications
 * to display the corresponding screen
 * @param {object} linking - Linking configuration for deep linking
 * @param {object} initialNotification - Initial notification received when the app is launched from a notification
 * @returns {JSX.Element} Router component
 */

type RouterProps = {
  linking: any;
  initialNotification: NotificationModel | null
}

export const Router = ({ linking, initialNotification }: RouterProps) => {
  //const [userToken, setUserToken] = useState<string | null>(null);

  useEffect(() => {
    setTimeout(() => {
      SplashScreen.hide();
    }, 1500);
  }, []);

  useEffect(() => {
    if (initialNotification) {
      handlingResettingStackAndActionsWhenReceivingNotifications(
        initialNotification
      );
    }
  }, [initialNotification]);

  React.useEffect(() => {
    requestUserPermission();
  }, []);

  const dispatch = useDispatch();

  const [isVersionUpdated, setIsVersionUpdated] = useState<boolean>(true);

  useCheckApplicationVersion(setIsVersionUpdated);

  /**
   * Handle navigation and state updates when receiving a notification
   * This function updates Redux state and triggers navigation to the appropriate screen
   *
   * @param notificationModel The notification data
   */
  const handlingResettingStackAndActionsWhenReceivingNotifications = (
    notificationModel: NotificationModel
  ) => {
    // Update Redux state
    dispatch(setOpenedFromNotification(true));
    dispatch(setNotification(notificationModel));

    // Set current project ID if available
    if (notificationModel.projectId !== null) {
      dispatch(setCurrentProjectId(notificationModel.projectId));
    }

    // Import NavigationService dynamically to avoid circular dependencies
    import("@src/services/NavigationService").then(({ default: NavigationService }) => {
      NavigationService.navigateToNotification(notificationModel);
    });
  };

  const onForegroundEvent = ({ type, detail }: Event) => {
    const { notification } = detail;
    const notificationModel = notification.data.notificationModel;
    const object = JSON.parse(notificationModel);
    switch (type) {
      case EventType.CHANNEL_BLOCKED.toString():
        break;
      case EventType.DISMISSED.toString():
        break;
      case EventType.PRESS.toString():
        handlingResettingStackAndActionsWhenReceivingNotifications(object);
    }
  };

  useEffect(() => {
    const bootstrapCheckingAuthentication = async () => {
      const accessToken = await AuthManager.getAccessTokenAsync();
      dispatch(setUserToken(accessToken || null));
    };
    bootstrapCheckingAuthentication();
  }, []);

  useEffect(() => {
    const unsubscribeNotification = messaging().onNotificationOpenedApp(
      message => {
        const notificationModel = message?.data?.notificationDto;
        const object = JSON.parse(notificationModel!!);
        handlingResettingStackAndActionsWhenReceivingNotifications(object);
      }
    );
    const unsubMessaging = messaging().onMessage(onMessageHandler);
    const unsubscribeNotifee = notifee.onForegroundEvent(onForegroundEvent);

    return () => {
      unsubMessaging();
      unsubscribeNotifee();
      unsubscribeNotification();
    };
  }, []);

  const { theme } = useSelector(globalSelector);
  const { userToken } = useSelector(authenticationSelector);
  const { isConnected } = useInternetConnectivity();

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <ThemeProvider value={theme}>
        <SafeAreaProvider>
          <NavigationContainer ref={navigationRef} linking={linking}>
            {!isVersionUpdated ? (
              <VersionNotUpdateStack
                setVersion={setIsVersionUpdated}
                connected={isConnected}
              />
            ) : userToken !== null ? (
              <AppStack />
            ) : (
              <AuthStack />
            )}
          </NavigationContainer>
          <NoInternetModal />
          <Toast />
        </SafeAreaProvider>
      </ThemeProvider>
    </GestureHandlerRootView>
  );
};
