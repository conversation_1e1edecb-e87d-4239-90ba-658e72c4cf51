export type LinearColor = {
  firstColor: string;
  secondColor: string;
};
export const CHANNEL_STATUS_COLOR_VALUE: Record<string, string[]> = {
  PRIMARY: ['#808080', '#B3B3B3'],
  SUCCESS: ['#81ECEC', '#00CEC9'],
  DANGER: ['#E64748', '#FF7675'],
  WARNING: ['#FBCB6E', '#ffeaa7'],
};

export const CHANNEL_STATUS_VALUE: Record<string, string> = {
  DANGER: 'Red',
  WARNING: 'Amber',
  SUCCESS: 'Green',
  PRIMARY: 'Grey',
};

export const LINEAR_COLORS_FOR_EACH_STATUS_COLOR: Record<string, LinearColor> =
  {
    DANGER: {
      firstColor: '#E64748',
      secondColor: '#FF7675',
    },
    WARNING: {
      firstColor: '#FDCB6E',
      secondColor: '#FFEAA7',
    },
    SUCCESS: {
      firstColor: '#00CEC9',
      secondColor: '#81ECEC',
    },
    PRIMARY: {
      firstColor: '#808080',
      secondColor: '#B3B3B3',
    },
  };

export const CHANNEL_STATUS = {
  DANGER: 'DANGER',
  WARNING: 'WARNING',
  SUCCESS: 'SUCCESS',
  PRIMARY: 'PRIMARY',
};

export const overallStatusMap: {[key: string]: number} = {
  DANGER: 4,
  WARNING: 3,
  SUCCESS: 2,
  PRIMARY: 1,
};
