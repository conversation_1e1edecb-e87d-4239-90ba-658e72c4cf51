import React from 'react';
import {TextInput} from 'react-native';
import {AddComment} from '@src/components/AddComment';
import {renderWithProviders} from '@src/utils/utils-for-tests';

const handleUpdateListInteractions = jest.fn();
const textInputRef = React.createRef<TextInput>();
const focusedInteractionId = 1;
const handleSettingFocusedInteraction = jest.fn();

describe('AddComment', () => {
  it('Should render and match the snapshot', () => {
    const firstChild = renderWithProviders(
      <AddComment
        handleUpdateListInteractions={handleUpdateListInteractions}
        textInputRef={textInputRef}
        focusedInteractionId={focusedInteractionId}
        handleSettingFocusedInteraction={handleSettingFocusedInteraction}
        userId={'userId'}
      />,
    ).toJSON();
    expect(firstChild).toMatchSnapshot();
  });
});
