import React from 'react';
import {G, Path, Svg} from 'react-native-svg';

const PreviousChannelArrowLessOpacity = (props: {style: {}}) => (
  <Svg
    width="9.066"
    height="16.983"
    viewBox="0 0 9.066 16.983"
    style={props.style}>
    <G
      id="style_doutone"
      data-name="style=doutone"
      transform="translate(0.25 0.313)">
      <G id="arrow-long-right" transform="translate(0 0)">
        <Path
          id="vector_Stroke__2"
          data-name="vector (Stroke)_2"
          d="M22.283,4.483a.794.794,0,0,1,0,1.124l-6.667,6.666a.265.265,0,0,0,0,.375l6.667,6.666a.795.795,0,0,1-1.124,1.124l-6.666-6.666a1.854,1.854,0,0,1,0-2.622l6.666-6.666A.8.8,0,0,1,22.283,4.483Z"
          transform="translate(-13.95 -4.25)"
          fill="#eaeef4"
          stroke="#eaeef4"
          stroke-width="0.5"
          fill-rule="evenodd"
        />
      </G>
    </G>
  </Svg>
);

export default PreviousChannelArrowLessOpacity;
