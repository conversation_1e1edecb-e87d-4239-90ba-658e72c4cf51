import { StyleSheet } from "react-native";
import { PERCENTAGES } from "@src/constants";
import { COMMON_COLORS } from "@src/theme/colors";
import layout from "@src/theme/layout";
import { correspondentHeight, correspondentWidth } from "@src/utils/imageUtils";
import typography from "@src/theme/fonts";

export const bottomSheetStyles = StyleSheet.create({
  modal: {
    margin: 0
  },
  blurContainer: {
    position: "absolute",
    backgroundColor: COMMON_COLORS.BLUE_40,
    opacity: 0.3,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0
  },
  container: {
    flex: 1
  },
  twoButtonsContainer: {
    marginTop: correspondentHeight(30),
    ...layout.rowVCenterSpaceBetween
  },
  content: {
    backgroundColor: COMMON_COLORS.WHITE,
    padding: 20,
    paddingTop: correspondentHeight(10),
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20
  },
  closeButton: {
    alignSelf: "flex-end",
    marginBottom: correspondentHeight(10)
  },
  closeText: {
    fontSize: typography.fontSizes.lg,
    color: COMMON_COLORS.BLACK
  },
  title: {
    fontSize: typography.fontSizes.xl,
    fontWeight: "bold",
    marginTop: correspondentHeight(10),
    marginBottom: correspondentHeight(5)
  },
  description: {
    fontSize: typography.fontSizes.md,
    marginBottom: correspondentHeight(20)
  },
  button: {
    flex: 1,
    padding: 12,
    paddingHorizontal: correspondentWidth(26),
    alignItems: "center",
    borderRadius: 14,
    width: PERCENTAGES["50"],
    backgroundColor: COMMON_COLORS.GREY_10
  },
  buttonText: {
    color: COMMON_COLORS.WHITE,
    fontSize: typography.fontSizes.xs,
    fontWeight: "700"
  },
  line: {
    width: "28%",
    height: correspondentHeight(5),
    backgroundColor: COMMON_COLORS.GREY_15,
    alignSelf: "center",
    marginVertical: correspondentHeight(12),
    borderRadius: 11
  },
  filterFieldsTitle: {
    fontSize: typography.fontSizes.lg,
    color: COMMON_COLORS.BLUE_40,
    fontWeight: "700",
    marginBottom: correspondentHeight(30)
  }
});
