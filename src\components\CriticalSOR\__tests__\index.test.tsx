import {renderWithProviders} from '@src/utils/utils-for-tests';
import {CriticalSOR} from '@src/components/CriticalSOR';
import React from 'react';

describe('CriticalSOR', () => {
  const value = '22';
  it('should render and match the snapshot', () => {
    const tree = renderWithProviders(<CriticalSOR value={value} />).toJSON();
    expect(tree).toMatchSnapshot();
  });
  it('should render without crashing', () => {
    const tree = renderWithProviders(<CriticalSOR value={value} />).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
