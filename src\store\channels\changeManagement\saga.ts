import {call, put, select, takeEvery} from 'redux-saga/effects';
import {
  getChangeManagementFeatures,
  getChangeManagementFeaturesWithError,
  getChangeManagementFeaturesWithSuccess,
} from '@src/store/channels/changeManagement/slice';
import {ChannelsWebServices} from '@src/api/channels';
import {projectSelector} from '@src/store/project/selectors';
import baseRequest from '@src/api/request';

export function* handleChangeManagementFeatures() {
  try {
    const {projectId, reportingDateFilter} = yield select(projectSelector);
    const {data} = yield call(
      baseRequest.get,
      ChannelsWebServices.WS_GET_CHANGE_MANAGEMENT_SUMMARY(projectId),
      {params: reportingDateFilter},
    );
    yield put(getChangeManagementFeaturesWithSuccess({features: data}));
  } catch (e) {
    yield put(getChangeManagementFeaturesWithError());
  }
}

export default function* saga() {
  // same as incrementNumber.type or incrementNumber
  yield takeEvery(getChangeManagementFeatures, handleChangeManagementFeatures);
}
