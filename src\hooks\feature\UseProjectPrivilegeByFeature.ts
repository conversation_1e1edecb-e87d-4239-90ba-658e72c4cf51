import { useSelector } from "react-redux";
import { projectSelector } from "@src/store/project/selectors";
import { ProjectUtils } from "@src/utils/projectUtils";
import { usePrivilegeByFeature } from "@src/hooks/feature/UsePrivilegeByFeature";
import { PRIVILEGES } from "@src/constants/channels/privilege";

/*
 * This hook is used to check if the admin has make this feature enabled on the project
 */
export const useProjectPrivilegeByFeature = (feature: string) => {
  const { flattenKpis } = useSelector(projectSelector);
  if (flattenKpis != null) {
    const flattenKpisValues = Object.values(flattenKpis);
    return flattenKpisValues.some(kpis => {
      const filteredFeatures = kpis.filter(kpi => {
        return kpi?.feature?.code === feature;
      });
      return filteredFeatures.some(featureItem => {
        return featureItem.isIn === true;
      });
    });
  } else {
    return false;
  }
};

export const useFeatureVisibility = (featureKey: string | null) => {
  if (featureKey != null) {
    return ProjectUtils.isFeatureVisible(
      useProjectPrivilegeByFeature(featureKey),
      usePrivilegeByFeature(featureKey, PRIVILEGES.READ)
    );
  }
  return true;
};
