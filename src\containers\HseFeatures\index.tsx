import React from "react";
import { View } from "react-native";
import { useSelector } from "react-redux";
import Loader from "@src/components/Loader";
import FeatureContainer, { renderFeatureContainers } from "src/containers/FeatureContainer";
import { ObjectUtils } from "@src/utils/objectUtils";
import {
  COLLAB_FEATURES,
  COLLAB_FEATURES_TITLES,
  COLLAB_SUMMARY_FEATURE_KEY_WITH_FEATURES_CODE,
  getSectionValueByKey
} from "@src/constants/channels/features";
import { ChannelFeaturesDefaultValues } from "@src/constants/channelFeaturesDefaultValues";
import { KEYS_CHANNEL } from "@src/constants/channels/modules";
import { hseSelector } from "@src/store/channels/hse/selectors";

const HseFeatures = () => {
  const { features, loading } = useSelector(hseSelector);

  // Use the reusable function for specific features
  function renderDefaultValues() {
    const hseFeatures: FeatureContainer[] = [
      {
        featureKey: COLLAB_FEATURES.HSE.HSE_CRITICAL_SOR,
        featureTitle: COLLAB_FEATURES_TITLES.HSE.trir,
        featureValue: null,
        defaultFeatureValue: ChannelFeaturesDefaultValues.HSE.openSor
      },
      {
        featureKey: COLLAB_FEATURES.HSE.HSE_KPI,
        featureTitle: COLLAB_FEATURES_TITLES.HSE.openSor,
        featureValue: null,
        defaultFeatureValue: ChannelFeaturesDefaultValues.HSE.trir
      }
    ];

    return renderFeatureContainers(hseFeatures);
  }


  if (loading) {
    return <Loader show={true} />;
  }


  return (
    <View>
      {features
        ? ObjectUtils.convertObjectToKeyValuesArray(features).map((item) => {
          if (item.key !== KEYS_CHANNEL.COMMON.CHANNEL_STATUS) {
            return (
              <FeatureContainer
                key={item.key}
                featureKey={
                  getSectionValueByKey(COLLAB_SUMMARY_FEATURE_KEY_WITH_FEATURES_CODE, "HSE", item.key)
                }
                featureTitle={
                  getSectionValueByKey(COLLAB_FEATURES_TITLES, "HSE", item.key)
                }
                featureValue={item.value}
                defaultFeatureValue={
                  getSectionValueByKey(ChannelFeaturesDefaultValues, "HSE", item.key)
                }
              />
            );
          }
          return null;
        })
        : renderDefaultValues()}
    </View>
  );
};

export default HseFeatures;
