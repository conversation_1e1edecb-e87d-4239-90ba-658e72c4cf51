import {call, put, takeEvery} from 'redux-saga/effects';
import {
  setFcmToken,
  setFcmTokenWithError,
  setFcmTokenWithSuccess,
  setUser,
  setUserWithError,
  setUserWithSuccess,
} from '@src/store/authentication/slice';
import {AuthenticationWebServices} from '@src/api/login';
import baseRequest from '@src/api/request';

export function* handleUserSaga() {
  try {
    const {data} = yield call(
      baseRequest.get,
      AuthenticationWebServices.GET_CONNECTED_USER,
    );
    yield put(setUserWithSuccess(data));
  } catch (e) {
    yield put(setUserWithError());
  }
}

export function* handleFcmTokenSaga(action: {
  payload: {
    fireBaseToken: string;
  };
  type: string;
}) {
  try {
    const {fireBaseToken} = action.payload;
    const {data} = yield call(
      baseRequest.post,
      AuthenticationWebServices.UPDATE_FIREBASE_TOKEN,
      {},
      {
        params: {
          fireBaseToken,
        },
      },
    );
    yield put(setFcmTokenWithSuccess(data));
  } catch (e) {
    console.log(e);
    yield put(setFcmTokenWithError());
  }
}

export default function* authenticationSaga() {
  yield takeEvery(setUser, handleUserSaga);
  yield takeEvery(setFcmToken, handleFcmTokenSaga);
}
