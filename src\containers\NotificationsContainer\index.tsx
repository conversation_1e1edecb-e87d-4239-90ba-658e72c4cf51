import { View } from "react-native";
import React, { useCallback, useEffect, useMemo, useState, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import style from "./style";
import { notificationsSelector } from "@src/store/notifications/selectors";
import { ROUTE_NAMES } from "@src/constants/navigation";
import {
  deleteNotification,
  getNotificationsExistence,
  getNotificationsList,
  markAsRead,
  cleanUp as cleanUpNotifications
} from "@src/store/notifications/slice";
import { setNotification, setOpenedFromNotification } from "@src/store/notification/slice";
import ListNotifications from "@src/containers/ListNotifications";
import NotificationBottomModal from "@src/components/NotificationBottomModal";
import { projectSelector } from "@src/store/project/selectors";
import OverviewProject from "@src/components/OverviewProject";
import { notificationSelector } from "@src/store/notification/selectors";
import { MobileAlertType } from "@src/constants/notification";
import Loader from "@src/components/Loader";
import { NotificationModel } from "@src/models/notification";
import Error from "src/components/ErrorModal";
import { setCurrentProject } from "@src/store/project/slice";
import { useHandleErrorModal } from "@src/hooks/useHandleErrorModal";
import { globalStyles } from "@src/theme/style";
import NotificationsIconHeaderContainer from "@src/containers/headers/NotificationsIconHeaderContainer";
import ClickableImage from "@src/components/ClickableImage";
import { MenuDrawer } from "@src/svg/MenuDrawer";
import Header from "@src/components/Header";
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from "react-native-reanimated";

type Props = {
  toggleDrawer: () => void;
};

/**
 * NotificationsContainer component displays a list of notifications with header and project overview
 * Optimized for performance and to handle drawer state correctly
 */
const NotificationsContainer = (props: Props) => {
  const { toggleDrawer } = props;
  const dispatch = useDispatch();

  // Get state from Redux
  const { notifications, loading, markAsReadSuccess, error, success } =
    useSelector(notificationsSelector);
  const { notification, isOpenedFromNotification } = useSelector(notificationSelector);
  const { projectId, currentProject, reportingDateFilter } = useSelector(projectSelector);

  // Local state
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isErrorModalVisible, setIsErrorModalVisible] = useState(false);

  // Handle error modal
  useHandleErrorModal(error, () => {
    setIsErrorModalVisible(true);
  });

  // Memoized empty list message
  const emptyListMessage = useMemo(() => {
    if (success) {
      if (projectId !== null) {
        return "You don't have any notification yet";
      } else {
        return "Notifications not found for all projects";
      }
    }
    return "";
  }, [projectId, success]);

  // Callbacks for handling notifications
  const handleRefreshNotificationsPage = useCallback(() => {
    dispatch(getNotificationsList());
  }, [dispatch]);

  const handleOpenModal = useCallback(() => {
    setIsModalVisible(true);
  }, []);

  const handleCloseModal = useCallback(() => {
    setIsModalVisible(false);
  }, []);

  const handleSetNotification = useCallback((notificationModel: NotificationModel) => {
    dispatch(setNotification(notificationModel));
  }, [dispatch]);

  const handleMarkAsRead = useCallback((notificationId: number) => {
    dispatch(markAsRead({ notificationId }));
  }, [dispatch]);

  const handleDeleteNotification = useCallback((notificationId: number) => {
    dispatch(deleteNotification({ notificationId }));
  }, [dispatch]);

  // Effect to handle notification opening
  useEffect(() => {
    if (notification && isOpenedFromNotification) {
      if (notification?.mobileAlertType !== MobileAlertType.REDIRECTION) {
        handleOpenModal();
        dispatch(setOpenedFromNotification(false));
      }
    }
  }, [notification, isOpenedFromNotification, dispatch, handleOpenModal]);

  // Effect to refresh notifications when marked as read
  useEffect(() => {
    if (
      notification?.mobileAlertType !== MobileAlertType.REDIRECTION &&
      markAsReadSuccess
    ) {
      dispatch(getNotificationsList());
    }
  }, [markAsReadSuccess, notification, dispatch]);

  // Effect to set current project
  useEffect(() => {
    if (projectId !== null || currentProject !== null) {
      dispatch(setCurrentProject(projectId));
    }
  }, [projectId, currentProject, dispatch]);

  // Use a ref to track if we've already fetched data
  const dataFetched = useRef(false);

  // Effect to fetch notifications ONLY on first mount, not on re-renders
  useEffect(() => {
    // Only fetch if we haven't fetched yet
    if (!dataFetched.current) {
      dispatch(getNotificationsExistence({ projectId }));
      dispatch(getNotificationsList());
      dataFetched.current = true;
    }

    // Cleanup when component unmounts
    return () => {
      dispatch(cleanUpNotifications());
    };
  }, []); // Empty dependency array - only run on mount/unmount

  const active = useSharedValue(false)

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform : [{scale : active ? withTiming(0.8) : withTiming(1)}]
    }
  })

  // Show loader only when loading
  if (loading) {
    return <Loader show={true} />;
  }


  return (
    <Animated.View style={[style.container , animatedStyle]}>
      <Header
        routeName={ROUTE_NAMES.NOTIFICATIONS}
        rightContent={
          <View style={globalStyles.iconsContainer}>
            <NotificationsIconHeaderContainer projectId={projectId!!} />
            <ClickableImage
              SvgComponent={<MenuDrawer style={globalStyles.drawerLogoImage} />}
              onPress={() => {
                active.value = true
              }}
              url={null}
              imageStyle={globalStyles.notificationLogoImage}
            />
          </View>
        }
        showBackButton={true}
      />

      {currentProject !== null && (
        <View style={{ zIndex: 44 }}>
          <OverviewProject
            toggleDrawer={toggleDrawer}
            currentProject={currentProject!!}
            period={reportingDateFilter}
          />
        </View>
      )}

      <ListNotifications
        handleDeleteNotification={handleDeleteNotification}
        toggleDrawer={toggleDrawer}
        handleRefreshNotificationsPage={handleRefreshNotificationsPage}
        handleOpenModal={handleOpenModal}
        handleSetNotifications={handleSetNotification}
        handleMarkAsRead={handleMarkAsRead}
        notifications={notifications || []}
        isLoading={loading}
        emptyListMessage={emptyListMessage}
      />

      {isModalVisible &&
        notification?.mobileAlertType !== MobileAlertType.REDIRECTION && (
          <NotificationBottomModal
            notification={notification}
            visible={isModalVisible}
            handleMarkAsRead={handleMarkAsRead}
            onClose={handleCloseModal}
          />
        )}

      {(error || isErrorModalVisible) && !loading && <Error />}
    </Animated.View>
  );
};

export default NotificationsContainer;
