import React from "react";
import { View } from "react-native";
import { useSelector } from "react-redux";
import Loader from "@src/components/Loader";
import FeatureContainer, { renderFeatureContainers } from "src/containers/FeatureContainer";
import { costSelector } from "@src/store/channels/cost/selectors";
import { ObjectUtils } from "@src/utils/objectUtils";
import {
  COLLAB_FEATURES,
  COLLAB_FEATURES_TITLES,
  COLLAB_SUMMARY_FEATURE_KEY_WITH_FEATURES_CODE,
  getSectionValueByKey
} from "@src/constants/channels/features";
import { ChannelFeaturesDefaultValues } from "@src/constants/channelFeaturesDefaultValues";
import { KEYS_CHANNEL } from "@src/constants/channels/modules";

const CostFeatures = () => {
  const { features, loading } = useSelector(costSelector);

  function renderDefaultValues() {
    const costFeatures: FeatureContainer[] = [
      {
        featureKey: COLLAB_FEATURES.COST.COST_CPI,
        featureTitle: COLLAB_FEATURES_TITLES.COST.cpi,
        featureValue: null,
        defaultFeatureValue: ChannelFeaturesDefaultValues.COST.cpi
      },
      {
        featureKey: COLLAB_FEATURES.COST.COST_GID,
        featureTitle: COLLAB_FEATURES_TITLES.COST.forecast,
        featureValue: null,
        defaultFeatureValue: ChannelFeaturesDefaultValues.COST.forecast
      },
      {
        featureKey: COLLAB_FEATURES.COST.COST_BUDGET,
        featureTitle: COLLAB_FEATURES_TITLES.COST.revisedBudgetTic,
        featureValue: null,
        defaultFeatureValue: ChannelFeaturesDefaultValues.COST.revisedBudgetTic
      }
    ];

    return renderFeatureContainers(costFeatures);
  }


  if (loading) {
    return <Loader show={true} />;
  }
  return (
    <View>
      {features
        ? ObjectUtils.convertObjectToKeyValuesArray(features).map((item, index) => {
          if (item.key !== KEYS_CHANNEL.COMMON.CHANNEL_STATUS) {
            return (
              <FeatureContainer
                key={item.key}
                featureKey={getSectionValueByKey(COLLAB_SUMMARY_FEATURE_KEY_WITH_FEATURES_CODE, "COST", item.key)}
                featureTitle={COLLAB_FEATURES_TITLES.COST[item.key]}
                featureValue={item.value}
                defaultFeatureValue={
                  getSectionValueByKey(ChannelFeaturesDefaultValues, "COST", item.key)
                }
              />
            );
          }
          return null;
        })
        : renderDefaultValues()}
    </View>
  );
};

export default CostFeatures;
