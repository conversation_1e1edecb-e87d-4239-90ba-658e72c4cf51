import {expectSaga} from 'redux-saga-test-plan';
import {handleApplicationVersionUpdated} from '@src/store/global/saga';
import {
  setIsApplicationVersionUpdatedWithError,
  setIsApplicationVersionUpdatedWithSuccess,
} from '@src/store/global/slice';

describe('globalSagas', () => {
  it('should yield an api call', () => {
    expectSaga(handleApplicationVersionUpdated).run();
  });

  it('should handle setIsApplicationVersionUpdated sagas successfully', () => {
    expectSaga(handleApplicationVersionUpdated)
      .put(setIsApplicationVersionUpdatedWithSuccess(true))
      .run();
  });

  it('should handle setIsApplicationVersionUpdated sagas with error', () => {
    expectSaga(handleApplicationVersionUpdated)
      .put(setIsApplicationVersionUpdatedWithError())
      .run();
  });
});
