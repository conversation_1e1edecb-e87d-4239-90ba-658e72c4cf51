import React from 'react';
import {CriticalRisk} from '@src/components/CriticalRisk';
import {renderWithProviders} from '@src/utils/utils-for-tests';

describe('CriticalRisk Component', () => {
  test('renders CriticalRisk component with value', () => {
    const {getByText} = renderWithProviders(<CriticalRisk value="42" />);
    const valueElement = getByText('42'); // Adjust the expected text if needed
    expect(valueElement).toBeTruthy();
  });

  test('displays the correct value', () => {
    const {getByText} = renderWithProviders(<CriticalRisk value="100" />);
    const valueElement = getByText('100');
    expect(valueElement).toBeTruthy();
  });

  it('Should render and match the snapshot', () => {
    const tree = renderWithProviders(<CriticalRisk value="100" />).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
