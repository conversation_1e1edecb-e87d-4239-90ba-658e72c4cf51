import React, {useEffect, useState} from 'react';
import linking from './linking';
import {Provider} from 'react-redux';
import {store} from '@src/store/store';
import {Router} from '@src/routes/Router';
import messaging from '@react-native-firebase/messaging';
import {NotificationModel} from '@src/models/notification';
import {ObjectUtils} from '@src/utils/objectUtils';
import 'react-native-gesture-handler';

// Main App component
export default function App() {
  // State to hold initial notification
  const [initialNotification, setInitialNotification] =
    useState<NotificationModel | null>(null);

  // Effect to handle getting initial notification on component mount
  useEffect(() => {
    messaging()
      .getInitialNotification()
      .then(response => {
        const notificationModel: NotificationModel =
          ObjectUtils.convertRemoteNotificationObjectToNotificationModel(
            response!!
          );
        if (notificationModel !== null) {
          setInitialNotification(notificationModel);
        } else {
          setInitialNotification(null);
        }
      })
      .catch(error => {
        console.log(error);
      });
  }, []);

  // Render the provider wrapping the router component with Redux store
  return (
    <Provider store={store()}>
      <Router initialNotification={initialNotification} linking={linking} />
    </Provider>
  );
}
