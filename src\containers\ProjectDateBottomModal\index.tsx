import React, {memo, useState} from 'react';
import {
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import Modal from 'react-native-modal';
import {TEST_IDS} from '@src/constants/strings';
import {bottomSheetStyles} from './style';
import CustomDatePicker from '@src/components/CustomDatePicker';
import {DateUtils} from '@src/utils/dateUtils';
import {invertMonthMap, monthMap} from '@src/utils/timeLine/constants';
import {globalStyles} from '@src/theme/style';

type ProjectDateBottomModalProps = {
  children?: React.ReactNode;
  handleApplyFilter: (arg: {year: number; month: number}) => void;
  handleCancelFilter?: () => void;
  visible?: boolean;
  reportingDateParams: {month: number; year: number};
  startDate: string;
  reportingDate: string;
};

const ProjectDateBottomModal = (props: ProjectDateBottomModalProps) => {
  const {
    visible,
    handleApplyFilter,
    handleCancelFilter,
    startDate,
    reportingDate,
    reportingDateParams,
  } = props;

  const extractedYearFromReportingDate = DateUtils.extractYearFromDate(
    reportingDateParams.year.toString(),
  );
  const extractedMonthFromReportingDate =
    invertMonthMap[reportingDateParams.month];

  const [currentYear, setCurrentYear] = useState(
    extractedYearFromReportingDate,
  );
  const [currentMonth, setCurrentMonth] = useState(
    extractedMonthFromReportingDate,
  );
  const years = DateUtils.extractYearsInterval(startDate, reportingDate);
  const months = DateUtils.extractMonthsFromYear(
    parseInt(currentYear, 10),
    startDate,
    reportingDate,
  );

  const handleUpdateDateFilter = (month: string, year: string) => {
    setCurrentMonth(month);
    setCurrentYear(year);
  };

  const handleApplyDateFilter = () => {
    if (handleApplyFilter) {
      const currentYearNumber = parseInt(currentYear, 10);
      const currentMonthNumber = monthMap[currentMonth];
      handleApplyFilter({
        year: currentYearNumber,
        month: currentMonthNumber,
      });
    }
  };

  return (
    <>
      <Modal
        statusBarTranslucent={false}
        isVisible={visible}
        style={bottomSheetStyles.modal}
        onBackButtonPress={handleApplyDateFilter}>
        <TouchableWithoutFeedback
          onPress={() => {
            if (handleCancelFilter) {
              handleCancelFilter();
            }
          }}>
          <View style={globalStyles.blurViewModal} />
        </TouchableWithoutFeedback>

        <View style={bottomSheetStyles.content}>
          <View style={bottomSheetStyles.line} />
          <Text style={bottomSheetStyles.filterFieldsTitle}>Calendar</Text>
          <Text style={bottomSheetStyles.filterFieldsSubTitle}>
            Choose another date if needed
          </Text>
          <CustomDatePicker
            handleUpdateDateFilter={handleUpdateDateFilter}
            selectedMonth={currentMonth}
            selectedYear={currentYear}
            years={years}
            months={months}
          />
          <View style={bottomSheetStyles.twoButtonsContainer}>
            <TouchableOpacity
              style={bottomSheetStyles.button}
              onPress={handleCancelFilter}>
              <Text style={{...bottomSheetStyles.buttonText, color: 'black'}}>
                Cancel
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              testID={TEST_IDS.APPLY_BUTTON}
              style={bottomSheetStyles.applyButton}
              onPress={handleApplyDateFilter}>
              <Text style={bottomSheetStyles.buttonText}>Apply</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </>
  );
};

export default memo(ProjectDateBottomModal);
