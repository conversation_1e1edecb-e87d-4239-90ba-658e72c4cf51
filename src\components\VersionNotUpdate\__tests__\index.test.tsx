import {renderWithProviders} from '@src/utils/utils-for-tests';
import React from 'react';
import VersionNotUpdate from '@src/components/VersionNotUpdate';

describe('VersionNotUpdate', function () {
  const setVersion = jest.fn();
  const toggleTheme = jest.fn();
  it('Should render and match the snapshot', () => {
    const tree = renderWithProviders(
      <VersionNotUpdate
        setVersion={setVersion}
        connected={false}
        toggleTheme={toggleTheme}
      />,
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
