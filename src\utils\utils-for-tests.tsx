/*
import React, {PropsWithChildren} from 'react';
import type {RenderOptions} from '@testing-library/react-native';
import {render} from '@testing-library/react-native';
import type {PreloadedState} from '@reduxjs/toolkit';
import {configureStore} from '@reduxjs/toolkit';
import {Provider} from 'react-redux';
import {RootState} from '@src/store/reducers';
import {rootReducer} from '@reduxjs/toolkit/src/tests/injectableCombineReducers.example';

// As a basic setup, import your same slice reducers

// This type interface extends the default options for render from RTL, as well
// as allows the user to specify other things such as initialState, store.
interface ExtendedRenderOptions extends Omit<RenderOptions, 'queries'> {
  preloadedState?: PreloadedState<RootState>;
  store?: AppStore;
}

export function renderWithProviders(
  ui: React.ReactElement,
  {
    preloadedState = {},
    // Automatically create a store instance if no store was passed in
    store = configureStore({
      reducer: {...rootReducer},
      preloadedState,
    }),
    ...renderOptions
  }: ExtendedRenderOptions = {},
) {
  function Wrapper({children}: PropsWithChildren<{}>): JSX.Element {
    return <Provider store={store}>{children}</Provider>;
  }

  // Return an object with the store and all of RTL's query functions
  return {store, ...render(ui, {wrapper: Wrapper, ...renderOptions})};
}
*/

import { AppStore, RootState, store } from "@src/store/store";
import { render, render as rtlRender, RenderOptions } from "@testing-library/react-native";
import { PreloadedState } from "@reduxjs/toolkit";
import React, { PropsWithChildren } from "react";
import { Provider } from "react-redux";
import { useNavigation } from "@react-navigation/core";
import { CustomTheme, lightTheme } from "@src/theme";
import { ThemeProvider } from "@react-navigation/native";


interface ExtendedRenderOptions extends Omit<RenderOptions, "queries"> {
  preloadedState?: PreloadedState<RootState>;
  initialStore?: AppStore;
}

export function renderWithProviders(
  ui: React.ReactElement,
  {
    //preloadedState = {},
    // Automatically create a store instance if no store was passed in
    //initialStore = store(preloadedState),
    ...renderOptions
  }: ExtendedRenderOptions = {}
) {
  function Wrapper({ children }: PropsWithChildren<{}>): JSX.Element {
    return <Provider store={store()}>{children}</Provider>;
  }

  return { store, ...render(ui, { wrapper: Wrapper, ...renderOptions }) };
}

const renderTest = (ui: any, { theme = lightTheme, ...options } = {}) => {
  // @ts-ignore
  const Wrapper = ({ children }): ComponentType => (
    <ThemeProvider value={theme as CustomTheme}>{children}</ThemeProvider>
  );
  // @ts-ignore
  return rtlRender(ui, { wrapper: Wrapper, ...options });
};

export * from "@testing-library/react-native";
// override React Testing Library's render with our own
export { renderTest };

export const useNavigationMock = useNavigation as jest.MockedFunction<
  typeof useNavigation
>;
