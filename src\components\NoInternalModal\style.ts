import {StyleSheet} from 'react-native';
import {PERCENTAGES} from '@src/constants';
import typography from '@src/theme/fonts';
import {COMMON_COLORS} from '@src/theme/colors';
import {correspondentHeight, correspondentWidth} from '@src/utils/imageUtils';

export const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 52, 147, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  blurContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  collabLogo: {
    top: correspondentHeight(22),
    position: 'absolute',
    alignSelf: 'center',
  },
  jesa<PERSON>ogo: {
    bottom: correspondentHeight(12),
    position: 'absolute',
    alignSelf: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    paddingHorizontal: correspondentWidth(51),
    paddingVertical: correspondentHeight(40),
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    width: correspondentWidth(340),
  },
  modalText: {
    fontSize: typography.fontSizes.md,
    marginBottom: correspondentHeight(10),
    textAlign: 'center',
  },
  no_internet_illustration: {
    alignSelf: 'center',
    marginBottom: correspondentHeight(25),
  },
  title: {
    color: COMMON_COLORS.BLUE_40,
    fontWeight: '700',
    fontSize: typography.fontSizes.lg,
    alignSelf: 'center',
    fontFamily: typography.fontFamily.montserratSemiBold,
    marginBottom: correspondentHeight(17),
  },
  subTitle: {
    color: COMMON_COLORS.GREY_20,
    fontSize: typography.fontSizes.xs,
    alignSelf: 'center',
    textAlign: 'center',
    fontFamily: typography.fontFamily.montserratRegular,
    marginBottom: correspondentHeight(30),
  },
  updateButton: {
    marginBottom: correspondentHeight(10),
    backgroundColor: COMMON_COLORS.PRIMARY,
    width: PERCENTAGES['100'],
    padding: 11,
    borderRadius: 14,
  },
  updateButtonText: {
    color: COMMON_COLORS.WHITE,
    fontSize: typography.fontSizes.sm,
    alignSelf: 'center',
    textAlign: 'center',
    fontFamily: typography.fontFamily.montserratBold,
  },
  closeButton: {
    marginBottom: correspondentHeight(10),
    backgroundColor: COMMON_COLORS.GREY_10,
    padding: 11,
    borderRadius: 14,
    width: PERCENTAGES['100'],
  },
  closeButtonText: {
    color: COMMON_COLORS.BLUE_10,
    fontSize: 13,
    alignSelf: 'center',
    textAlign: 'center',
    fontFamily: typography.fontFamily.montserratBold,
  },
});
