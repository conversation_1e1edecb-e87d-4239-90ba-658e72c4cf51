import {
  combineReducers,
  configureStore,
  PreloadedState,
} from '@reduxjs/toolkit';
import {reducers} from '@src/store/reducers';
import createSagaMiddleware from 'redux-saga';
import notificationsSaga from '@src/store/notifications/saga';
import projectSaga from '@src/store/project/saga';
import hseSaga from '@src/store/channels/hse/saga';
import qaSaga from '@src/store/channels/qa/saga';
import costSaga from '@src/store/channels/cost/saga';
import humanCapSaga from '@src/store/channels/humanCapital/saga';
import changeManagementSaga from '@src/store/channels/changeManagement/saga';
import cashFlowSaga from '@src/store/channels/cashFlow/saga';
import progressSaga from '@src/store/channels/progress/saga';
import saga from '@src/store/discussion/saga';
import programSaga from '@src/store/filterEnumsProjects/programEnums/saga';
import sectorSaga from '@src/store/filterEnumsProjects/sectorEnums/saga';
import buSaga from '@src/store/filterEnumsProjects/buEnums/saga';
import projectsSaga from '@src/store/projects/saga';
import authenticationSaga from '@src/store/authentication/saga';
import riskSaga from '@src/store/channels/risk/saga';
import schedulingSaga from '@src/store/channels/scheduling/saga';
import globalSaga from '@src/store/global/saga';

export const rootReducer = combineReducers({
  ...reducers,
});

let sagaMiddleware = createSagaMiddleware();

// Create the root reducer separately so we can extract the RootState type

const sagas = [
  notificationsSaga,
  saga,
  projectSaga,
  hseSaga,
  costSaga,
  qaSaga,
  riskSaga,
  humanCapSaga,
  cashFlowSaga,
  progressSaga,
  changeManagementSaga,
  programSaga,
  sectorSaga,
  schedulingSaga,
  buSaga,
  authenticationSaga,
  projectsSaga,
  globalSaga,
];
const middleware = [sagaMiddleware];

export const store = (preloadedState?: PreloadedState<RootState>) => {
  const configuredStore = configureStore({
    reducer: rootReducer,
    preloadedState,
    middleware,
  });

  sagas.forEach(item => sagaMiddleware.run(item));

  return configuredStore;
};

export type RootState = ReturnType<typeof rootReducer>;
export type AppStore = ReturnType<typeof store>;
