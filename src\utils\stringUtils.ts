export const thousandsFormat = (number: number) => {
  if (number) {
    return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
  if (number === 0) {
    return 0;
  }
  return null;
};

export const textEllipsis = (
  str: string,
  maxLength: number,
  ellipsis = '...',
) => {
  if (str.length > maxLength) {
    return str.slice(0, maxLength - ellipsis.length) + ellipsis;
  }
  return str;
};

export const isBlankString = (str: string) =>
  str ? str.trim().length === 0 : true;

export const capitalizeFirst = (str: string) =>
  !isBlankString(str) && str.charAt(0).toUpperCase() + str.slice(1);

export const removeSpaces = (str: string) =>
  str ? str.replace(/\s/g, '') : null;

export function addCommasToNumber(number: number): string {
  const numberString = number.toString();
  const parts = numberString.split('.');
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  return parts.join('.');
}

export const printMentionedUserInTextInput = (text: string) => {
  return `@${text}`;
};

export const extractUserNameFromTaggedPattern = (input: string): string => {
  const pattern = /@(\[.*?\])?\{([^\}]+)\}/;

  const match = input.match(pattern);
  if (match && match[1]) {
    return match[1].substring(1, match[1].length - 1);
  }
  return '';
};

export function transformString(input: string): string {
  const regex = /\((.*?)\)/g;
  return input.replace(regex, '{$1}');
}
