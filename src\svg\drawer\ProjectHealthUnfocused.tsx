import React from 'react';
import Svg, {G, <PERSON>} from 'react-native-svg';

export const ProjectHealthUnfocused = (props: {style: {}}) => {
  return (
    <Svg style={props.style} width="20" height="20" viewBox="0 0 20 20">
      <G
        id="Project_health"
        data-name="Project health"
        transform="translate(-2 -2)">
        <Path
          id="Tracé_52403"
          data-name="Tracé 52403"
          d="M17.832,9.555a1,1,0,0,0-1.664-1.109l-2.775,4.163-3.34-2.254a1.3,1.3,0,0,0-1.864.446L6.126,14.514a1,1,0,1,0,1.748.971l1.693-3.047,3.29,2.221a1.3,1.3,0,0,0,1.809-.356Z"
          fill="#66bcff"
        />
        <Path
          id="Tracé_52404"
          data-name="<PERSON>rac<PERSON> 52404"
          d="M7,2A5,5,0,0,0,2,7V17a5,5,0,0,0,5,5H17a5,5,0,0,0,5-5V7a5,5,0,0,0-5-5ZM4,7A3,3,0,0,1,7,4H17a3,3,0,0,1,3,3V17a3,3,0,0,1-3,3H7a3,3,0,0,1-3-3Z"
          fill="#66bcff"
          fill-rule="evenodd"
        />
      </G>
    </Svg>
  );
};
