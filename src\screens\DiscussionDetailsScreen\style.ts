import {StyleSheet} from 'react-native';
import {GLOBAL_MARGIN_HORIZONTAL, windowWidth} from '@src/constants';
import {correspondentHeight} from '@src/utils/imageUtils';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginHorizontal: GLOBAL_MARGIN_HORIZONTAL,
  },
  divider: {
    marginVertical: correspondentHeight(22),
    width: '100%',
    borderWidth: 0.2,
    backgroundColor: '#DDEBFF',
  },
  listPersonsContributed: {
    flexDirection: 'row',
  },
  profileImage: {
    borderRadius: 16,
    marginStart: -20,
    width: windowWidth * 0.07,
    aspectRatio: 1,
  },
  commentsContainer: {
    height: 22,
    flex: 1,
  },
  listSubscribers: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
});
