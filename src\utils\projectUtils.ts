import {COLLAB_MODULES_TITLES} from '../constants/channels/modules';
import {FilterParams} from '../store/projects/slice';
import {IMAGES} from '../constants/images';

export const ProjectUtils = {
  isModuleVisible(
    moduleProfileAccess: boolean,
    moduleProjectAccess: boolean,
  ): boolean {
    return moduleProfileAccess && moduleProjectAccess;
  },

  isFeatureVisible(
    featureProfileAccess: boolean,
    featureProjectAccess: boolean,
  ): boolean {
    return featureProfileAccess && featureProjectAccess;
  },

  isFilterOptionsArrayValuesEmpty(filter: FilterParams): boolean {
    const excludedKeys = ['year', 'month', 'platformCode', 'size', 'page'];
    for (const key in filter) {
      if (!excludedKeys.includes(key) && filter[key] !== null) {
        return false;
      }
    }
    return true;
  },

  getKeyByValue(object: any, value: any) {
    return Object.keys(object).find(key => object[key] === value);
  },

  returnIllustrationImage(title: string) {
    switch (title) {
      case COLLAB_MODULES_TITLES.HSE:
        return IMAGES.HSE_ILLUSTRATION_IMAGE;
      case COLLAB_MODULES_TITLES.SCHEDULING:
        return IMAGES.SCHEDULING_ILLUSTRATION_IMAGE;
      case COLLAB_MODULES_TITLES.COST:
        return IMAGES.COST_ILLUSTRATION_IMAGE;
      case COLLAB_MODULES_TITLES.HUMAN_CAP:
        return IMAGES.HUMAN_CAPITAL_ILLUSTRATION_IMAGE;
      case COLLAB_MODULES_TITLES.CASH:
        return IMAGES.CASH_ILLUSTRATION_IMAGE;
      case COLLAB_MODULES_TITLES.PROGRESS:
        return IMAGES.PROGRESS_ILLUSTRATION_IMAGE;
      case COLLAB_MODULES_TITLES.RISK_MGMT:
        return IMAGES.RISK_MANAGEMENT_ILLUSTRATION_IMAGE;
      case COLLAB_MODULES_TITLES.CHANGE_MGMT:
        return IMAGES.CHANGE_MGMT_ILLUSTRATION_IMAGE;
      case COLLAB_MODULES_TITLES.QA:
        return IMAGES.QA_ILLUSTRATION_IMAGE;
      default:
        return IMAGES.RISK_MANAGEMENT_ILLUSTRATION_IMAGE;
    }
  },
};
