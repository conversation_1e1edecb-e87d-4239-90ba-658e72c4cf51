import { StyleSheet } from "react-native";
import typography from "@src/theme/fonts";
import { COMMON_COLORS } from "@src/theme/colors";
import { correspondentHeight, correspondentWidth } from "@src/utils/imageUtils";

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COMMON_COLORS.WHITE_20,
    paddingHorizontal: correspondentWidth(70)
  },
  collab_logo: {
    alignSelf: "center",
    marginTop: correspondentHeight(64),
    marginBottom: correspondentHeight(118.45),
    height: correspondentHeight(52.1),
    width: correspondentWidth(52.1),
    aspectRatio: 1
  },
  not_updated_illustration: {
    alignSelf: "center",
    marginBottom: correspondentHeight(22.24)
  },
  title: {
    color: COMMON_COLORS.BLUE_40,
    fontWeight: "700",
    fontSize: typography.fontSizes.lg,
    alignSelf: "center",
    fontFamily: typography.fontFamily.montserratSemiBold,
    marginBottom: correspondentHeight(10)
  },
  subTitle: {
    color: COMMON_COLORS.GREY_20,
    fontSize: typography.fontSizes.xs,
    alignSelf: "center",
    textAlign: "center",
    fontFamily: typography.fontFamily.montserratRegular,
    marginBottom: correspondentHeight(32)
  },
  updateButton: {
    marginBottom: correspondentHeight(17),
    backgroundColor: COMMON_COLORS.PRIMARY,
    paddingVertical: correspondentHeight(11),
    borderRadius: 14
  },
  updateButtonText: {
    color: COMMON_COLORS.WHITE,
    fontSize: typography.fontSizes.xs + 1,
    alignSelf: "center",
    textAlign: "center",
    fontFamily: typography.fontFamily.montserratBold
  },
  closeButton: {
    backgroundColor: COMMON_COLORS.GREY_10,
    paddingVertical: correspondentHeight(11),
    borderRadius: 14
  },
  closeButtonText: {
    color: COMMON_COLORS.BLUE_10,
    fontSize: typography.fontSizes.xs + 1,
    alignSelf: "center",
    textAlign: "center",
    fontFamily: typography.fontFamily.montserratBold
  }
});
