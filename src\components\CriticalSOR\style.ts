import {StyleSheet} from 'react-native';
import typography from '@src/theme/fonts';
import {COMMON_COLORS} from '@src/theme/colors';

const style = StyleSheet.create({
  previousArrow: {
    marginHorizontal: 12,
  },
  valueEvolution: {
    fontSize: 14,
    color: COMMON_COLORS.BLUE_30,
    fontWeight: 'bold',
    fontFamily: typography.fontFamily.montserratSemiBold,
  },
  arrowStyle: {
    marginRight: 8,
  },
  valueSOR: {
    color: COMMON_COLORS.BLUE_40,
    fontSize: 16,
    fontWeight: '800',
  },
});

export default style;
