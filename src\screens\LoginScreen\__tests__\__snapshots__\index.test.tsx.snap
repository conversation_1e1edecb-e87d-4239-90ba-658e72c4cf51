// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Login Screen Tests Should render and match the snapshot 1`] = `
<RCTSafeAreaView
  style={
    {
      "paddingHorizontal": 38,
    }
  }
>
  <Image
    background={false}
    height={93.38000000000001}
    onSize={[Function]}
    source={
      {
        "testUri": "../../../assets/images/collab_logo_image.png",
      }
    }
    style={
      [
        {
          "alignSelf": "center",
          "marginVertical": 40.019999999999996,
        },
        {
          "height": NaN,
          "width": NaN,
        },
      ]
    }
    width={90}
  />
  <View
    style={
      {
        "alignSelf": "center",
        "width": 487.5,
      }
    }
  >
    <Text
      style={
        {
          "color": "#003493",
          "fontFamily": "Montserrat-Black",
          "fontSize": 26,
          "fontWeight": "bold",
          "textAlign": "center",
          "textTransform": "uppercase",
          "width": "100%",
        }
      }
    >
      Welcome Back !
    </Text>
    <Text
      style={
        {
          "color": "#394861",
          "fontFamily": "Montserrat-Regular",
          "fontSize": 15,
          "marginBottom": 80.03999999999999,
          "textAlign": "center",
        }
      }
    >
      Please use your Jesa credentials to login into Collab
    </Text>
  </View>
  <Image
    background={false}
    onSize={[Function]}
    source={
      {
        "testUri": "../../../assets/images/splashIllustration.png",
      }
    }
    style={
      [
        {
          "alignSelf": "center",
          "marginBottom": 200.1,
        },
        {
          "height": NaN,
          "width": NaN,
        },
      ]
    }
  />
  <View
    onStartShouldSetResponder={[Function]}
    testID="Get Started"
  />
  <Image
    background={false}
    onSize={[Function]}
    source={
      {
        "testUri": "../../../assets/images/jesa_logo_image.png",
      }
    }
    style={
      [
        {
          "alignSelf": "center",
          "marginTop": 26.68,
        },
        {
          "height": NaN,
          "width": NaN,
        },
      ]
    }
  />
</RCTSafeAreaView>
`;
