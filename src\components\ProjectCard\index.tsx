import React from 'react';
import {
  ActivityIndicator,
  ImageBackground,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import Image from '@src/components/ScalableImage';
import styles from './style';
import {IMAGES} from '@src/constants/images';
import {TEST_IDS} from '@src/constants/strings';
import {CustomLinearGradient} from 'src/components/CustomLinearGradient';
import useGetClientImage from '@src/hooks/useGetClientImage';
import useGetProjectImage from '@src/hooks/useGetProjectImage';
import {
  correspondentHeight,
  returnFinalBase64String,
} from '@src/utils/imageUtils';
import {LinearColor} from '@src/constants/channels/channelStatusColor';
import {ArrowProjectCard} from '@src/svg/ArrowProjectCard';

type ProjectCardProps = {
  onClick: () => void;
  projectPictureLink: string;
  clientLogo: string;
  title: string;
  reference: string;
  colorName: string;
  linearColors: LinearColor;
};
const ProjectCard = (props: ProjectCardProps) => {
  const {
    onClick,
    clientLogo,
    title,
    reference,
    colorName,
    linearColors,
    projectPictureLink,
  } = props;

  const {clientImage: clientImageLogoFromHook, loadingClientImage} =
    useGetClientImage({
      clientUrl: clientLogo,
    });

  const {projectImage: projectImageFromHook, loadingProjectImage} =
    useGetProjectImage({projectPictureLink});

  const renderProjectImage = () => {
    if (loadingProjectImage) {
      // Render a loader while loading
      return (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color="#0000ff" />
        </View>
      );
    } else if (projectImageFromHook.length !== 0) {
      // Render the image if available
      return (
        <ImageBackground
          blurRadius={2}
          source={{
            uri: returnFinalBase64String(projectImageFromHook),
          }}
          style={[StyleSheet.absoluteFillObject]}
          borderRadius={22}
        />
      );
    } else {
      // Render a default image if projectImageFromHook is empty
      return (
        <ImageBackground
          blurRadius={2}
          source={IMAGES.DEFAULT_IMAGE_CLIENT_AND_PROJECT}
          style={[StyleSheet.absoluteFillObject]}
          borderRadius={22}
        />
      );
    }
  };

  const renderClientImage = () => {
    if (loadingClientImage) {
      // Render a loader while loading
      return (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color="#0000ff" />
        </View>
      );
    } else if (clientImageLogoFromHook.length !== 0) {
      // Render the client image if available
      return (
        <Image
          height={correspondentHeight(34)}
          source={{
            uri: returnFinalBase64String(clientImageLogoFromHook),
          }}
        />
      );
    } else {
      // Render a default user avatar if clientImageLogoFromHook is empty
      return (
        <Image height={correspondentHeight(38)} source={IMAGES.USER_AVATAR} />
      );
    }
  };

  return (
    <>
      <TouchableOpacity
        testID={TEST_IDS.PROJECT_CARD}
        style={styles.parentContainer}
        activeOpacity={1}
        onPress={onClick}>
        {renderProjectImage()}
        <TouchableOpacity
          activeOpacity={1}
          onPress={onClick}
          style={styles.childContainer}>
          <View style={styles.providerImageAndProjectStatusContainer}>
            {renderClientImage()}
            <CustomLinearGradient
              firstColor={linearColors.firstColor}
              secondColor={linearColors.secondColor}
              style={styles.projectStatus}
              x_start={0}
              y_start={0}
              x_end={1}
              y_end={0}>
              <Text style={styles.colorName}>{colorName}</Text>
            </CustomLinearGradient>
          </View>
          <View style={styles.titleAndReferenceProjectContainer}>
            <View style={styles.textsContainer}>
              <Text style={styles.title}>{title}</Text>
              <Text style={styles.reference}>{reference}</Text>
            </View>
            <View style={styles.button}>
              <ArrowProjectCard />
            </View>
          </View>
        </TouchableOpacity>
      </TouchableOpacity>
      {/*<Loader show={loading} />*/}
      {/*<Loader show={loadingProjectImage} />*/}
    </>
  );
};

export default ProjectCard;
