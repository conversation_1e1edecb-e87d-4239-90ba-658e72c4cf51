import React, { Dispatch, SetStateAction } from "react";
import { Alert, Pressable, Text, TouchableOpacity, View } from "react-native";
import { styles } from "./style";
import { NotUpdated } from "@src/svg/modals/NotUpdated";
import { CollabLogo } from "@src/svg/CollabLogo";
import { useSelector } from "react-redux";
import { globalSelector } from "@src/store/global/selectors";
import { COMMON_COLORS } from "@src/theme/colors";
import typography from "@src/theme/fonts";

type VersionNotUpdatedModalProps = {
  setVersion: Dispatch<SetStateAction<boolean>>;
  connected: boolean;
};
const VersionNotUpdate = (props: VersionNotUpdatedModalProps) => {
  const { setVersion, connected } = props;

  const handleOpenPlayStore = () => {
    Alert.alert("The Application is not ready to be published in the store");
  };

  const handleContinuingAnywayWithoutUpdatingTheApplication = () => {
    setVersion(true);
  };

  const { theme } = useSelector(globalSelector);

  return (
    <View style={styles.container}>
      <CollabLogo style={styles.collab_logo} />

      <NotUpdated style={styles.not_updated_illustration} />
      <Text style={styles.title}>Updated Version</Text>
      <Text style={styles.subTitle}>
        This version of the application is obsolete, please download the latest
        version from the marketplace.
      </Text>
      <TouchableOpacity
        style={styles.updateButton}
        onPress={handleOpenPlayStore}>
        <Text style={styles.updateButtonText}>Update</Text>
      </TouchableOpacity>
      <Pressable
        style={styles.closeButton}
        onPress={handleContinuingAnywayWithoutUpdatingTheApplication}>
        <Text style={{
          color: COMMON_COLORS.CHANNEL_TITLE,
          alignSelf: "center",
          textAlign: "center",
          fontFamily: typography.fontFamily.montserratBold
        }}>Continue Anyway</Text>
      </Pressable>
    </View>
  );
};

export default VersionNotUpdate;
