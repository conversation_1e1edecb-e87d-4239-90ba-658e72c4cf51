module.exports = {
  moduleDirectories: ["node_modules", "src"],
  preset: "react-native",
  moduleFileExtensions: ["ts", "tsx", "test.tsx", "js", "test.ts"],
  setupFilesAfterEnv: ["./jest.setup.js"],
  transform: {
    "^.+\\.[t|j]sx?$": "babel-jest",
    "^.+\\.[t|j]s?$": "babel-jest",
  },
  transformIgnorePatterns: [
    "/node_modules/(?!@react-navigation|react-native|@react-native|react-native-gesture-handler|@react-navigation-community/netinfo)",
  ],
  testMatch: [
    "**/*.test.tsx",
    "**/*.test.js",
    "**/__tests__/**/*.test.ts",
    "**/*.test.ts",
  ],
  moduleNameMapper: {
    "^react-native-reanimated$":
      "<rootDir>/__mocks__/react-native-reanimated.js",
  },
  collectCoverage: true,
  coverageReporters: ["json", "html"],
};
