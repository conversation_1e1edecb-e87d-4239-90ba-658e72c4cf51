import {RootState} from '@src/store/store';
import {FilterSectorEnumsProjectType} from '@src/store/filterEnumsProjects/sectorEnums/slice';
import {sectorEnumsSelector} from '@src/store/filterEnumsProjects/sectorEnums/selectors';

describe('sectorEnumsSelector', () => {
  it('should select the sector enums state from the root state', () => {
    // Arrange
    const programEnumsState: FilterSectorEnumsProjectType = {
      loading: false,
      error: false,
      success: false,
      sectorEnums: [],
    };

    // @ts-ignore
    const mockRootState: RootState = {
      sectorEnums: programEnumsState,
    };

    // Act
    const selectedState = sectorEnumsSelector(mockRootState);

    // Assert
    expect(selectedState).toEqual(programEnumsState);
  });
});
