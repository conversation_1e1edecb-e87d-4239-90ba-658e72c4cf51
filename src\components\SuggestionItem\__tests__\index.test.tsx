import {renderWithProviders} from '@src/utils/utils-for-tests';
import React from 'react';
import {SuggestionItem} from '@src/components/SuggestionItem';

describe('SuggestionItem', function () {
  const onSuggestionPress = jest.fn();
  it('Should render and match the snapshot', () => {
    const tree = renderWithProviders(
      <SuggestionItem
        newSuggestionObject={{
          id: '',
          name: '',
        }}
        onSuggestionPress={onSuggestionPress}
        userId={''}
        displayName={''}
      />,
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
