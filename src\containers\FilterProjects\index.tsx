import {Text, TouchableOpacity, View} from 'react-native';
import React, {useMemo} from 'react';
import style from './style';
import FilterProjectsOption from '../../components/FilterProjectsOption';
import {ScrollView} from 'react-native-gesture-handler';
import {ProjectUtils} from '@src/utils/projectUtils';
import {FilterParams} from '@src/store/projects/slice';
import {Search} from '@src/svg/Search';
import ClickableImage from '@src/components/ClickableImage';

type FilterProjectsProps = {
  onClick: any;
  filter: FilterParams;
  handleResetFilterOption: (arg0: string) => void;
};

const FilterProjects = (props: FilterProjectsProps) => {
  const {filter, onClick, handleResetFilterOption} = props;
  const isFilterObjectEmpty = useMemo(
    () => ProjectUtils.isFilterOptionsArrayValuesEmpty(filter!!),
    [filter],
  );
  return (
    <View style={style.searchContainer}>
      {isFilterObjectEmpty ? (
        <TouchableOpacity
          activeOpacity={0.2}
          onPress={onClick}
          style={{
            ...style.searchWrapper,
            flex: 1,
            alignItems: 'flex-start',
            justifyContent: 'center',
          }}>
          <Text style={style.placeholder}>Filter Search</Text>
        </TouchableOpacity>
      ) : (
        <ScrollView
          horizontal
          style={style.searchWrapper}
          contentContainerStyle={{
            alignItems: 'center',
            justifyContent: 'center',
          }}
          showsHorizontalScrollIndicator={false}>
          <Text style={style.placeholder}>Filtered By</Text>
          {!isFilterObjectEmpty && (
            <View style={style.filteredItemsContainer}>
              {filter &&
                Object.keys(filter).map(key => {
                  const isExcludedKey = [
                    'year',
                    'month',
                    'platformCode',
                    'page',
                    'size',
                  ].includes(key);

                  if (!isExcludedKey && filter[key] !== null) {
                    let finalValue;

                    if (typeof filter[key] === 'object') {
                      finalValue = filter[key]?.name;
                    } else {
                      finalValue = filter[key];
                    }
                    return (
                      <TouchableOpacity
                        key={key}
                        onPress={() => handleResetFilterOption(key)}>
                        <FilterProjectsOption value={finalValue} />
                      </TouchableOpacity>
                    );
                  }

                  return null;
                })}
            </View>
          )}
        </ScrollView>
      )}

      <ClickableImage
        imageStyle={style.searchBtn}
        onPress={onClick}
        url={null}
        SvgComponent={<Search />}
      />
    </View>
  );
};

export default React.memo(FilterProjects);
