import {G, Path, Rect, Svg} from 'react-native-svg';
import React from 'react';

export const Send = (props: {style: {}}) => {
  return (
    <Svg style={props.style} width="43" height="32" viewBox="0 0 43 32">
      <G id="Send" transform="translate(-331 -613)">
        <G id="BG" transform="translate(331 613)">
          <Rect
            id="Rectangle_548"
            data-name="Rectangle 548"
            width="43"
            height="32"
            rx="14"
            fill="#007cff"
          />
        </G>
        <G id="Send-2" data-name="Send" transform="translate(344.543 621)">
          <Path
            id="Tracé_29146"
            data-name="Tracé 29146"
            d="M8.793,4.094,16.4,7.9c3.414,1.707,3.414,4.5,0,6.205l-7.61,3.8c-5.121,2.56-7.21.462-4.65-4.65l.773-1.538a1.819,1.819,0,0,0,0-1.431L4.143,8.743C1.583,3.631,3.681,1.533,8.793,4.094Z"
            transform="translate(-3.05 -3.001)"
            fill="none"
            stroke="#fff"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
          <Path
            id="Tracé_29147"
            data-name="Tracé 29147"
            d="M5.44,12h4.8"
            transform="translate(-3.315 -4)"
            fill="none"
            stroke="#fff"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
        </G>
      </G>
    </Svg>
  );
};
