import { createSlice } from "@reduxjs/toolkit";
import { SLICES_NAMES } from "@src/constants/store";
import { NotificationModel } from "@src/models/notification";

export type NotificationsSliceType = {
  notificationExistence: boolean;
  notifications: NotificationModel[];
  markAsReadSuccess: boolean;
  markAsReadFailure: boolean;
  loading: boolean;
  error: boolean;
  success: boolean;
  errorNotificationsExistence: boolean;
  successNotificationsExistence: boolean;
};

export const initialState: NotificationsSliceType = {
  notificationExistence: false,
  notifications: [],
  loading: false,
  error: false,
  success: false,
  markAsReadFailure: false,
  markAsReadSuccess: false,
  errorNotificationsExistence: false,
  successNotificationsExistence: false
};
const slice = createSlice({
  name: SLICES_NAMES.NOTIFICATIONS_SLICE,
  initialState,
  reducers: {
    getNotificationsExistence: (state, action) => {
      return {
        ...state,
        loading: true,
        errorNotificationsExistence: false,
        successNotificationsExistence: false
      };
    },
    getNotificationsExistenceWithSuccess: (state, action) => ({
      ...state,
      notificationExistence: action.payload,
      loading: false,
      errorNotificationsExistence: false,
      successNotificationsExistence: true
    }),
    getNotificationsExistenceWithError: state => ({
      ...state,
      notificationExistence: false,
      loading: false,
      errorNotificationsExistence: true,
      success: false
    }),
    getNotificationsList: state => {
      return {
        ...state,
        loading: true,
        error: false,
        success: false
      };
    },
    getNotificationsListWithSuccess: (state, action) => {
      return {
        ...state,
        notifications: action.payload,
        loading: false,
        success: true,
        error: false
      };
    },
    getNotificationsListWithError: state => ({
      ...state,
      loading: false,
      error: true,
      success: false
    }),
    cleanUpNotifications: state => {
      state.notifications = [];
    },
    markAsRead: (state, action) => {
      return {
        ...state,
        loading: true,
        markAsReadFailure: false,
        markAsReadSuccess: false
      };
    },
    markAsReadWithSuccess: state => {
      return {
        ...state,
        loading: false,
        markAsReadFailure: false,
        markAsReadSuccess: true
      };
    },
    markAsReadWithError: state => {
      return {
        ...state,
        loading: false, // Set loading to false on error
        markAsReadFailure: true,
        markAsReadSuccess: false
      };
    },
    deleteNotification: (state, action) => {
      return {
        ...state,
        loading: true,
        error: false,
        success: false
      };
    },
    deleteNotificationWithSuccess: (
      state,
      action: { payload: { notification: NotificationModel } }
    ) => {
      const notificationsFiltered = state.notifications.filter(
        notification => notification.id !== action.payload.notification.id
      );
      return {
        ...state,
        notifications: notificationsFiltered,
        loading: false,
        success: true,
        error: false
      };
    },
    deleteNotificationWithError: state => {
      return {
        ...state,
        loading: false,
        error: true,
        success: false
      };
    },
    cleanUp: () => {
      // Reset to initial state, ensuring loading is false
      return {
        ...initialState,
        loading: false
      };
    }

    // we will dispatch this action when the user will click on a specific notification
  }
});

export const {
  getNotificationsExistenceWithSuccess,
  getNotificationsExistenceWithError,
  getNotificationsExistence,
  getNotificationsListWithError,
  getNotificationsListWithSuccess,
  getNotificationsList,
  markAsReadWithSuccess,
  markAsReadWithError,
  markAsRead,
  deleteNotification,
  deleteNotificationWithSuccess,
  deleteNotificationWithError,
  cleanUp,
  cleanUpNotifications
} = slice.actions;
export default slice.reducer;
