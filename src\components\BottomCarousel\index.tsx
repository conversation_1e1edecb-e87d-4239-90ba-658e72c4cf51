import React, { useEffect, useRef, useState } from "react";
import { FlatList, TouchableOpacity, View } from "react-native";
import styles, { returnBorderColor } from "./style";
import { ITEM_WIDTH, ItemBottomCarousel } from "@src/components/ItemBottomCarousel";
import { ChannelModel } from "@src/components/CarouselChannels";
import { OPACITY } from "@src/constants";
import NextChannelArrow from "@src/svg/NextChannelArrow";
import NextChannelArrowLessOpacity from "@src/svg/NextChannelArrowLessOpacity";
import PreviousChannelArrow from "@src/svg/PreviousChannelArrow";
import PreviousChannelArrowLessOpacity from "@src/svg/PreviousChannelArrowLessOpacity";

type BottomCarouselProps = {
  allowedChannels?: ChannelModel[];
  updateCarouselPosition: (a: number) => void;
  currentPosition: number;
};

const BottomCarousel = (props: BottomCarouselProps) => {
  const { updateCarouselPosition, allowedChannels, currentPosition } = props;
  const [isEndReached, setIsEndReached] = useState(false);
  const [isStartReached, setIsStartReached] = useState(true);

  const THRESHOLD_ENABLE_SCROLL_AUTOMATIC_BOTTOM_CAROUSEL = 3;

  const WIDTH_ONE_CHANNEL_BOTTOM_CAROUSEL = 33;

  const START_BOTTOM_CAROUSEL = 0;

  const flatListRef = useRef<FlatList>(null);
  useEffect(() => {
    if (flatListRef.current) {
      if (
        currentPosition >= THRESHOLD_ENABLE_SCROLL_AUTOMATIC_BOTTOM_CAROUSEL
      ) {
        flatListRef.current.scrollToOffset({
          offset: WIDTH_ONE_CHANNEL_BOTTOM_CAROUSEL * currentPosition,
          animated: false
        });
      } else {
        flatListRef.current.scrollToOffset({
          offset: START_BOTTOM_CAROUSEL,
          animated: false
        });
      }
    }
  }, [currentPosition]);

  const isClicked = (key: number, setSelectedItemIndex: number) => {
    return key === setSelectedItemIndex;
  };
  const data = allowedChannels?.map((item, index) => {
    const image =
      currentPosition === index
        ? item.smallIconFocused
        : item.smallIconUnFocused;
    return {
      ...item,
      component: <ItemBottomCarousel source={image!!} />
    };
  });

  const handleRightClick = () => {
    if (isEndReached) {
      setIsEndReached(false);
    }
    // Scroll to the end of the list
    flatListRef?.current?.scrollToOffset({
      offset: ITEM_WIDTH * (allowedChannels!!.length - 1),
      animated: false
    });
  };

  const handleLeftClick = () => {
    // Scroll to the start of the list
    flatListRef?.current?.scrollToOffset({
      offset: START_BOTTOM_CAROUSEL,
      animated: false
    });
  };

  const handleItemClick = ({ index }: { item: any; index: any }) => {
    updateCarouselPosition(index);
  };

  const renderItem = ({ item, index }: { index: number; item: any }) => {
    if (item !== null) {
      const isItemClicked = isClicked(index, currentPosition);
      const borderColor = returnBorderColor(isItemClicked);
      return (
        <TouchableOpacity
          style={{ ...styles.itemIconContainer, borderColor: borderColor }}
          activeOpacity={OPACITY["80"]}
          onPress={() => handleItemClick({ item, index })}>
          {item.component}
        </TouchableOpacity>
      );
    } else {
      return null;
    }
  };

  const handleScroll = (event: {
    nativeEvent: {
      contentOffset: { x: any };
      layoutMeasurement: { width: any };
      contentSize: { width: any };
    };
  }) => {
    // Represents the current horizontal scroll position within the FlatList
    const scrollOffset = event.nativeEvent.contentOffset.x;
    // Indicates the fixed width of the visible area of the FlatList
    const layoutWidth = event.nativeEvent.layoutMeasurement.width;
    // Denotes the total width of the content within the FlatList, including off-screen items
    const contentWidth = event.nativeEvent.contentSize.width;

    // Calculate the maximum scroll position to determine if close to the end
    const maxScroll = contentWidth - layoutWidth;
    const isNotCloseToEnd = maxScroll - scrollOffset > WIDTH_ONE_CHANNEL_BOTTOM_CAROUSEL;

    // Check if at the beginning of the list and update isStartReached state
    if (scrollOffset === 0) {
      setIsStartReached(true); // Set isStartReached to true when at the beginning
    } else {
      setIsStartReached(false); // Reset isStartReached if not at the beginning
      setIsEndReached(!isNotCloseToEnd); // Update isEndReached based on proximity to the end
    }
  };

  const handleOnEndReached = () => {
    console.log("hi");
    setIsEndReached(true); // Set the flag to true
  };

  const previousArrowUrl = isStartReached ? (
    <PreviousChannelArrowLessOpacity style={styles.imageArrow} />
  ) : (
    <PreviousChannelArrow style={styles.imageArrow} />
  );
  const nextArrowUrl = isEndReached ? (
    <NextChannelArrowLessOpacity style={styles.imageArrow} />
  ) : (
    <NextChannelArrow style={styles.imageArrow} />
  );

  return (
    <View style={styles.container}>
      <TouchableOpacity onPress={handleLeftClick} style={{ ...styles.box }}>
        {previousArrowUrl}
      </TouchableOpacity>

      <FlatList
        data={data}
        ref={flatListRef}
        horizontal={true}
        pagingEnabled
        snapToInterval={ITEM_WIDTH}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.contentContainer}
        style={styles.scrollViewContainer}
        renderItem={renderItem}
        onEndReached={handleOnEndReached}
        onScroll={handleScroll}
      />
      <TouchableOpacity
        activeOpacity={OPACITY["60"]}
        onPress={handleRightClick}
        style={styles.box}>
        {nextArrowUrl}
      </TouchableOpacity>
    </View>
  );
};

export default BottomCarousel;
