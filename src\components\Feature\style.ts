import {StyleSheet} from 'react-native';
import layout from '@src/theme/layout';
import {COMMON_COLORS} from '@src/theme/colors';
import {correspondentHeight, correspondentWidth} from '@src/utils/imageUtils';
import typography from '@src/theme/fonts';

const style = StyleSheet.create({
  detailFeatureContainer: {
    ...layout.rowSpaceBetweenCenter,
    backgroundColor: COMMON_COLORS.WHITE,
    //height: correspondentHeight(50),
    paddingTop: correspondentHeight(12),
    paddingBottom: correspondentHeight(12),
    paddingHorizontal: correspondentWidth(21),
    marginHorizontal: correspondentWidth(20),
    marginBottom: correspondentHeight(16),
    borderColor: COMMON_COLORS.GREY_10,
    borderRadius: 14,
    borderWidth: 1,
  },
  label: {
    maxWidth: correspondentWidth(156),
    fontSize: typography.fontSizes.sm,
    color: COMMON_COLORS.BLUE_20,
    fontWeight: '800',
  },
});

export default style;
