import {Defs, G, LinearGradient, Path, Rect, Stop, Svg} from 'react-native-svg';
import React from 'react';

export const MiniKpiIcon = (props: {style: {}}) => {
  return (
    <Svg style={props.style} width="24" height="24" viewBox="0 0 24 24">
      <Defs>
        <LinearGradient
          id="linear-gradient"
          x1="0.158"
          y1="0.148"
          x2="0.886"
          y2="0.783"
          gradientUnits="objectBoundingBox">
          <Stop offset="0" stopColor="#007cff" />
          <Stop offset="1" stopColor="#0af" />
        </LinearGradient>
      </Defs>
      <G
        id="Groupe_52584"
        data-name="Groupe 52584"
        transform="translate(-24 -135)"
        fill="url(#linear-gradient)">
        <Rect
          id="Rectangle_939"
          data-name="Rectangle 939"
          width="24"
          height="24"
          rx="6"
          transform="translate(24 135)"
        />
        <G
          id="conversation-alt-svgrepo-com"
          transform="translate(26.774 137.773)">
          <Path
            id="secondary"
            d="M8,13.61h6.226l2.767,2.767V6.692A.692.692,0,0,0,16.3,6H14.226"
            transform="translate(-1.541 -0.925)"
            fill="none"
            stroke="#fff"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1"
          />
          <Path
            id="primary"
            d="M12.686,3.692V9.226a.692.692,0,0,1-.692.692H5.767L3,12.686V3.692A.692.692,0,0,1,3.692,3h8.3A.692.692,0,0,1,12.686,3.692Z"
            fill="none"
            stroke="#fff"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1"
          />
        </G>
      </G>
    </Svg>
  );
};
