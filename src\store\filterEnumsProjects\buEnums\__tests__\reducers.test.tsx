import reducer, {
  cleanUp,
  getBuEnums,
  getBuEnumsWithError,
  getBuEnumsWithSuccess,
  initialState,
} from '@src/store/filterEnumsProjects/buEnums/slice';
import ProjectFilter from '@src/models/projectFilter';

const buEnumsExamples: ProjectFilter[] = [
  {
    type: 'type',
    code: 'code',
    label: 'label',
    id: 2,
  },
];
describe('Bu Enums reducer', () => {
  it('should return the initial state', () => {
    expect(reducer(undefined, {type: undefined})).toEqual(initialState);
  });

  it('should handle getBuEnums action', () => {
    const action = getBuEnums();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      loading: true,
      error: false,
      success: false,
    });
  });

  it('should handle getBuEnumsWithSuccess action ', () => {
    const action = getBuEnumsWithSuccess(buEnumsExamples);
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      buEnums: action.payload,
      loading: false,
      error: false,
      success: true,
    });
  });

  it('should handle getBuEnumsWithError action', () => {
    const action = getBuEnumsWithError();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      loading: false,
      error: true,
      success: false,
    });
  });

  it('should handle cleanUp action', () => {
    const action = cleanUp();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
    });
  });
});
