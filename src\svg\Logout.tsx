import {Path, Svg} from 'react-native-svg';
import React from 'react';
import {correspondentHeight, correspondentWidth} from '@src/utils/imageUtils';

export const Logout = (props: {style: {}}) => {
  return (
    <Svg
      id="trash"
      style={props.style}
      width={correspondentWidth(21)}
      height={correspondentHeight(20)}
      viewBox="0 0 21 20">
      <Path
        id="Logout"
        d="M2,6.5A4.5,4.5,0,0,1,6.5,2H12a4,4,0,0,1,4,4V7a1,1,0,0,1-2,0V6a2,2,0,0,0-2-2H6.5A2.5,2.5,0,0,0,4,6.5v11A2.5,2.5,0,0,0,6.5,20H12a2,2,0,0,0,2-2V17a1,1,0,0,1,2,0v1a4,4,0,0,1-4,4H6.5A4.5,4.5,0,0,1,2,17.5ZM18.293,8.293a1,1,0,0,1,1.414,0l3,3a1,1,0,0,1,0,1.414l-3,3a1,1,0,0,1-1.414-1.414L19.586,13H11a1,1,0,0,1,0-2h8.586L18.293,9.707A1,1,0,0,1,18.293,8.293Z"
        transform="translate(-2 -2)"
        fill="#fff"
        fillRule="evenodd"
      />
    </Svg>
  );
};
