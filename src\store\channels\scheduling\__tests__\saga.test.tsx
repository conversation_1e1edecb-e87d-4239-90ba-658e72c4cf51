import {call} from 'redux-saga/effects';
import {expectSaga} from 'redux-saga-test-plan';
import baseRequest from '@src/api/request';
import {handleCashFlowFeatures} from '@src/store/channels/cashFlow/saga';
import {ChannelsWebServices} from '@src/api/channels';
import {SchedulingChannelSummary} from '@src/models/channelsSummary/schedulingChannelSummary';
import {handleSchedulingFeatures} from '@src/store/channels/scheduling/saga';
import {
  getSchedulingFeaturesWithError,
  getSchedulingFeaturesWithSuccess,
} from '@src/store/channels/scheduling/slice';

describe('scheduling saga', () => {
  const projectId = 22;
  const reportingDateFilter = {};
  const features: SchedulingChannelSummary = {
    channelStatus: 'PRIMARY',
  };
  it('should yield an api call', () => {
    expectSaga(handleSchedulingFeatures)
      .provide([
        [
          call(
            baseRequest.get,
            ChannelsWebServices.WS_GET_SCHEDULING_SUMMARY(projectId),
          ),
          {params: reportingDateFilter},
        ],
      ])
      .run();
  });

  it('should handle scheduling features successfully', () => {
    expectSaga(handleSchedulingFeatures)
      .provide([
        [
          call(
            baseRequest.get,
            ChannelsWebServices.WS_GET_SCHEDULING_SUMMARY(projectId),
          ),
          {params: reportingDateFilter},
        ],
      ])
      .put(getSchedulingFeaturesWithSuccess({features}))
      .run();
  });

  it('should handle scheduling features with error', () => {
    expectSaga(handleCashFlowFeatures)
      .provide([
        [
          call(
            baseRequest.get,
            ChannelsWebServices.WS_GET_SCHEDULING_SUMMARY(projectId),
          ),
          {throw: true},
        ],
      ])
      .put(getSchedulingFeaturesWithError())
      .run();
  });
});
