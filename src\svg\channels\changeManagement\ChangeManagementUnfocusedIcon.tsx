import React from 'react';
import {G, Path, Svg} from 'react-native-svg';
import {svgChannelIconHeight, svgChannelIconWidth} from '@src/theme/style';

const ChangeManagementUnfocusedIcon = () => (
  <Svg
    width={svgChannelIconWidth(25.154)}
    height={svgChannelIconHeight(27.663)}
    viewBox="0 0 25.154 27.663">
    <G
      id="Groupe_52557"
      data-name="Groupe 52557"
      transform="translate(0.1 0.1)"
      opacity="0.3">
      <G id="Groupe_446" data-name="Groupe 446">
        <G
          id="Groupe_254"
          data-name="Groupe 254"
          transform="translate(10.899 13.409)">
          <G
            id="Groupe_254-2"
            data-name="Groupe 254"
            transform="translate(0 0)">
            <Path
              id="Tracé_219"
              data-name="<PERSON><PERSON><PERSON> 219"
              d="M707.949,299.054a1.1,1.1,0,0,1-.776-.322l-2.689-2.683a1.648,1.648,0,0,1,0-2.333l2.689-2.683a1.1,1.1,0,0,1,1.878.778v2.523a7.915,7.915,0,0,0,7.906-7.906v-.878a.549.549,0,0,1,1.1,0v.878a9,9,0,0,1-9,9,1.1,1.1,0,0,1-1.1-1.1V291.81h0l-2.689,2.683a.55.55,0,0,0,0,.779l2.689,2.683h0v-.6a.549.549,0,0,1,1.1,0v.6a1.1,1.1,0,0,1-1.1,1.1Zm0,0"
              transform="translate(-704 -285)"
              fill="#007cff"
              stroke="#fff"
              stroke-width="0.2"
            />
          </G>
        </G>
        <G id="Groupe_255" data-name="Groupe 255">
          <G id="Groupe_254-3" data-name="Groupe 254">
            <Path
              id="Tracé_219-2"
              data-name="Tracé 219"
              d="M714.105,285a1.1,1.1,0,0,1,.776.322l2.689,2.683a1.648,1.648,0,0,1,0,2.333l-2.689,2.683a1.1,1.1,0,0,1-1.878-.778v-2.523a7.915,7.915,0,0,0-7.906,7.906v.878a.549.549,0,1,1-1.1,0v-.878a9,9,0,0,1,9-9,1.1,1.1,0,0,1,1.1,1.1v2.523h0l2.689-2.683a.55.55,0,0,0,0-.779l-2.689-2.683h0v.6a.549.549,0,0,1-1.1,0v-.6a1.1,1.1,0,0,1,1.1-1.1Zm0,0"
              transform="translate(-704 -285)"
              fill="#007cff"
              stroke="#fff"
              stroke-width="0.2"
            />
          </G>
        </G>
      </G>
      <Path
        id="Tracé_9242"
        data-name="Tracé 9242"
        d="M-2805.435,1239.582l8.083-8.082,2.088,2.088,6.958-6.958"
        transform="translate(2809.121 -1218.486)"
        fill="none"
        stroke="#007cff"
        stroke-linecap="round"
        stroke-width="1.5"
      />
    </G>
  </Svg>
);

export default ChangeManagementUnfocusedIcon;
