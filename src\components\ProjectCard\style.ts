import {StyleSheet} from 'react-native';
import {PERCENTAGES, windowHeight, windowWidth} from '@src/constants';
import layout from '@src/theme/layout';
import {correspondentWidth} from '@src/utils/imageUtils';
import {COMMON_COLORS} from '@src/theme/colors';

const TWO_PERCENTAGE_WINDOW_HEIGHT = windowHeight * 0.02;
const ONE_PERCENTAGE_WINDOW_HEIGHT = windowHeight * 0.015;
const PADDING_HORIZONTAL_CHILD_CONTAINER = windowWidth * 0.05;

const styles = StyleSheet.create({
  parentContainer: {
    paddingBottom: ONE_PERCENTAGE_WINDOW_HEIGHT,
    marginBottom: TWO_PERCENTAGE_WINDOW_HEIGHT,
    paddingTop: windowHeight * 0.15,
    borderRadius: 22,
    alignItems: 'center',
  },
  childContainer: {
    width: PERCENTAGES['95'],
    backgroundColor: 'white',
    borderRadius: 20,
    paddingHorizontal: PADDING_HORIZONTAL_CHILD_CONTAINER,
    paddingBottom: windowHeight * 0.015,
    paddingTop: windowHeight * 0.025,
  },
  loaderContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    // Add your additional styling for the loader container
  },
  projectStatus: {
    borderRadius: 10,
    paddingHorizontal: windowWidth * 0.039,
    width: windowWidth * 0.155,
    height: windowHeight * 0.025,
    alignSelf: 'center',
    justifyContent: 'center',
  },
  providerImageAndProjectStatusContainer: {
    ...layout.rowHCenterSpaceBetween,
    marginBottom: ONE_PERCENTAGE_WINDOW_HEIGHT * 1.5,
  },
  providerImageContainer: {
    maxWidth: correspondentWidth(120),
    flexGrow: 1,
    padding: 0,
  },
  providerImage: {
    resizeMode: 'contain',
    height: windowHeight * 0.05,
    maxWidth: correspondentWidth(120),
    width: '100%',
    borderWidth: 3,
  },

  titleAndReferenceProjectContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  textsContainer: {},
  button: {
    borderRadius: 4,
  },
  reference: {
    fontSize: 12,
    color: COMMON_COLORS.BLUE_10,
  },
  title: {
    color: COMMON_COLORS.BLUE_30,
    maxWidth: PERCENTAGES['100'],
    fontSize: 15,
    fontWeight: '900',
  },
  colorName: {
    color: COMMON_COLORS.WHITE,
    fontSize: 10,
    textAlign: 'center',
  },
});
export default styles;
