import { FirebaseMessagingTypes } from "@react-native-firebase/messaging";
import { User } from "@src/models/user";
import { Suggestion } from "react-native-controlled-mentions";
import { extractUserRegex, isEmailRegex } from "@src/constants/reegexes";
import { NotificationModel } from "@src/models/notification";

export const ObjectUtils = {
  convertObjectToKeyValuesArray(object: Object) {
    return Object.entries(object).map(([key, value]) => {
      return { key, value };
    });
  },

  convertRemoteNotificationObjectToNotificationModel: (
    remoteMessage: FirebaseMessagingTypes.RemoteMessage
  ): NotificationModel => {
    return JSON.parse(<string>remoteMessage.data?.notificationDto!!);
  },

  mapToSuggestion(oldObject: User): Suggestion {
    let name = oldObject.userProject.name;
    const match = name.match(isEmailRegex);
    if (match && match[0]) {
      const userFromEmail = match[0].match(extractUserRegex);
      if (userFromEmail) {
        name = userFromEmail[0];
      }
    }
    return {
      id: oldObject.azureDirectoryId,
      name
    };
  }
};
