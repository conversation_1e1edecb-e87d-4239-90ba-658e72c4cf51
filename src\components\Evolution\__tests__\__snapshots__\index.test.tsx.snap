// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Evolution Should render and match the snapshot 1`] = `
<View
  style={
    {
      "alignItems": "center",
      "flexDirection": "row",
      "justifyContent": "space-between",
    }
  }
>
  <View
    style={
      [
        {
          "alignItems": "center",
          "flexDirection": "row",
          "justifyContent": "center",
        },
        {
          "marginHorizontal": 12,
        },
      ]
    }
  >
    <Image
      source={
        {
          "testUri": "../../../assets/images/previous_arrow_risk_channel.png",
        }
      }
      style={
        {
          "marginRight": 8,
        }
      }
    />
    <Text
      style={
        {
          "color": "#00338D",
          "fontSize": 14,
          "fontWeight": "bold",
        }
      }
    >
      1
    </Text>
  </View>
  <View
    style={
      [
        {
          "alignItems": "center",
          "flexDirection": "row",
          "justifyContent": "center",
        },
      ]
    }
  >
    <Image
      source={
        {
          "testUri": "../../../assets/images/next_arrow_risk_channel.png",
        }
      }
      style={
        {
          "marginRight": 8,
        }
      }
    />
    <Text
      style={
        {
          "color": "#00338D",
          "fontSize": 14,
          "fontWeight": "bold",
        }
      }
    >
      2
    </Text>
  </View>
</View>
`;
