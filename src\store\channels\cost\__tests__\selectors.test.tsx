import {RootState} from '@src/store/store';
import {CostSliceType} from '@src/store/channels/cost/slice';
import {costSelector} from '@src/store/channels/cost/selectors';

describe('CostSelector', () => {
  it('should select the cost state from the root state', () => {
    // Arrange
    const costState: CostSliceType = {
      features: null,
      channelStatus: 'PRIMARY',
      loading: false,
      error: false,
      success: false,
    };

    // @ts-ignore
    const mockRootState: RootState = {
      costChannel: costState,
    };

    // Act
    const selectedState = costSelector(mockRootState);

    // Assert
    expect(selectedState).toEqual(costState);
  });
});
