import {renderWithProviders} from '@src/utils/utils-for-tests';
import React from 'react';
import {CustomLinearGradient} from '@src/components/CustomLinearGradient';

describe('CustomLinearGradient', () => {
  it('should render and match the snapshot', () => {
    const tree = renderWithProviders(
      <CustomLinearGradient
        firstColor={''}
        secondColor={''}
        x_start={0}
        y_start={0}
        x_end={0}
        y_end={0}
      />,
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
