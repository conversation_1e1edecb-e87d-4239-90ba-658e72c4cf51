import {call} from 'redux-saga/effects';
import {expectSaga} from 'redux-saga-test-plan';
import baseRequest from '@src/api/request';
import {ChannelsWebServices} from '@src/api/channels';
import {HseChannelSummary} from '@src/models/channelsSummary/hseChannelSummary';
import {handleHseFeatures} from '@src/store/channels/hse/saga';
import {getHseFeaturesWithSuccess} from '@src/store/channels/hse/slice';
import {getHumanCapFeaturesWithError} from '@src/store/channels/humanCapital/slice';

describe('hse saga', () => {
  const projectId = 22;
  const reportingDateFilter = {};
  const features: HseChannelSummary = {
    channelStatus: 'PRIMARY',
  };
  it('should yield an api call', () => {
    expectSaga(handleHseFeatures)
      .provide([
        [
          call(
            baseRequest.get,
            ChannelsWebServices.WS_GET_HSE_SUMMARY(projectId),
          ),
          {params: reportingDateFilter},
        ],
      ])
      .run();
  });

  it('should handle hse features successfully', () => {
    expectSaga(handleHseFeatures)
      .provide([
        [
          call(
            baseRequest.get,
            ChannelsWebServices.WS_GET_HSE_SUMMARY(projectId),
          ),
          {params: reportingDateFilter},
        ],
      ])
      .put(getHseFeaturesWithSuccess({features}))
      .run();
  });

  it('should handle hse features with error', () => {
    expectSaga(handleHseFeatures)
      .provide([
        [
          call(
            baseRequest.get,
            ChannelsWebServices.WS_GET_HSE_SUMMARY(projectId),
          ),
          {throw: true},
        ],
      ])
      .put(getHumanCapFeaturesWithError())
      .run();
  });
});
