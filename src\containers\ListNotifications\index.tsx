import React, { memo } from "react";
import { View } from "react-native";
import { DateUtils } from "@src/utils/dateUtils";
import { NotificationModel } from "@src/models/notification";
import { NotificationsUtils } from "@src/utils/notificationsUtils";
import Notification from "@src/components/Notification";
import { useSelector } from "react-redux";
import { globalSelector } from "@src/store/global/selectors";
import { correspondentHeight } from "@src/utils/imageUtils";
import Swipeable from "@src/components/Notification/Swipeable";
import { MobileAlertType } from "@src/constants/notification";
import { ROUTE_NAMES } from "@src/constants/navigation";
import { navigate } from "@src/utils/navigationUtils";
import { EmptyList, List } from "@src/components/List";

type ListNotificationsProps = {
  handleRefreshNotificationsPage: () => void;
  notifications: NotificationModel[];
  handleMarkAsRead: (notificationId: number) => void;
  handleOpenModal: () => void;
  handleDeleteNotification: (notificationId: number) => void;
  handleSetNotifications: (notification: NotificationModel) => void;
  toggleDrawer: () => void;
  isLoading: boolean;
  emptyListMessage: string
};

const ListNotifications = (props: ListNotificationsProps) => {
  const {
    notifications,
    handleMarkAsRead,
    handleOpenModal,
    handleSetNotifications,
    handleRefreshNotificationsPage,
    toggleDrawer,
    handleDeleteNotification,
    isLoading,
    emptyListMessage
  } = props;


  const onClick = (notification: NotificationModel) => {
    const notificationId = notification.id;
    handleMarkAsRead(notificationId!!);
    handleSetNotifications(notification);
    const mobileAlertType = notification.mobileAlertType!!;
    if (mobileAlertType === MobileAlertType.REDIRECTION) {
      const objectType = notification.objectType;
      const objectId = notification.objectId;
      navigate(ROUTE_NAMES.DISCUSSION_DETAILS, { objectType, objectId });
    } else {
      handleOpenModal();
    }
  };

  const renderItem = ({ item }: { item: NotificationModel }) => {
    const date = DateUtils.extractNotificationTimeFromCreationDate(
      item.creationDate
    );
    const isRead = NotificationsUtils.mapNotificationStatusLabelToBoolean(
      item.status!!
    );

    return (
      <Swipeable
        onDelete={() => {
          handleDeleteNotification(item.id);
        }}>
        <Notification
          onClick={() => onClick(item)}
          isRead={isRead}
          time={date}
          type={item.objectType!!}
          notification={item}
        />
      </Swipeable>
    );
  };

  return (
    <View style={{
      paddingBottom: correspondentHeight(350),
      marginTop: correspondentHeight(20),
      minHeight: "10%"
    }}>
      <List
        showsVerticalScrollIndicator={false}
        data={notifications}
        renderItem={({ item }) => {
          return renderItem({ item });
        }}
        ListEmptyComponent={<EmptyList isLoading={isLoading} emptyListMessage={emptyListMessage} />}
        keyExtractor={item => item.id.toString()}
        onRefresh={handleRefreshNotificationsPage}
        refreshing={false}
      />
    </View>
  );
};

export default memo(ListNotifications);
