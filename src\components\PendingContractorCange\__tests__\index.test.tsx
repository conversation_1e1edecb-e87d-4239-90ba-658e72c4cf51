import {renderWithProviders} from '@src/utils/utils-for-tests';
import React from 'react';
import {PendingContractorChange} from '@src/components/PendingContractorCange';

describe('PendingContractorChange', function () {
  it('Should render and match the snapshot', () => {
    const tree = renderWithProviders(
      <PendingContractorChange value={'2'} />,
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
