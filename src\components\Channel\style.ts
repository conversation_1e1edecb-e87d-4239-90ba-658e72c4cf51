import {StyleSheet} from 'react-native';
import typography from '@src/theme/fonts';
import {correspondentHeight, correspondentWidth} from '@src/utils/imageUtils';
import {COMMON_COLORS} from '@src/theme/colors';

const style = StyleSheet.create({
  container: {
    backgroundColor: COMMON_COLORS.WHITE,
    paddingTop: correspondentHeight(30),
    borderRadius: 20,
  },
  channelTitle: {
    color: COMMON_COLORS.BLACK,
    fontFamily: typography.fontFamily.montserratSemiBold,
    fontSize: typography.fontSizes.md,
    marginBottom: correspondentHeight(22),
    fontWeight: '600',
    textAlign: 'center',
  },
  svgChannel: {
    marginBottom: correspondentHeight(30),
    alignSelf: 'center',
  },
  svgChannelDimensions: {
    width: correspondentWidth(300),
    height: correspondentHeight(230),
  },
  gradientBorder: {
    borderRadius: 20,
    paddingBottom: correspondentWidth(5),
  },
});

export default style;
