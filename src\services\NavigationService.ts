import { CommonActions } from '@react-navigation/native';
import { navigationRef } from '@src/utils/navigationUtils';
import { NotificationModel } from '@src/models/notification';
import { ROUTE_NAMES } from '@src/constants/navigation';
import { MobileAlertType } from '@src/constants/notification';

/**
 * NavigationService provides centralized navigation functionality
 * for handling complex navigation scenarios like deep linking and push notifications.
 */
class NavigationService {
  /**
   * Navigate to the appropriate screen based on notification data
   * This maintains a complete navigation stack for proper back navigation
   * 
   * @param notification The notification model containing navigation data
   */
  navigateToNotification(notification: NotificationModel) {
    if (!navigationRef.isReady()) return;
    
    // Build a logical navigation stack based on notification data
    const routes = [];
    
    // Always include HOME as the base route
    routes.push({ 
      name: ROUTE_NAMES.HOME 
    });
    
    // Add PROJECT_HEALTH_DETAILS if we have a projectId
    if (notification.projectId) {
      routes.push({ 
        name: ROUTE_NAMES.PROJECT_HEALTH_DETAILS,
        params: { projectId: notification.projectId }
      });
    }
    
    // Add NOTIFICATIONS for context
    routes.push({ 
      name: ROUTE_NAMES.NOTIFICATIONS 
    });
    
    // Add DISCUSSION_DETAILS for redirection notifications
    if (notification.mobileAlertType === MobileAlertType.REDIRECTION) {
      routes.push({ 
        name: ROUTE_NAMES.DISCUSSION_DETAILS,
        params: { 
          objectType: notification.objectType,
          objectId: notification.objectId,
          fromNotification: true // Flag to indicate this came from a notification
        }
      });
    }
    
    // Reset the navigation stack with our new routes
    navigationRef.current?.dispatch(
      CommonActions.reset({
        index: routes.length - 1,
        routes: routes
      })
    );
  }

  /**
   * Check if we're already on a specific screen
   * 
   * @param routeName The route name to check
   * @returns boolean indicating if we're on the specified screen
   */
  isCurrentScreen(routeName: string): boolean {
    if (!navigationRef.isReady()) return false;
    
    const state = navigationRef.current?.getRootState();
    const routes = state?.routes || [];
    const currentRouteName = routes[routes.length - 1]?.name;
    
    return currentRouteName === routeName;
  }

  /**
   * Navigate to a screen with parameters
   * 
   * @param name The route name to navigate to
   * @param params Optional parameters to pass to the route
   */
  navigate(name: string, params?: Record<string, unknown>) {
    if (navigationRef.isReady()) {
      navigationRef.current?.navigate(name as never, params as never);
    }
  }

  /**
   * Go back to the previous screen
   */
  goBack() {
    if (navigationRef.isReady() && navigationRef.current?.canGoBack()) {
      navigationRef.current?.goBack();
    }
  }
}

export default new NavigationService();
