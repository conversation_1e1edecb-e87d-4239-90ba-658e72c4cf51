import {RootState} from '@src/store/store';
import {FilterProgramEnumsProjectType} from '@src/store/filterEnumsProjects/programEnums/slice';
import {programEnumsSelector} from '@src/store/filterEnumsProjects/programEnums/selectors';

describe('programEnumsSelector', () => {
  it('should select the program enums state from the root state', () => {
    // Arrange
    const programEnumsState: FilterProgramEnumsProjectType = {
      loading: false,
      error: false,
      success: false,
      programEnums: [],
    };

    // @ts-ignore
    const mockRootState: RootState = {
      programEnums: programEnumsState,
    };

    // Act
    const selectedState = programEnumsSelector(mockRootState);

    // Assert
    expect(selectedState).toEqual(programEnumsState);
  });
});
