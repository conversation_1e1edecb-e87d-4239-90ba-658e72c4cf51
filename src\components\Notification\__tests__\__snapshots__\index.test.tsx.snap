// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Notification Should render and match the snapshot 1`] = `
<View
  accessibilityValue={
    {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  focusable={true}
  onClick={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    [
      {
        "paddingLeft": 52.50000000000001,
        "paddingRight": 52.50000000000001,
        "paddingTop": 24.011999999999997,
        "width": 750,
      },
      {
        "backgroundColor": "#FAFCFF",
      },
    ]
  }
>
  <View>
    <View
      style={
        {
          "alignItems": "center",
          "flexDirection": "row",
          "justifyContent": "space-between",
        }
      }
    >
      <View
        style={
          {
            "alignItems": "center",
            "flexDirection": "row",
            "maxWidth": "70%",
            "minWidth": "70%",
          }
        }
      >
        <View
          style={
            {
              "aspectRatio": 1,
              "width": 45,
            }
          }
        >
<<<<<<< HEAD
          <Image
            source={
              {
                "testUri": "../../../assets/images/mini_action_icon.png",
              }
            }
=======
          <RNSVGSvgView
            align="xMidYMid"
            bbHeight="24"
            bbWidth="24"
            focusable={false}
            height="24"
            meetOrSlice={0}
            minX={0}
            minY={0}
>>>>>>> 92153e74ea60e13508840ae81f48a0cae0067140
            style={
              [
                {
                  "backgroundColor": "transparent",
                  "borderWidth": 0,
                },
                {
                  "height": "100%",
                  "width": "100%",
                },
                {
                  "flex": 0,
                  "height": 24,
                  "width": 24,
                },
              ]
            }
            vbHeight={24}
            vbWidth={24}
            width="24"
          >
            <RNSVGGroup
              fill={
                {
                  "payload": 4278190080,
                  "type": 0,
                }
              }
            >
              <RNSVGDefs>
                <RNSVGLinearGradient
                  gradient={
                    [
                      0,
                      -16745217,
                      1,
                      -16733441,
                    ]
                  }
                  gradientTransform={null}
                  gradientUnits={0}
                  name="gradient"
                  x1="0.158"
                  x2="0.886"
                  y1="0.148"
                  y2="0.783"
                />
              </RNSVGDefs>
              <RNSVGGroup
                fill={
                  {
                    "payload": 4278190080,
                    "type": 0,
                  }
                }
                matrix={
                  [
                    1,
                    0,
                    0,
                    1,
                    -24,
                    -135,
                  ]
                }
                name="Groupe_61247"
              >
                <RNSVGRect
                  fill={
                    {
                      "brushRef": "gradient",
                      "type": 1,
                    }
                  }
                  height="24"
                  matrix={
                    [
                      1,
                      0,
                      0,
                      1,
                      24,
                      135,
                    ]
                  }
                  name="Rectangle_939"
                  propList={
                    [
                      "fill",
                    ]
                  }
                  rx="6"
                  width="24"
                  x="0"
                  y="0"
                />
                <RNSVGGroup
                  fill={
                    {
                      "payload": 4278190080,
                      "type": 0,
                    }
                  }
                  matrix={
                    [
                      1,
                      0,
                      0,
                      1,
                      27.317,
                      138.317,
                    ]
                  }
                  name="graph-svgrepo-com"
                >
                  <RNSVGPath
                    d="M13.907,9.039A.668.668,0,0,0,12.795,8.3L10.94,11.08,8.708,9.573a.869.869,0,0,0-1.245.3L6.084,12.353A.668.668,0,1,0,7.252,13l1.131-2.036,2.2,1.484a.869.869,0,0,0,1.209-.238Z"
                    fill={
                      {
                        "payload": 4294967295,
                        "type": 0,
                      }
                    }
                    matrix={
                      [
                        1,
                        0,
                        0,
                        1,
                        -1.327,
                        -1.99,
                      ]
                    }
                    name="Tracé_52403"
                    propList={
                      [
                        "fill",
                      ]
                    }
                  />
                  <RNSVGPath
                    d="M5.341,2A3.341,3.341,0,0,0,2,5.341v6.683a3.341,3.341,0,0,0,3.341,3.341h6.683a3.341,3.341,0,0,0,3.341-3.341V5.341A3.341,3.341,0,0,0,12.024,2Zm-2,3.341a2,2,0,0,1,2-2h6.683a2,2,0,0,1,2,2v6.683a2,2,0,0,1-2,2H5.341a2,2,0,0,1-2-2Z"
                    fill={
                      {
                        "payload": 4294967295,
                        "type": 0,
                      }
                    }
                    name="Tracé_52404"
                    propList={
                      [
                        "fill",
                      ]
                    }
                  />
                </RNSVGGroup>
              </RNSVGGroup>
            </RNSVGGroup>
          </RNSVGSvgView>
        </View>
        <View
          style={
            {
              "marginStart": 15,
            }
          }
        >
          <Text
            style={
              {
                "color": "#3C485F",
                "fontFamily": "Montserrat-Bold",
                "fontSize": 13,
              }
            }
          />
        </View>
      </View>
      <View
        style={
          {
            "maxWidth": 150,
            "textAlign": "right",
          }
        }
      >
        <Text
          style={
            {
              "color": "#3C485F",
              "fontFamily": "Montserrat-Regular",
              "fontSize": 12,
              "opacity": 0.7,
            }
          }
        >
          time
        </Text>
      </View>
    </View>
    <View
      style={
        {
          "marginTop": 12,
        }
      }
    >
      <Text
        style={
          {
            "color": "#3C485F",
            "fontFamily": "Montserrat-Regular",
            "fontSize": 13,
          }
        }
      />
    </View>
    <View
      style={
        {
          "backgroundColor": "#DDEBFF",
          "height": 1,
          "marginTop": 23,
        }
      }
    />
  </View>
</View>
`;
