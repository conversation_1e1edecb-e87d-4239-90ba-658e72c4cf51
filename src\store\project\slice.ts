import { createSlice } from "@reduxjs/toolkit";
import { SLICES_NAMES } from "@src/constants/store";
import { Project } from "@src/models/project";
import { ProjectChannelKpis } from "@src/models/projectChannelKpis";
import Profile from "@src/models/profile";
import { Role } from "@src/models/role";
import { FeaturePrivilege } from "@src/models/featurePrivilege";

export type ProjectSliceType = {
  profile: Profile | null;
  clientImage: string | null;
  role: Role | null;
  flattenKpis: ProjectChannelKpis | null;
  featurePrivileges: FeaturePrivilege[] | null;
  currentProject: Project | null;
  reportingDateFilter: any;
  success: boolean;
  error: boolean;
  successFlattenKpis: boolean;
  errorFlattenKpis: boolean;
  successUserProject: boolean;
  errorUserProject: boolean;
  loading: boolean;
  projectId: number | null;
};

export const initialState: ProjectSliceType = {
  errorFlattenKpis: false,
  successFlattenKpis: false,
  errorUserProject: false,
  successUserProject: false,
  success: false,
  loading: false,
  error: false,
  currentProject: null,
  profile: null,
  flattenKpis: null,
  role: null,
  reportingDateFilter: null,
  projectId: null,
  clientImage: null,
  featurePrivileges: null
};

const slice = createSlice({
  name: SLICES_NAMES.PROJECT_SLICE,
  initialState,
  reducers: {
    getUserProjectWithProjectIdAndUserId: (state, action) => {
      return {
        ...state,
        loading: true,
        successUserProject: false,
        errorUserProject: false
      };
    },
    getUserProjectWithProjectIdAndUserIdWithSuccess: (
      state,
      action: {
        payload: {
          profile: Profile | null;
          role: Role | null;
          featurePrivileges: FeaturePrivilege[];
        };
      }
    ) => {
      return {
        ...state,
        profile: action.payload.profile,
        role: action.payload.role,
        featurePrivileges: action.payload.featurePrivileges,
        loading: false,
        successUserProject: true,
        errorUserProject: false
      };
    },
    getUserProjectWithProjectIdAndUserIdWithError: state => {
      return {
        ...state,
        loading: false,
        successUserProject: false,
        errorUserProject: true
      };
    },
    getFlattenKpis: (state, action) => {
      return {
        ...state,
        loading: true,
        successFlattenKpis: false,
        errorFlattenKpis: false
      };
    },
    getFlattenKpisWithSuccess: (
      state,
      action: { payload: ProjectChannelKpis }
    ) => {
      return {
        ...state,
        flattenKpis: action.payload,
        loading: false,
        successFlattenKpis: true,
        errorFlattenKpis: false
      };
    },
    getFlattenKpisWithError: state => {
      return {
        ...state,
        loading: false,
        errorFlattenKpis: true,
        successFlattenKpis: false
      };
    },
    setCurrentProjectId: (state, action) => {
      return {
        ...state,
        projectId: action.payload
      };
    },
    setCurrentProject: (state, action) => {
      return {
        ...state,
        loading: true,
        success: false,
        error: false
      };
    },
    setCurrentProjectWithSuccess: (
      state,
      action: {
        payload: {
          project: any;
          reportingDate: { month: number; year: any };
        };
      }
    ) => {
      return {
        ...state,
        loading: false,
        error: false,
        success: true,
        currentProject: action.payload.project,
        reportingDateFilter: action.payload.reportingDate
      };
    },
    setCurrentProjectWithError: state => {
      return {
        ...state,
        loading: false,
        error: true,
        success: false
      };
    },
    setReportingDate: (
      state,
      action: { payload: { month: number; year: any } }
    ) => {
      return {
        ...state,
        reportingDateFilter: action.payload
      };
    },
    cleanUp: () => {
      return initialState;
    }
  }
});

export const {
  setCurrentProject,
  setCurrentProjectWithSuccess,
  setCurrentProjectWithError,
  cleanUp,
  getFlattenKpisWithSuccess,
  getFlattenKpisWithError,
  getFlattenKpis,
  setReportingDate,
  setCurrentProjectId,
  getUserProjectWithProjectIdAndUserIdWithSuccess,
  getUserProjectWithProjectIdAndUserIdWithError,
  getUserProjectWithProjectIdAndUserId
} = slice.actions;
export default slice.reducer;
