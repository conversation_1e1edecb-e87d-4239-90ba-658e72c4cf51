import React from 'react';
import {G, Path, Svg} from 'react-native-svg';
import {svgChannelIconHeight, svgChannelIconWidth} from '@src/theme/style';

const RiskUnfocusedIcon = () => (
  <Svg
    width={svgChannelIconWidth(35.974)}
    height={svgChannelIconHeight(29.491)}
    viewBox="0 0 35.974 29.491">
    <G
      id="Groupe_61262"
      data-name="Groupe 61262"
      transform="translate(1.266 0.1)"
      opacity="0.3">
      <G id="Groupe_406" data-name="Groupe 406" transform="translate(-1)">
        <Path
          id="Tracé_9222"
          data-name="Tracé 9222"
          d="M1419.289,333.141a.832.832,0,0,1-.793-.867V312.918a.833.833,0,0,1,.793-.867h2.24l-5.454-5.967-5.453,5.967h2.239a.832.832,0,0,1,.793.867v19.356a.8.8,0,1,1-1.585,0V313.785h-3.36a.8.8,0,0,1-.732-.535.929.929,0,0,1,.172-.945l7.367-8.061a.766.766,0,0,1,1.121,0l7.367,8.061a.926.926,0,0,1,.172.945.793.793,0,0,1-.732.535h-3.359v18.488a.833.833,0,0,1-.793.867Zm0,0"
          transform="translate(-1398.385 -304)"
          fill="#007cff"
          stroke="#fff"
          stroke-width="0.2"
        />
        <Path
          id="Tracé_9223"
          data-name="Tracé 9223"
          d="M1477.51,371.407a.958.958,0,0,1-.958-.959V357.72a.959.959,0,0,1,.958-.959h.77l-3.155-3.155-3.155,3.155h.769a.959.959,0,0,1,.958.959v12.728a.959.959,0,1,1-1.917,0v-11.77h-2.125a.958.958,0,0,1-.678-1.636l5.47-5.47a.958.958,0,0,1,1.356,0l5.47,5.47a.959.959,0,0,1-.678,1.636h-2.125v11.77a.959.959,0,0,1-.959.957Zm0,0"
          transform="translate(-1446.112 -342.266)"
          fill="#007cff"
          stroke="#fff"
          stroke-width="0.5"
        />
        <Path
          id="Tracé_9224"
          data-name="Tracé 9224"
          d="M1383.091,371.407a.958.958,0,0,1-.958-.959V357.72a.959.959,0,0,1,.958-.959h.77l-3.155-3.155-3.155,3.155h.769a.959.959,0,0,1,.959.959v12.728a.958.958,0,1,1-1.917,0v-11.77h-2.125a.959.959,0,0,1-.678-1.637l5.47-5.469a.957.957,0,0,1,1.355,0l5.47,5.469a.959.959,0,0,1-.678,1.637h-2.125v11.77a.958.958,0,0,1-.959.959Zm0,0"
          transform="translate(-1374.277 -342.266)"
          fill="#007cff"
          stroke="#fff"
          stroke-width="0.5"
        />
      </G>
    </G>
  </Svg>
);

export default RiskUnfocusedIcon;
