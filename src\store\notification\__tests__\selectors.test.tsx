import {RootState} from '@src/store/store';
import {NotificationsSliceType} from '@src/store/notification/slice';
import {notificationSelector} from '@src/store/notification/selectors';

describe('notificationSelector', () => {
  it('should select the notification state from the root state', () => {
    // Arrange
    const notificationState: NotificationsSliceType = {
      error: false,
      isOpenedFromNotification: false,
      loading: false,
      notificationId: '3',
      notificationOpenedDestination: 'true',
      success: false,
      notification: {},
    };

    // @ts-ignore
    const mockRootState: RootState = {
      notification: notificationState,
    };

    // Act
    const selectedState = notificationSelector(mockRootState);

    // Assert
    expect(selectedState).toEqual(notificationState);
  });
});
