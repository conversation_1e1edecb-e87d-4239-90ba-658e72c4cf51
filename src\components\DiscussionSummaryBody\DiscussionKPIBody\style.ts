import {StyleSheet} from 'react-native';
import {correspondentHeight, correspondentWidth} from '@src/utils/imageUtils';
import {COMMON_COLORS} from '@src/theme/colors';
import typography from '@src/theme/fonts';

export const styles = StyleSheet.create({
  container: {
    marginBottom: correspondentHeight(22),
    backgroundColor: COMMON_COLORS.WHITE,
    shadowColor: COMMON_COLORS.BLACK,
    paddingStart: correspondentWidth(25),
    paddingBottom: correspondentHeight(26),
    borderRadius: 20,
    shadowRadius: 16,
    elevation: 3,
  },
  channelIconAndTitle: {
    marginEnd: correspondentHeight(25),
    flexDirection: 'row',
    alignItems: 'center',
  },
  channelIcon: {},
  channelIconContainer: {
    backgroundColor: COMMON_COLORS.RED,
    alignItems: 'center',
    justifyContent: 'center',
    width: correspondentWidth(60),
    marginEnd: correspondent<PERSON>idth(18),
    padding: 12,
    borderBottomStartRadius: 10,
    borderBottomEndRadius: 10,
    elevation: 1,
  },
  notificationSummaryInfo: {
    marginEnd: correspondentWidth(25),
  },
  oneInfoDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: correspondentHeight(12),
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dateIcon: {
    marginEnd: correspondentWidth(14),
  },
  icon: {
    marginEnd: correspondentWidth(18),
  },
  periodValue: {
    color: COMMON_COLORS.BLUE_10,
    fontSize: typography.fontSizes.xs + 1,
    //fontFamily: typography.fontFamily.montserratBold,
    fontWeight: '800',
  },
});
