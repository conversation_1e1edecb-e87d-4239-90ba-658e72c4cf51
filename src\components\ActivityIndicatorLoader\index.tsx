import React, { useEffect } from "react";
import { globalStyles } from "@src/theme/style";
import { ActivityIndicator, View } from "react-native";

export const ActivityIndicatorLoader = (props: { error: boolean }) => {
  const { error } = props;

  const showLoading = () => {
    return (
      <View style={globalStyles.loaderContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    );
  };

  useEffect(() => {
    if (error) {
      showLoading();
    }
  }, [error]);

  return null;
};
