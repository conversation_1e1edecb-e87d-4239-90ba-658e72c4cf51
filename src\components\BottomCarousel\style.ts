import {StyleSheet} from 'react-native';
import {windowWidth} from '@src/constants';
import {HORIZONTAL_MARGIN} from '@src/components/ItemBottomCarousel/styles';
import layout from '@src/theme/layout';
import {correspondentHeight, correspondentWidth} from '@src/utils/imageUtils';
import {COMMON_COLORS} from '@src/theme/colors';

export const boxWidth = correspondentWidth(35);
export const returnBorderColor = (isClicked: boolean): string => {
  return isClicked ? COMMON_COLORS.SEMI_DARK_BLUE : COMMON_COLORS.TRANSPARENT;
};
const styles = StyleSheet.create({
  container: {
    ...layout.rowVCenter,
    borderRadius: 30,
    backgroundColor: COMMON_COLORS.WHITE,
    shadowColor: COMMON_COLORS.BLACK,
    zIndex: 2222222,
    elevation: 2,
    height: correspondentHeight(51),
  },
  box: {
    width: boxWidth,
    ...layout.center,
  },
  scrollViewContainer: {},
  // here the problem when we do the height and the width !
  contentContainer: {
    justifyContent: 'space-between',
  },
  imageArrow: {
    alignSelf: 'center',
  },
  logoBottomLine: {},
  itemIconContainer: {
    marginHorizontal: windowWidth * HORIZONTAL_MARGIN,
    borderBottomWidth: 1.5,
  },
});

export default styles;
