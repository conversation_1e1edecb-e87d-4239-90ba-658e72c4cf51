<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.CAMERA"/>
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>

    <application
            android:supportsRtl="true"
            android:name=".MainApplication"
            android:label="@string/app_name"
            android:usesCleartextTraffic="true"
            android:icon="@drawable/collab_logo_image"
            android:roundIcon="@drawable/collab_logo_image"
            android:allowBackup="false"
            android:launchMode="singleTask"
            android:theme="@style/AppTheme">

        <activity
                android:name=".MainActivity"
                android:label="@string/app_name"
                android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
                android:launchMode="singleTask"
                android:windowSoftInputMode="adjustResize"
                android:exported="true">
            <intent-filter auto-verify="true">
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <meta-data
                android:name="com.google.firebase.messaging.default_notification_icon"
                android:resource="@drawable/collab_logo_image"/>
    </application>
</manifest>
