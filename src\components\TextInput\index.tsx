import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  Text,
  TextInput,
} from 'react-native';
import styles from './style';
import React, {memo, useEffect} from 'react';

type TextInputProps = {
  placeholder: string;
  label: string;
  value: string | null | undefined;
  onChangeText: (value: string) => void;
};

// const initialText = (
//   selectedItems: Item[] | undefined,
//   selectedLabel: string,
// ) => {
//   const foundItem = selectedItems?.find(({label}) => selectedLabel === label);
//   return foundItem ? foundItem.value : '';
// };

// @ts-ignore
const InputText = (props: TextInputProps) => {
  const {placeholder, label, onChangeText, value} = props;

  // const handleChangeText = (newText: string) => {
  //   // setChars(prevState => [...prevState, newText]);
  //   onChangeText(newText);
  // };

  const inputRef = React.useRef<TextInput>(null);

  useEffect(() => {
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        // Dismiss focus when the keyboard is dismissed
        inputRef?.current?.blur();
      },
    );
    return () => {
      keyboardDidHideListener.remove();
    };
  }, []);
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.inputContainer}>
      <Text style={styles.label}>{label}</Text>
      <TextInput
        value={value!!}
        ref={inputRef}
        numberOfLines={1}
        onChangeText={onChangeText}
        style={styles.input}
        placeholder={placeholder}
        placeholderTextColor={'#BFC3CB'}
      />
    </KeyboardAvoidingView>
  );
};

export default memo(InputText);
