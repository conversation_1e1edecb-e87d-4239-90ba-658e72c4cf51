import React from 'react';
import {renderWithProviders} from '@src/utils/utils-for-tests';
import {ListDiscussionInteractionsWithHeaderContainer} from '@src/containers/ListDiscussionInteractionsWithHeaderContainer';
import {NavigationContainer} from '@react-navigation/native';

describe('List Discussion Interactions With Header Container', () => {
  it('should render and match the snapshot', () => {
    const toggleDrawer = jest.fn();
    const useRouteHooks = jest.fn();
    const tree = renderWithProviders(
      <NavigationContainer>
        <ListDiscussionInteractionsWithHeaderContainer
          toggleDrawer={toggleDrawer}
          useRouteHooks={useRouteHooks}
        />
      </NavigationContainer>,
      {},
    );
    expect(tree).toMatchSnapshot();
  });
});
