export const START_YEAR = 2020;
export const NUM_OF_YEARS = 3;

export const INTERVAL_GET_NOTIFICATIONS = 1000;
export const MONTH_NAMES = [
  'Jan',
  'Feb',
  'Mar',
  'Apr',
  'May',
  'Jun',
  'Jul',
  'Aug',
  'Sep',
  'Oct',
  'Nov',
  'Dec',
];
export const monthMap: {[month: string]: number} = {
  January: 1,
  February: 2,
  March: 3,
  April: 4,
  May: 5,
  June: 6,
  July: 7,
  August: 8,
  September: 9,
  October: 10,
  November: 11,
  December: 12,
};

export const invertMonthMap: {[month: number]: string} = {
  1: 'January',
  2: 'February',
  3: 'March',
  4: 'April',
  5: 'May',
  6: 'June',
  7: 'July',
  8: 'August',
  9: 'September',
  10: 'October',
  11: 'November',
  12: 'December',
};

export const YEAR_NAMES_PICKER = ['2018'];
export const MONTHS_PER_YEAR = 12;
export const QUARTERS_PER_YEAR = 4;
export const MONTHS_PER_QUARTER = 3;
export const NUM_OF_MONTHS = NUM_OF_YEARS * MONTHS_PER_YEAR;
export const MAX_TRACK_START_GAP = 10;
export const MAX_ELEMENT_GAP = 8;
export const MAX_MONTH_SPAN = 1;
export const MIN_MONTH_SPAN = 1;
export const NBR_ELEMENTS_PER_TRACK = 2;

export const STATUS_COLOR = {
  FINISH: '#00338D',
  NOT_STARTED: '#0BD1CD',
  IN_PROGRESS: '#007CFF',
};

export const STATUS = {
  FINISH: 'completed',
  NOT_STARTED: 'notStarted',
  IN_PROGRESS: 'inProgress',
  DEFAULT: 'default',
};

export const STATUS_LABELS = {
  FINISH: 'Completed',
  NOT_STARTED: 'Not started',
  IN_PROGRESS: 'In progress',
  DEFAULT: 'None',
};

export const TYPE = {
  CRITICAL_MILESTONE: 'startMilestone',
  COMPLETION_MILESTONE: 'finishMilestone',
};

export const MAX_OF_CHARACTER_NBR = 10;

export const DEFAULT_CODE = 'DEFAULT';

export const DEFAULT_NAME = 'None';

export const TIME_COLLAPSE_VIEW_OVERVIEW_PROJECT_CONTAINER = 300;
