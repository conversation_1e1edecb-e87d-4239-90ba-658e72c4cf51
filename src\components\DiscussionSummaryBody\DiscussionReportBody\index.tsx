import { Text, View } from "react-native";
import { styles } from "./style";
import React from "react";
import { useSelector } from "react-redux";
import { discussionSelector } from "@src/store/discussion/selectors";
import { CalendarDiscussionIcon } from "@src/svg/CalendarDiscussionIcon";

const NotificationReportBody = () => {
  const { discussion } = useSelector(discussionSelector);
  if (discussion === null) {
    return null;
  }
  return (
    <>
      <View style={styles.oneInfoDetail}>
        <Text>KPI NAME</Text>
        <Text>Average overdue days</Text>
      </View>
      <View style={styles.oneInfoDetail}>
        <Text>Report Period</Text>
        <View style={styles.dateContainer}>
          <View style={styles.dateIcon}>
            <CalendarDiscussionIcon />
          </View>
        </View>
      </View>
    </>
  );
};

export default NotificationReportBody;
