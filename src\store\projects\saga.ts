import { call, put, takeEvery } from "redux-saga/effects";

import { ProjectWebServices } from "@src/api/project";
import {
  FilterParams,
  getProjects,
  getProjectsWithError,
  getProjectsWithSuccess,
  updateProjects,
  updateProjectsWithError,
  updateProjectsWithSuccess
} from "@src/store/projects/slice";
import baseRequest from "@src/api/request";
import crashlytics from "@react-native-firebase/crashlytics";
import { setUserToken } from "@src/store/authentication/slice";

export function* handleGetProjects(action: { payload: FilterParams }) {
  try {
    const params = {
      ...action.payload,
      bu: action.payload.bu?.code ? action.payload.bu.code : null,
      sector: action.payload.sector?.name ? action.payload.sector.code : null,
      program: action.payload.program?.name
        ? action.payload.program.code
        : null
    };

    console.log("params of the saga , ", params.page);

    const startTime = performance.now();
    const { data } = yield call(
      baseRequest.get,
      ProjectWebServices.WS_GET_PROJECTS,
      { params }
    );

    console.log(data.content.length);

    const endTime = performance.now();
    const duration = endTime - startTime;
    crashlytics().log(
      `Request Fetching projects api took ${duration} milliseconds`
    );
    yield put(getProjectsWithSuccess(data));
  } catch (e) {
    // @ts-ignore
    const status = e?.response?.status;
    yield put(getProjectsWithError());
    if (status === 401) {
      yield put(setUserToken(null));
    }
  }
}

export function* handleUpdateProjects(action: { payload: FilterParams }) {
  try {
    const params = {
      ...action.payload,
      bu: action.payload.bu?.code,
      sector: action.payload.sector?.code,
      program: action.payload.program?.code
    };
    const { data } = yield call(
      baseRequest.get,
      ProjectWebServices.WS_GET_PROJECTS,
      { params }
    );
    crashlytics().log(" getting the projects api");
    yield put(
      updateProjectsWithSuccess({
        data
      })
    );
  } catch (e) {
    // @ts-ignore
    const status = e?.response?.status;
    crashlytics().log(e + " in get projects api");
    yield put(updateProjectsWithError());
    if (status === 401) {
      yield put(setUserToken(null));
    }
  }
}

export default function* saga() {
  yield takeEvery(getProjects, handleGetProjects);
  yield takeEvery(updateProjects, handleUpdateProjects);
}
