import {Image, Pressable} from 'react-native';
import React from 'react';
import {style} from '@src/components/ClickableImage/style';

type ClickableImageProps = {
  onPress: () => void;
  imageStyle?: {};
  url: {} | null;
  testID?: string;
  SvgComponent: any;
};
const ClickableImage: React.FC<ClickableImageProps> = ({
  onPress,
  imageStyle,
  url,
  SvgComponent,
  testID,
}) => {
  return (
    <Pressable testID={testID} onPress={onPress} style={style.container}>
      {url !== null ? <Image style={imageStyle} source={url} /> : SvgComponent}
    </Pressable>
  );
};

export default ClickableImage;
