import {call} from 'redux-saga/effects';
import {expectSaga} from 'redux-saga-test-plan';
import baseRequest from '@src/api/request';
import {ChannelsWebServices} from '@src/api/channels';
import {handleProgressFeatures} from '@src/store/channels/progress/saga';
import {ProgressChannelSummary} from '@src/models/channelsSummary/progressChannelSummary';
import {
  getProgressFeaturesWithError,
  getProgressFeaturesWithSuccess,
} from '@src/store/channels/progress/slice';

describe('cost saga', () => {
  const projectId = 22;
  const reportingDateFilter = {};
  const features: ProgressChannelSummary = {
    channelStatus: 'PRIMARY',
  };
  it('should yield an api call', () => {
    expectSaga(handleProgressFeatures)
      .provide([
        [
          call(
            baseRequest.get,
            ChannelsWebServices.WS_GET_PROGRESS_SUMMARY(projectId),
          ),
          {params: reportingDateFilter},
        ],
      ])
      .run();
  });

  it('should handle progress features successfully', () => {
    expectSaga(handleProgressFeatures)
      .provide([
        [
          call(
            baseRequest.get,
            ChannelsWebServices.WS_GET_PROGRESS_SUMMARY(projectId),
          ),
          {params: reportingDateFilter},
        ],
      ])
      .put(getProgressFeaturesWithSuccess({features}))
      .run();
  });

  it('should handle progress features with error', () => {
    expectSaga(handleProgressFeatures)
      .provide([
        [
          call(
            baseRequest.get,
            ChannelsWebServices.WS_GET_PROGRESS_SUMMARY(projectId),
          ),
          {throw: true},
        ],
      ])
      .put(getProgressFeaturesWithError())
      .run();
  });
});
