import {FlatList, Image, Text, TouchableOpacity, View} from 'react-native';
import React from 'react';
import styles from './style';
import {IMAGES} from '@src/constants/images';
import {TEST_IDS} from '@src/constants/strings';
import {useDispatch} from 'react-redux';
import {AuthManager} from '@src/auth/AuthManager';
import {logOut} from '@src/store/authentication/slice';
import {navigate} from '@src/utils/navigationUtils';
import ClickableImage from '@src/components/ClickableImage';
import useGetUserAvatar from '@src/hooks/useGetUserAvatar';
import {returnFinalBase64String} from '@src/utils/imageUtils';
import {DrawerItem} from '@src/constants/navigation';
import {COMMON_COLORS} from '@src/theme/colors';
import {Logout} from '@src/svg/Logout';
import {Close} from '@src/svg/Close';

type CustomDrawerProps = {
  onCloseDrawer: () => void;
  drawerItems: any;
  name: string;
  fcmToken: string | null | undefined;
  userId: string;
};
const CustomDrawer = (props: CustomDrawerProps) => {
  const {onCloseDrawer, drawerItems, name, userId} = props;
  const dispatch = useDispatch();

  const handleClosingBrowserManually = async () => {
    await AuthManager.signOutAsyncAndRemoveLocalData();
    dispatch(logOut());
  };

  const handleLogOut = async () => {
    try {
      await AuthManager.logOut();
    } catch (_) {
      await handleClosingBrowserManually();
    }
  };

  const userAvatarFromHook = useGetUserAvatar({userIdProps: userId});

  const renderItem = ({item}: {item: DrawerItem}) => {
    const onViewClick = () => {
      onCloseDrawer();
      navigate(item.destination);
    };

    const isItemClicked = false;

    const color = isItemClicked ? COMMON_COLORS.WHITE : COMMON_COLORS.WHITE;

    return (
      <TouchableOpacity
        onPress={onViewClick}
        style={styles.drawerItemContainer}>
        {item.iconFocused}
        <Text style={{...styles.textRouteName, color}}>{item.name}</Text>
      </TouchableOpacity>
    );
  };

  /* TODO Make the display of the image in image utils*/

  const renderUserImageProfile = () => {
    if (userAvatarFromHook.length > 0) {
      return (
        <Image
          style={styles.profileImage}
          source={{uri: returnFinalBase64String(userAvatarFromHook)}}
        />
      );
    } else {
      return <Image style={styles.profileImage} source={IMAGES.USER_AVATAR} />;
    }
  };

  // FIXME change it after testing the real fcm token with the real name user !
  return (
    <View style={styles.container}>
      {/* Top section with profile and close button */}
      <View style={styles.header}>
        <View style={styles.profileInformationContainer}>
          <View style={styles.imageContainer}>{renderUserImageProfile()}</View>
          <View style={styles.fullNameAndPositionContainer}>
            <Text style={styles.fullName}>{name}</Text>
          </View>
        </View>
        <ClickableImage
          testID={TEST_IDS.CLOSE_MENU_ICON}
          onPress={() => onCloseDrawer()}
          SvgComponent={<Close />}
          imageStyle={styles.X_button}
          url={null}
        />
      </View>

      {/* Middle section with navigation items */}
      <View style={styles.drawerItemsContainer}>
        <FlatList
          data={drawerItems}
          renderItem={renderItem}
          scrollEnabled={false} // Prevent scrolling to ensure fixed layout
        />
      </View>

      {/* Bottom section with logout button */}
      <TouchableOpacity
        style={styles.logOutButtonContainer}
        onPress={handleLogOut}
      >
        <Logout style={styles.iconImage} />
        <Text style={styles.logOutText}>Log Out</Text>
      </TouchableOpacity>
    </View>
  );
};

export default CustomDrawer;
