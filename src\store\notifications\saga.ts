import { call, put, select, takeEvery } from "redux-saga/effects";

import { NotificationsWebServices } from "@src/api/notifications";
import { authenticationSelector } from "@src/store/authentication/selectors";
import { projectSelector } from "@src/store/project/selectors";
import baseRequest from "@src/api/request";
import {
  deleteNotification,
  deleteNotificationWithError,
  deleteNotificationWithSuccess,
  getNotificationsExistence,
  getNotificationsExistenceWithError,
  getNotificationsExistenceWithSuccess,
  getNotificationsList,
  getNotificationsListWithError,
  getNotificationsListWithSuccess,
  markAsRead,
  markAsReadWithError,
  markAsReadWithSuccess
} from "@src/store/notifications/slice";

export function* handleNotificationExistenceSagas(action: {
  payload: { projectId: number };
}) {
  try {
    const { user } = yield select(authenticationSelector);
    if (user) {
      const { projectId } = action.payload;
      const params = { userId: user.userId, projectId };
      const { data } = yield call(
        baseRequest.get,
        NotificationsWebServices.WS_CHECK_NOTIFICATION_EXISTENCE,
        { params }
      );
      yield put(getNotificationsExistenceWithSuccess(data));
    }
  } catch (e) {
    yield put(getNotificationsExistenceWithError());
  }
}

export function* handleNotificationsListSagas() {
  try {
    const { projectId } = yield select(projectSelector);
    const { data } = yield call(
      baseRequest.get,
      NotificationsWebServices.WS_GET_NOTIFICATION_LIST,
      { params: { projectId: projectId } }
    );
    yield put(getNotificationsListWithSuccess(data));
  } catch (e) {
    yield put(getNotificationsListWithError());
  }
}

export function* handleMarkAsReadSaga(action: {
  payload: { notificationId: number };
}) {
  try {
    const { notificationId } = action.payload;
    const params = { notificationId };
    const { data } = yield call(
      baseRequest.post,
      NotificationsWebServices.WS_MARK_AS_READ,
      {},
      { params }
    );
    yield put(markAsReadWithSuccess(data));
  } catch (e) {
    yield put(markAsReadWithError());
  }
}

export function* handleDeleteNotificationSaga(action: {
  payload: { notificationId: number };
}) {
  try {
    const { notificationId } = action.payload;
    const params = { notificationId };
    const { data } = yield call(
      baseRequest.post,
      NotificationsWebServices.WS_MARK_AS_DELETE,
      {},
      { params }
    );
    yield put(deleteNotificationWithSuccess({ notification: data }));
  } catch (e) {
    yield put(deleteNotificationWithError());
  }
}

export default function* listenNotificationsSaga() {
  yield takeEvery(getNotificationsExistence, handleNotificationExistenceSagas);
  yield takeEvery(getNotificationsList, handleNotificationsListSagas);
  yield takeEvery(markAsRead, handleMarkAsReadSaga);
  yield takeEvery(deleteNotification, handleDeleteNotificationSaga);
}
