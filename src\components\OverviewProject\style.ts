import { StyleSheet, Platform } from 'react-native';
import { PERCENTAGES } from '@src/constants';
import typography from '@src/theme/fonts';
import { correspondentHeight, correspondentWidth } from '@src/utils/imageUtils';
import { COMMON_COLORS } from '@src/theme/colors';
import layout from '@src/theme/layout';

// Pre-calculate common values for better performance
const BORDER_RADIUS = 20;
const BOTTOM_RADIUS = 20;
const STATUS_RADIUS = 15;
const INDICATOR_WIDTH = PERCENTAGES['20'];

// Create optimized styles
const style = StyleSheet.create({
  bottomContainer: {
    flex: 1,
    zIndex: 10, // Simplified z-index
    paddingTop: correspondentHeight(45),
    borderBottomRightRadius: BOTTOM_RADIUS,
    borderBottomLeftRadius: BOTTOM_RADIUS,
    // Use hardware acceleration where possible
    ...Platform.select({
      ios: {
        shadowColor: 'transparent', // No shadow on iOS for better performance
      },
      android: {
        elevation: 0, // No elevation on Android for better performance
      },
    }),
  },
  topContainer: {
    backgroundColor: COMMON_COLORS.WHITE,
    paddingTop: correspondentHeight(21),
    paddingBottom: correspondentHeight(25),
    paddingHorizontal: correspondentWidth(22),
    borderRadius: BORDER_RADIUS,
    // Optimized shadows
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 1,
      },
      android: {
        elevation: 2,
      },
    }),
    zIndex: 5, // Simplified z-index
  },
  rowAndJustifySpaceAndAlignCenter: {
    ...layout.rowSpaceBetweenCenter,
    padding: correspondentHeight(12),
    paddingHorizontal: correspondentWidth(25),
  },
  titleAndStatusProjectContainer: layout.rowSpaceBetweenCenter,
  title: {
    color: COMMON_COLORS.SEMI_DARK_BLUE,
    fontSize: typography.fontSizes.md,
    fontFamily: typography.fontFamily.montserratSemiBold,
    maxWidth: PERCENTAGES['80'],
    // Add for better text rendering performance
    ...Platform.select({
      android: { includeFontPadding: false },
    }),
  },
  status: {
    paddingHorizontal: correspondentWidth(18),
    paddingVertical: correspondentHeight(3),
    borderRadius: STATUS_RADIUS,
  },
  statusText: {
    color: COMMON_COLORS.WHITE,
    fontSize: typography.fontSizes.xs,
    // Add for better text rendering performance
    ...Platform.select({
      android: { includeFontPadding: false },
    }),
  },
  label: {
    color: COMMON_COLORS.WHITE,
    fontSize: typography.fontSizes.xs,
    fontFamily: typography.fontFamily.montserratSemiBold,
    // Add for better text rendering performance
    ...Platform.select({
      android: { includeFontPadding: false },
    }),
  },
  value: {
    color: COMMON_COLORS.WHITE,
    fontSize: typography.fontSizes.xs + 1,
    fontFamily: typography.fontFamily.montserratRegular,
    // Add for better text rendering performance
    ...Platform.select({
      android: { includeFontPadding: false },
    }),
  },
  reference: {
    color: COMMON_COLORS.BLUE_10,
    fontSize: typography.fontSizes.xs,
    fontFamily: typography.fontFamily.montserratRegular,
    // Add for better text rendering performance
    ...Platform.select({
      android: { includeFontPadding: false },
    }),
  },
  collapsedView: {
    top: correspondentHeight(40),
    width: PERCENTAGES['100'],
    position: 'absolute',
    overflow: 'hidden', // Prevent content from leaking outside container
  },
  bottomLine: {
    borderColor: COMMON_COLORS.WHITE,
    bottom: correspondentHeight(5),
    borderWidth: 1,
    position: 'absolute',
    alignSelf: 'center',
    width: INDICATOR_WIDTH,
    ...layout.center,
  },
});
export default style;
