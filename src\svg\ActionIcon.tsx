import {Defs, G, LinearGradient, Path, Stop, Svg} from 'react-native-svg';
import React from 'react';
import {svgChannelIconHeight, svgChannelIconWidth} from '@src/theme/style';

export const ActionIcon = () => {
  return (
    <Svg
      id="trash"
      width={svgChannelIconWidth(57)}
      height={svgChannelIconHeight(52)}
      viewBox="0 0 57 52">
      <Defs>
        <LinearGradient
          id="linear-gradient"
          x1="0.158"
          y1="0.148"
          x2="0.886"
          y2="0.783"
          gradientUnits="objectBoundingBox">
          <Stop offset="0" stopColor="#007cff" />
          <Stop offset="1" stopColor="#0af" />
        </LinearGradient>
      </Defs>
      <G
        id="Action_with_BG"
        data-name="Action with BG"
        transform="translate(-41.613 -92)">
        <Path
          id="Rectangle_5654"
          data-name="Rectangle 5654"
          d="M0,0H57a0,0,0,0,1,0,0V42A10,10,0,0,1,47,52H10A10,10,0,0,1,0,42V0A0,0,0,0,1,0,0Z"
          transform="translate(41.613 92)"
          fill="url(#linear-gradient)"
        />
        <G id="Action" transform="translate(54.614 102)">
          <Path
            id="Tracé_53109"
            data-name="Tracé 53109"
            d="M7,10a5,5,0,1,1,5-5,5,5,0,0,1-5,5ZM7,2a3,3,0,1,0,3,3A3,3,0,0,0,7,2Z"
            fill="#fff"
          />
          <Path
            id="Tracé_53110"
            data-name="Tracé 53110"
            d="M4,16H2a1,1,0,0,1-.98-.8l-1-5A1,1,0,0,1,.553,9.105l3.6-1.8a1,1,0,1,1,.894,1.8L2.13,10.553,2.82,14H4a1,1,0,0,1,0,2Z"
            fill="#fff"
          />
          <Path
            id="Tracé_53111"
            data-name="Tracé 53111"
            d="M12,16H8a1,1,0,0,1,0-2h3.18l.69-3.447L8.953,9.1a1,1,0,1,1,.894-1.789l3.6,1.8A1,1,0,0,1,13.98,10.2l-1,5A1,1,0,0,1,12,16Z"
            fill="#fff"
          />
          <Path
            id="Tracé_53112"
            data-name="Tracé 53112"
            d="M25,26a5,5,0,1,1,5-5,5,5,0,0,1-5,5Zm0-8a3,3,0,1,0,3,3,3,3,0,0,0-3-3Z"
            fill="#fff"
          />
          <Path
            id="Tracé_53113"
            data-name="Tracé 53113"
            d="M22,32H20a1,1,0,0,1-.98-.8l-1-5a1,1,0,0,1,.533-1.091l3.6-1.8a1,1,0,1,1,.894,1.789L20.13,26.553,20.82,30H22a1,1,0,0,1,0,2Z"
            fill="#fff"
          />
          <Path
            id="Tracé_53114"
            data-name="Tracé 53114"
            d="M30,32H26a1,1,0,0,1,0-2h3.18l.69-3.447L26.953,25.1a1,1,0,1,1,.894-1.789l3.6,1.8A1,1,0,0,1,31.98,26.2l-1,5A1,1,0,0,1,30,32Z"
            fill="#fff"
          />
          <Path
            id="Tracé_53115"
            data-name="Tracé 53115"
            d="M25,14a1,1,0,0,1-1-1V11a5.006,5.006,0,0,0-5-5H17a1,1,0,0,1,0-2h2a7.008,7.008,0,0,1,7,7v2A1,1,0,0,1,25,14Z"
            fill="#fff"
          />
          <Path
            id="Tracé_53116"
            data-name="Tracé 53116"
            d="M17,8a1,1,0,0,1-.707-.293l-2-2a1,1,0,0,1,0-1.414l2-2a1,1,0,1,1,1.414,1.414L16.414,5l1.293,1.293A1,1,0,0,1,17,8Z"
            fill="#fff"
          />
          <Path
            id="Tracé_53117"
            data-name="Tracé 53117"
            d="M13,28H11a5.006,5.006,0,0,1-5-5V19a1,1,0,0,1,2,0v4a3,3,0,0,0,3,3h2a1,1,0,0,1,0,2Z"
            fill="#fff"
          />
          <Path
            id="Tracé_53118"
            data-name="Tracé 53118"
            d="M13,30a1,1,0,0,1-.707-1.707L13.586,27l-1.293-1.293a1,1,0,1,1,1.414-1.414l2,2a1,1,0,0,1,0,1.414l-2,2A1,1,0,0,1,13,30Z"
            fill="#fff"
          />
        </G>
      </G>
    </Svg>
  );
};
