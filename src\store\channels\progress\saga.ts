import {call, put, select, takeEvery} from 'redux-saga/effects';
import {
  getProgressFeatures,
  getProgressFeaturesWithError,
  getProgressFeaturesWithSuccess,
} from './slice';
import {ChannelsWebServices} from '@src/api/channels';
import {projectSelector} from '@src/store/project/selectors';
import baseRequest from '@src/api/request';

export function* handleProgressFeatures() {
  try {
    const {currentProject, reportingDateFilter} = yield select(projectSelector);
    const projectId = currentProject?.id;
    const {data} = yield call(
      baseRequest.get,
      ChannelsWebServices.WS_GET_PROGRESS_SUMMARY(projectId),
      {params: {...reportingDateFilter, projectId}},
    );
    yield put(getProgressFeaturesWithSuccess({features: data}));
  } catch (e) {
    yield put(getProgressFeaturesWithError());
  }
}

export default function* saga() {
  // same as incrementNumber.type or incrementNumber
  yield takeEvery(getProgressFeatures, handleProgressFeatures);
}
