import React from 'react';
import {renderWithProviders} from '@src/utils/utils-for-tests';
import {NavigationContainer} from '@react-navigation/native';
import ListProjectsWithHeaderWithContainer from '@src/containers/ListProjectsWithHeaderContainer';
import {waitFor} from '@testing-library/react-native';

describe('List Projects With Header  Container', () => {
  it('should render and match the snapshot', async () => {
    const tree = renderWithProviders(
      <NavigationContainer>
        <ListProjectsWithHeaderWithContainer />
      </NavigationContainer>,
      {},
    ).toJSON();
    await waitFor(() => {
      expect(tree).toMatchSnapshot();
    });
  });
});
