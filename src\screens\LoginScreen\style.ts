import {StyleSheet} from 'react-native';
import typography from '@src/theme/fonts';
import {correspondentHeight, correspondentWidth} from '@src/utils/imageUtils';
import {COMMON_COLORS} from '@src/theme/colors';

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: correspondentWidth(70),
  },
  logoImage: {
    width: correspondentWidth(52),
    height: correspondentHeight(52),
    marginVertical: correspondentHeight(55),
    flex: 1,
    alignSelf: 'center',
  },
  title: {
    fontSize: typography.fontSizes['2xl'],
    color: COMMON_COLORS.BLUE_40,
    fontWeight: 'bold',
    textAlign: 'center',
    alignSelf: 'center',
    textTransform: 'uppercase',
    fontFamily: typography.fontFamily.montserratBlack,
    marginBottom: correspondentHeight(9),
  },
  subTitle: {
    fontFamily: typography.fontFamily.montserratRegular,
    fontSize: typography.fontSizes.sm,
    textAlign: 'center',
    color: COMMON_COLORS.BLUE_10,
    marginBottom: correspondentHeight(59),
  },
  vectorImage: {
    height: correspondentHeight(285),
    width: correspondentWidth(248),
    marginBottom: correspondentHeight(111),
    alignSelf: 'center',
  },
  jesa_logo: {
    alignSelf: 'center',
    marginTop: correspondentHeight(22),
  },
});
export default styles;
