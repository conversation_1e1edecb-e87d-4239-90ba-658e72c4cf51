import {call, put, select, takeEvery} from 'redux-saga/effects';

import {projectSelector} from '@src/store/project/selectors';
import baseRequest from '@src/api/request';
import {ChannelsWebServices} from '@src/api/channels';
import {
  getHumanCapFeatures,
  getHumanCapFeaturesWithError,
  getHumanCapFeaturesWithSuccess,
} from '@src/store/channels/humanCapital/slice';

export function* handleHumanCapFeatures() {
  try {
    const {projectId, reportingDateFilter} = yield select(projectSelector);
    const {data} = yield call(
      baseRequest.get,
      ChannelsWebServices.WS_GET_HUMAN_CAPITAL_SUMMARY(projectId),
      {params: reportingDateFilter},
    );
    yield put(getHumanCapFeaturesWithSuccess({features: data}));
  } catch (e) {
    yield put(getHumanCapFeaturesWithError());
  }
}

export default function* HumanCapSaga() {
  yield takeEvery(getHumanCapFeatures, handleHumanCapFeatures);
}
