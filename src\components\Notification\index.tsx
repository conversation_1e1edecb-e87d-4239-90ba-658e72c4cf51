import React from "react";
import { Pressable, Text, View } from "react-native";
import styles from "@src/components/Notification/style";
import { NotificationModel } from "@src/models/notification";
import { backgroundColorNotificationContainer } from "@src/theme/style";
import { CustomLinearGradient } from "src/components/CustomLinearGradient";
import { miniNotificationIcon } from "@src/utils/imageUtils";

type NotificationComponentProps = {
  onClick: (notification: NotificationModel) => void;
  isRead?: boolean;
  notification: NotificationModel;
  type: string;
  time: string;
};

const Notification = ({
                        type,
                        isRead,
                        notification,
                        time,
                        onClick
                      }: NotificationComponentProps) => {
  let backgroundColor = backgroundColorNotificationContainer(isRead!!);

  return (
    <Pressable
      style={[styles.container, { backgroundColor }]}
      onPress={() => {
        onClick(notification);
      }}>
      <View style={styles.iconTitleTimeNotificationContainer}>
        <View style={styles.leftContainer}>
          <View style={styles.iconContainer}>
            {miniNotificationIcon(type, { ...styles.titleIcon })}
          </View>
          <View style={styles.titleNotificationContainer}>
            <Text style={styles.title}>{notification.title}</Text>
          </View>
          {!isRead && (
            <CustomLinearGradient
              firstColor={"#FF7675"}
              secondColor={"#E64748"}
              x_start={0}
              y_start={0}
              x_end={1}
              y_end={0}
              style={styles.dot}
            />
          )}
        </View>
        <View style={styles.timeContainer}>
          <Text style={styles.time}>{time}</Text>
        </View>
      </View>
      <View style={styles.contentContainer}>
        <Text style={styles.content}>{notification.message}</Text>
      </View>
      <View style={styles.bottomLine} />
    </Pressable>
  );
};

export default Notification;
