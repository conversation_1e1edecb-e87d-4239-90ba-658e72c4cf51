import {FilterBottomModal} from '@src/components/FilterBottomModal';
import {renderWithProviders} from '@src/utils/utils-for-tests';
import React from 'react';
import {FilterParams} from '@src/store/projects/slice';

const mockOnClose = jest.fn();
const filterParams: FilterParams = {
  page: 0,
  platformCode: '',
  projectNumber: '0',
  projectName: '',
  size: 2,
  bu: {
    id: 1,
    code: '',
    name: '',
    creationDate: '',
    updateDate: '',
  },
  sector: {
    id: 1,
    code: '',
    name: '',
    creationDate: '',
    updateDate: '',
  },
  program: {
    id: 1,
    code: '',
    name: '',
    creationDate: '',
    updateDate: '',
  },
};
describe('Testing the custom filter bottom sheet modal', function () {
  it('Should render and match the snapshot', () => {
    const tree = renderWithProviders(
      <FilterBottomModal filterParams={filterParams} onClose={mockOnClose} />,
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
