import {Defs, LinearGradient, Path, Stop, Svg} from 'react-native-svg';
import React from 'react';
import {svgChannelIconHeight, svgChannelIconWidth} from '@src/theme/style';

export const UpArrowEvolution = () => {
  return (
    <Svg
      width={svgChannelIconWidth(17.632)}
      height={svgChannelIconHeight(9.918)}
      viewBox="0 0 17.632 9.918">
      <Defs>
        <LinearGradient
          id="linear-gradient"
          x1="0.5"
          x2="0.5"
          y2="1"
          gradientUnits="objectBoundingBox">
          <Stop offset="0" stopColor="#ff7675" />
          <Stop offset="1" stopColor="#e64748" />
        </LinearGradient>
      </Defs>
      <Path
        id="Tracé_9506"
        data-name="Tracé 9506"
        d="M927.1,503.918H942.53a1.1,1.1,0,0,0,.775-1.877l-7.714-7.714a1.082,1.082,0,0,0-1.55,0l-7.714,7.714a1.1,1.1,0,0,0,.775,1.877Zm0,0"
        transform="translate(-926 -494)"
        fill="url(#linear-gradient)"
      />
    </Svg>
  );
};
