import reducer, {
  cleanUpQa,
  getQAFeatures,
  getQAFeaturesWithError,
  getQAFeaturesWithSuccess,
  initialState,
} from '@src/store/channels/qa/slice';
import {CostChannelSummary} from '@src/models/channelsSummary/costChannelSummary';

const features: CostChannelSummary = {
  channelStatus: 'PRIMARY',
};
describe('Cost reducer', () => {
  it('should return the initial state', () => {
    expect(reducer(undefined, {type: undefined})).toEqual(initialState);
  });

  it('should handle getCostFeatures action', () => {
    const action = getQAFeatures();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      error: false,
      success: false,
    });
  });

  it('should handle getQAFeaturesWithSuccess action when the channel status is not null', () => {
    const action = getQAFeaturesWithSuccess({features});
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      features: action.payload.features,
      channelStatus: action.payload.features.channelStatus,
      error: false,
      success: true,
    });
  });

  it('should handle getQAFeaturesWithSuccess action when the channel status is null', () => {
    const action = getQAFeaturesWithSuccess({
      features: {
        ...features,
        channelStatus: null,
      },
    });
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      features: action.payload.features,
      error: false,
      success: true,
    });
  });

  it('should handle getQAFeaturesWithError action', () => {
    const action = getQAFeaturesWithError();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      error: true,
      success: false,
    });
  });

  it('should handle cleanUp action', () => {
    const action = cleanUpQa();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
    });
  });
});
