import {StyleSheet} from 'react-native';
import typography from '@src/theme/fonts';
import {COMMON_COLORS} from '@src/theme/colors';
import {correspondentHeight, correspondentWidth} from '@src/utils/imageUtils';

export const styles = StyleSheet.create({
  container: {
    marginBottom: correspondentHeight(22),
    borderRadius: 20,
    shadowColor: COMMON_COLORS.BLACK,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 3,
    backgroundColor: COMMON_COLORS.WHITE,
    paddingStart: correspondentWidth(25),
    paddingBottom: correspondentHeight(26),
  },
  channelIconAndTitle: {
    flexDirection: 'row',
    alignItems: 'center',
    marginEnd: correspondentWidth(25),
  },
  channelIcon: {},
  channelIconContainer: {
    backgroundColor: COMMON_COLORS.RED,
    width: correspondentWidth(58),
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    marginEnd: correspondentWidth(18),
    borderBottomStartRadius: 10,
    borderBottomEndRadius: 10,
    elevation: 1,
  },
  notificationSummaryInfo: {
    marginEnd: correspondentWidth(25),
  },
  oneInfoDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: correspondentHeight(12),
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dateIcon: {
    marginEnd: correspondentWidth(14),
  },
  icon: {
    marginEnd: correspondentWidth(18),
  },
  typeDiscussion: {
    color: COMMON_COLORS.BLUE_10,
    fontSize: typography.fontSizes.sm,
    fontFamily: typography.fontFamily.montserratRegular,
    maxWidth: correspondentWidth(215),
    fontWeight: '800',
  },
  typeDiscussionTitle: {
    color: COMMON_COLORS.GREY_20,
    fontStyle: 'italic',
    fontSize: typography.fontSizes.sm,
    fontFamily: typography.fontFamily.montserratRegular,
    fontWeight: '300',
  },
});
