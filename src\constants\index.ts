import VersionInfo from 'react-native-version-info';
import {Dimensions, StatusBar} from 'react-native';

const PERCENTAGES = {
  100: '100%',
  95: '95%',
  90: '90%',
  80: '80%',
  75: '75%',
  70: '70%',
  65: '65%',
  60: '60%',
  55: '55%',
  50: '50%',
  40: '40%',
  30: '30%',
  20: '20%',
  15: '15%',
  10: '10%',
  6: '6%',
  5: '5%',
  4: '4%',
  3: '3%',
  2: '2%',
  1: '1%',
  7: '7%',
  9: '9%',
};

const OPACITY = {
  10: 0.1,
  20: 0.2,
  30: 0.3,
  40: 0.4,
  50: 0.5,
  60: 0.6,
  70: 0.7,
  80: 0.8,
  90: 0.9,
  100: 1,
};

let statusBarCurrentHeight = StatusBar.currentHeight;
export const windowHeight =
  Dimensions.get('window').height - statusBarCurrentHeight!! / 2;

export {PERCENTAGES, OPACITY};

export const windowWidth = Dimensions.get('screen').width;

export const GLOBAL_MARGIN_HORIZONTAL = windowWidth * 0.07;

export const fcmToken =
  'eNTmK8cLSzWAa7MYqc_Sp8:APA91bGRs2xE0IQhvtmQNgUK8lTOaCu0yf31tLl5-H4h00uJmMgdVn_mrJmrozdcMq1BI8rHORZpKy_P0D1sPKalNNkG2HD-wDcILPmn4vxNKuyp28hIcAmX2WSTs-E9xepIpHEdvBYQ';

export const CURRENT_APPLICATION_VERSION = VersionInfo.appVersion;
