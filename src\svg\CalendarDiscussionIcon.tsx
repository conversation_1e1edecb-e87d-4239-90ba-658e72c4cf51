import {Path, Svg} from 'react-native-svg';
import React from 'react';
import {svgChannelIconHeight, svgChannelIconWidth} from '@src/theme/style';

export const CalendarDiscussionIcon: React.FC = () => {
  return (
    <Svg
      width={svgChannelIconWidth(24.244)}
      height={svgChannelIconHeight(24.196)}
      viewBox="0 0 24.244 24.196">
      <Path
        id="Tracé_200"
        data-name="Tracé 200"
        d="M23.244,5.671A3.785,3.785,0,0,0,19.463,1.89H18.234V.945a.945.945,0,1,0-1.89,0V1.89H12.091V.945A.945.945,0,0,0,11.145,0H11.1a.945.945,0,0,0-.945.945V1.89H5.9V.945a.945.945,0,1,0-1.89,0V1.89H2.781A3.785,3.785,0,0,0-1,5.671v5.387a.943.943,0,0,0,.047.294v9.063A3.785,3.785,0,0,0,2.828,24.2H19.416A3.785,3.785,0,0,0,23.2,20.416V11.353a.943.943,0,0,0,.047-.294ZM21.306,20.416a1.892,1.892,0,0,1-1.89,1.89H2.828a1.892,1.892,0,0,1-1.89-1.89V5.671a1.892,1.892,0,0,1,1.89-1.89H4.009v.945a.945.945,0,0,0,1.89,0V3.781h4.253v.945a.945.945,0,0,0,.945.945h.047a.945.945,0,0,0,.945-.945V3.781h4.253v.945a.945.945,0,1,0,1.89,0V3.781h1.181a1.892,1.892,0,0,1,1.89,1.89Z"
        transform="translate(1)"
        fill="#007eff"
      />
      <Path
        id="Tracé_201"
        data-name="Tracé 201"
        d="M366.945,190a.945.945,0,1,0,.945.945A.945.945,0,0,0,366.945,190Z"
        transform="translate(-348.656 -181.021)"
        fill="#007eff"
      />
      <Path
        id="Tracé_202"
        data-name="Tracé 202"
        d="M279.945,190a.941.941,0,0,0-.6.216.951.951,0,0,0-.182.2.945.945,0,1,0,.784-.417Z"
        transform="translate(-265.768 -181.021)"
        fill="#007eff"
      />
      <Path
        id="Tracé_203"
        data-name="Tracé 203"
        d="M192.945,190a.941.941,0,0,0-.6.216.951.951,0,0,0-.183.2.945.945,0,1,0,.784-.417Z"
        transform="translate(-182.879 -181.021)"
        fill="#007eff"
      />
      <Path
        id="Tracé_204"
        data-name="Tracé 204"
        d="M105.945,190a.941.941,0,0,0-.6.216.951.951,0,0,0-.183.2.945.945,0,1,0,.784-.417Z"
        transform="translate(-99.991 -181.021)"
        fill="#007eff"
      />
      <Path
        id="Tracé_51806"
        data-name="Tracé 51806"
        d="M366.945,190a.945.945,0,1,0,.945.945A.945.945,0,0,0,366.945,190Z"
        transform="translate(-348.656 -177.02)"
        fill="#007eff"
      />
      <Path
        id="Tracé_51807"
        data-name="Tracé 51807"
        d="M279.945,190a.941.941,0,0,0-.6.216.951.951,0,0,0-.182.2.945.945,0,1,0,.784-.417Z"
        transform="translate(-265.768 -177.02)"
        fill="#007eff"
      />
      <Path
        id="Tracé_51808"
        data-name="Tracé 51808"
        d="M192.945,190a.941.941,0,0,0-.6.216.951.951,0,0,0-.183.2.945.945,0,1,0,.784-.417Z"
        transform="translate(-182.879 -177.02)"
        fill="#007eff"
      />
      <Path
        id="Tracé_51809"
        data-name="Tracé 51809"
        d="M105.945,190a.941.941,0,0,0-.6.216.951.951,0,0,0-.183.2.945.945,0,1,0,.784-.417Z"
        transform="translate(-99.991 -177.02)"
        fill="#007eff"
      />
      <Path
        id="Tracé_51810"
        data-name="Tracé 51810"
        d="M366.945,190a.945.945,0,1,0,.945.945A.945.945,0,0,0,366.945,190Z"
        transform="translate(-348.656 -173.02)"
        fill="#007eff"
      />
      <Path
        id="Tracé_51811"
        data-name="Tracé 51811"
        d="M279.945,190a.941.941,0,0,0-.6.216.951.951,0,0,0-.182.2.945.945,0,1,0,.784-.417Z"
        transform="translate(-265.768 -173.02)"
        fill="#007eff"
      />
      <Path
        id="Tracé_51812"
        data-name="Tracé 51812"
        d="M192.945,190a.941.941,0,0,0-.6.216.951.951,0,0,0-.183.2.945.945,0,1,0,.784-.417Z"
        transform="translate(-182.879 -173.02)"
        fill="#007eff"
      />
      <Path
        id="Tracé_51813"
        data-name="Tracé 51813"
        d="M105.945,190a.941.941,0,0,0-.6.216.951.951,0,0,0-.183.2.945.945,0,1,0,.784-.417Z"
        transform="translate(-99.991 -173.02)"
        fill="#007eff"
      />
    </Svg>
  );
};
