apply plugin: "com.android.application"
apply plugin: "com.facebook.react"
apply plugin:  "com.google.gms.google-services"
apply plugin:  "com.google.firebase.crashlytics"
apply plugin: 'com.google.firebase.appdistribution'


project.ext.envConfigFiles = [
        devDebug: ".env.dev",
        devRelease : ".env.dev",
        recetteDebug :".env.recette",
        recetteRelease :".env.recette",
        preprodDebug :".env.preprod",
        preprodRelease :".env.preprod",
        prodDebug :".env.prod",
        prodRelease :".env.prod",
]

apply from: project(':react-native-config').projectDir.getPath() + '/dotenv.gradle'



import com.android.build.OutputFile

/**
 * This is the configuration block to customize your React Native Android app.
 * By default you don't need to apply any configuration, just uncomment the lines you need.
 */
react {
    /* Folders */
    //   The root of your project, i.e. where "package.json" lives. Default is '..'
    // root = file("../")
    //   The folder where the react-native NPM package is. Default is ../node_modules/react-native
    // reactNativeDir = file("../node_modules/react-native")
    //   The folder where the react-native Codegen package is. Default is ../node_modules/react-native-codegen
    // codegenDir = file("../node_modules/react-native-codegen")
    //   The cli.js file which is the React Native CLI entrypoint. Default is ../node_modules/react-native/cli.js
    // cliFile = file("../node_modules/react-native/cli.js")

    /* Variants */
    //   The list of variants to that are debuggable. For those we're going to
    //   skip the bundling of the JS bundle and the assets. By default is just 'debug'.
    //   If you add flavors like lite, prod, etc. you'll have to list your debuggableVariants.
    // debuggableVariants = ["liteDebug", "prodDebug"]

    /* Bundling */
    //   A list containing the node command and its flags. Default is just 'node'.
    // nodeExecutableAndArgs = ["node"]
    //
    //   The command to run when bundling. By default is 'bundle'
    // bundleCommand = "ram-bundle"
    //
    //   The path to the CLI configuration file. Default is empty.
    // bundleConfig = file(../rn-cli.config.js)
    //
    //   The name of the generated asset file containing your JS bundle
    // bundleAssetName = "MyApplication.android.bundle"
    //
    //   The entry file for bundle generation. Default is 'index.android.js' or 'index.js'
    // entryFile = file("../js/MyApplication.android.js")
    //
    //   A list of extra flags to pass to the 'bundle' commands.
    //   See https://github.com/react-native-community/cli/blob/main/docs/commands.md#bundle
    // extraPackagerArgs = []

    /* Hermes Commands */
    //   The hermes compiler command to run. By default it is 'hermesc'
    // hermesCommand = "$rootDir/my-custom-hermesc/bin/hermesc"
    //
    //   The list of flags to pass to the Hermes compiler. By default is "-O", "-output-source-map"
    // hermesFlags = ["-O", "-output-source-map"]
}

/**
 * Set this to true to create four separate APKs instead of one,
 * one for each native architecture. This is useful if you don't
 * use App Bundles (https://developer.android.com/guide/app-bundle/)
 * and want to have separate APKs to upload to the Play Store.
 */
def enableSeparateBuildPerCPUArchitecture = false

/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = false

/**
 * The preferred build flavor of JavaScriptCore (JSC)
 *
 * For example, to use the international variant, you can use:
 * `def jscFlavor = 'org.webkit:android-jsc-intl:+'`
 *
 * The international variant includes ICU i18n library and necessary data
 * allowing to use e.g. `Date.toLocaleString` and `String.localeCompare` that
 * give correct results when using with locales other than en-US. Note that
 * this variant is about 6MiB larger per architecture than default.
 */
def jscFlavor = 'org.webkit:android-jsc:+'

/**
 * Private function to get the list of Native Architectures you want to build.
 * This reads the value from reactNativeArchitectures in your gradle.properties
 * file and works together with the --active-arch-only flag of react-native run-android.
 */
def reactNativeArchitectures() {
    def value = project.getProperties().get("reactNativeArchitectures")
    return value ? value.split(",") : ["armeabi-v7a", "x86", "x86_64", "arm64-v8a"]
}

android {

    def appDistributionTestersEmailList =
            "<EMAIL>," +
            "<EMAIL>, " +
            "<EMAIL>, " +
            "<EMAIL>, " +
            "<EMAIL>, " +
            "<EMAIL>, " +
            "<EMAIL>, " +
            "<EMAIL>, " +
            "<EMAIL> "

    ndkVersion rootProject.ext.ndkVersion

    compileSdkVersion rootProject.ext.compileSdkVersion

    namespace "com.jesagroup.collab"

    def versionPropsFile = file('version.properties')
    def value = 0
    Properties versionProps = new Properties()
    if (!versionPropsFile.exists()) {
        versionProps['VERSION_NAME'] = "1"
        versionProps['VERSION_MIN'] = "0"
        versionProps['VERSION_PATCH'] = "10"
        versionProps.store(versionPropsFile.newWriter(), null)
    }

    Properties localProperties = new Properties()
    def propertiesFile = file('local.properties')
    if (propertiesFile.exists()) {
        localProperties.load(propertiesFile.newDataInputStream())
    }

    def mVersionName = ""
    def mVersionMin = ""
    def mVersionPatch = ""

    if (versionPropsFile.canRead()) {
        versionProps.load(new FileInputStream(versionPropsFile))
        versionProps['VERSION_NAME'] = (versionProps['VERSION_NAME'].toInteger()).toString()
        versionProps['VERSION_MIN'] = (versionProps['VERSION_MIN'].toInteger()).toString()
        versionProps['VERSION_PATCH'] = (versionProps['VERSION_PATCH'].toInteger() + value).toString()
        versionProps.store(versionPropsFile.newWriter(), null)
        mVersionName = versionProps['VERSION_NAME'];
        mVersionMin = versionProps['VERSION_MIN'];
        mVersionPatch = versionProps['VERSION_PATCH'];

        defaultConfig {
            applicationId "com.jesagroup.collab"
            minSdkVersion rootProject.ext.minSdkVersion
            targetSdkVersion rootProject.ext.targetSdkVersion
            versionCode 1
            versionName mVersionName + "." + mVersionMin + "." + mVersionPatch
            resValue 'string', 'build_config_package','com.jesagroup.collab'
             manifestPlaceholders = [
                  // TODO : to change it for the ios 1version "jesamobile://collab"
                  appAuthRedirectScheme: 'jesamobile'
             ]
        }

        splits {
            abi {
                reset()
                enable enableSeparateBuildPerCPUArchitecture
                universalApk false  // If true, also generate a universal APK
                include (*reactNativeArchitectures())
            }
        }
    } else {
        throw new FileNotFoundException("Could not read version.properties!")
    }

    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
        release {
            storeFile file("${project.rootDir}/app/collab-mobile.keystore")
            storePassword project.env.get("KEYSTORE_PASSWORD") ?: "${localProperties.getProperty('KeyStorePassword')}"
            keyAlias project.env.get("KEY_ALIAS") ?: "${localProperties.getProperty('KeyAlias')}"
            keyPassword project.env.get("KEY_PASSWORD") ?: "${localProperties.getProperty('KeyPassword')}"

           v1SigningEnabled true
           v2SigningEnabled true
        }
    }

    buildTypes {

        debug {
            signingConfig signingConfigs.debug
        }
        release {

            signingConfig signingConfigs.release
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
        }
    }

    flavorDimensions 'env'

    productFlavors {
        dev {
            dimension "env"
            applicationIdSuffix ".dev"
        }

        recette {
            dimension "env"
            applicationIdSuffix ".recette"

            firebaseAppDistribution {
                serviceCredentialsFile = "${project.rootDir}/app/service-account.json"
                artifactType = "APK"
                releaseNotes = "full recette release"
                testers = appDistributionTestersEmailList
            }
        }

        preprod {
            dimension "env"
            applicationIdSuffix ".preprod"

            firebaseAppDistribution {
                serviceCredentialsFile = "${project.rootDir}/app/service-account.json"
                artifactType = "APK"
                releaseNotes = "full preprod release"
                testers = appDistributionTestersEmailList
            }
        }

        prod {
            dimension "env"
            firebaseAppDistribution {
                serviceCredentialsFile = "${project.rootDir}/app/service-account.json"
                artifactType = "APK"
                releaseNotes = "full prod release"
                testers = appDistributionTestersEmailList
            }
        }

    }
}

dependencies {
    // The version of react-native is set by the React Native Gradle Plugin
    implementation("com.facebook.react:react-android")

    implementation("androidx.swiperefreshlayout:swiperefreshlayout:1.0.0")

    implementation project(':react-native-splash-screen')

    implementation platform('com.google.firebase:firebase-bom:32.0.0')

    implementation("androidx.core:core-splashscreen:1.0.0") // add this line

    //implementation project(':react-native-permissions')

    implementation project(':react-native-version-info')

    implementation("com.facebook.flipper:flipper:${FLIPPER_VERSION}")
    implementation("com.facebook.flipper:flipper-network-plugin:${FLIPPER_VERSION}") {
        exclude group:'com.squareup.okhttp3', module:'okhttp'

    // Fix Duplicate class
    implementation(platform("org.jetbrains.kotlin:kotlin-bom:1.8.0"))

    }

    implementation("com.facebook.flipper:flipper-fresco-plugin:${FLIPPER_VERSION}")
    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }
}

apply from: file("../../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesAppBuildGradle(project)
