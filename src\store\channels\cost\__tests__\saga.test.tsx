import {call} from 'redux-saga/effects';
import {expectSaga} from 'redux-saga-test-plan';
import baseRequest from '@src/api/request';
import {handleCashFlowFeatures} from '@src/store/channels/cashFlow/saga';
import {ChannelsWebServices} from '@src/api/channels';
import {handleCostFeatures} from '@src/store/channels/cost/saga';
import {
  getCostFeaturesWithError,
  getCostFeaturesWithSuccess,
} from '@src/store/channels/cost/slice';
import {CostChannelSummary} from '@src/models/channelsSummary/costChannelSummary';

describe('cost saga', () => {
  const projectId = 22;
  const reportingDateFilter = {};
  const features: CostChannelSummary = {
    channelStatus: 'PRIMARY',
  };
  it('should yield an api call', () => {
    expectSaga(handleCostFeatures)
      .provide([
        [
          call(
            baseRequest.get,
            ChannelsWebServices.WS_GET_COST_SUMMARY(projectId),
          ),
          {params: reportingDateFilter},
        ],
      ])
      .run();
  });

  it('should handle cost flow features successfully', () => {
    expectSaga(handleCostFeatures)
      .provide([
        [
          call(
            baseRequest.get,
            ChannelsWebServices.WS_GET_COST_SUMMARY(projectId),
          ),
          {params: reportingDateFilter},
        ],
      ])
      .put(getCostFeaturesWithSuccess({features}))
      .run();
  });

  it('should handle changeManagement features with error', () => {
    expectSaga(handleCashFlowFeatures)
      .provide([
        [
          call(
            baseRequest.get,
            ChannelsWebServices.WS_GET_COST_SUMMARY(projectId),
          ),
          {throw: true},
        ],
      ])
      .put(getCostFeaturesWithError())
      .run();
  });
});
