import React from 'react';
import {CalendarIconHeader} from '@src/components/CalendarIconHeader';
import {renderWithProviders} from '@src/utils/utils-for-tests';

describe('CalendarIconHeader ', () => {
  const onPress = jest.fn();
  it('Should Render and match the snapshot', () => {
    const tree = renderWithProviders(
      <CalendarIconHeader onPress={onPress} />,
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });
  it('should render without crashing', () => {
    const component = renderWithProviders(
      <CalendarIconHeader onPress={onPress} />,
    );
    expect(component).toBeDefined();
  });
});
