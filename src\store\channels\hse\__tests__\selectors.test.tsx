import {RootState} from '@src/store/store';
import {HseSliceType} from '@src/store/channels/hse/slice';
import {hseSelector} from '@src/store/channels/hse/selectors';

describe('HseSelector', () => {
  it('should select the hse state from the root state', () => {
    // Arrange
    const hseState: HseSliceType = {
      features: null,
      channelStatus: 'PRIMARY',
      loading: false,
      error: false,
      success: false,
    };

    // @ts-ignore
    const mockRootState: RootState = {
      hseChannel: hseState,
    };

    // Act
    const selectedState = hseSelector(mockRootState);

    // Assert
    expect(selectedState).toEqual(hseState);
  });
});
