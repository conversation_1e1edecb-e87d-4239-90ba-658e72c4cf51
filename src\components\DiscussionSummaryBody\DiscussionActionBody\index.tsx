import { Text, View } from "react-native";
import { styles } from "./style";
import React from "react";
import { useSelector } from "react-redux";
import { discussionSelector } from "@src/store/discussion/selectors";
import { DateUtils } from "@src/utils/dateUtils";
import { CalendarDiscussionIcon } from "@src/svg/CalendarDiscussionIcon";

const DiscussionActionBody = () => {
  const { discussion } = useSelector(discussionSelector);
  if (discussion !== null) {
    const { periodOfData, description } = discussion;
    console.log(discussion);
    const month = DateUtils.convertMonthNumberToName(periodOfData.month);
    return (
      <>
        <View style={styles.description}>
          <Text style={styles.descriptionText}>"{description}"</Text>
        </View>
        <View style={styles.dateContainer}>
          <View style={styles.dateIcon}>
            <CalendarDiscussionIcon />
          </View>
          <Text style={styles.periodValue}>
            {month ?? ""} {periodOfData.year ?? ""}
          </Text>
        </View>
      </>
    );
  } else {
    return null;
  }
};

export default DiscussionActionBody;
