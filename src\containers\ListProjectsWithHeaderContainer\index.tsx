import React, { useCallback, useState } from "react";
import { View } from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { projectsSelector } from "@src/store/projects/selectors";
import { FilterParams, getProjects, resetFilterItem, setFilterParams, updateProjects } from "@src/store/projects/slice";
import styles from "./style";
import FilterProjects from "@src/containers/FilterProjects";
import Loader from "@src/components/Loader";
import ListProjects from "@src/containers/ListProjects";
import FilterBottomModal from "@src/components/FilterBottomModal";
import { ROUTE_NAMES } from "@src/constants/navigation";
import useScreenFocusAndBlurListener from "@src/hooks/useScreenFocusListener";
import { projectSelector } from "@src/store/project/selectors";
import { cleanUpNotifications, getNotificationsExistence } from "@src/store/notifications/slice";
import { cleanUp } from "@src/store/project/slice";
import { notificationSelector } from "@src/store/notification/selectors";
import NotificationsIconHeaderContainer from "@src/containers/headers/NotificationsIconHeaderContainer";
import Header from "@src/components/Header";
import { globalStyles } from "@src/theme/style";

const ListProjectsWithHeaderWithContainer = () => {
  const dispatch = useDispatch();
  const { filterParams, projects, loading, last, error } =
    useSelector(projectsSelector);
  const { projectId } = useSelector(projectSelector);
  const { isOpenedFromNotification } = useSelector(notificationSelector);
  useScreenFocusAndBlurListener(
    [
      {
        action: getNotificationsExistence,
        params: { projectId: null }
      },
      {
        action: projects.length === 0 && !error ? getProjects : null,
        params: filterParams
      },
      { action: !isOpenedFromNotification ? cleanUp : null }
    ],
    [{
      action: cleanUpNotifications
    }]
  );
  const handleOpenModal = useCallback(() => {
    setIsModalVisible(true);
  }, []);

  const handleCloseModal = useCallback(() => {
    setIsModalVisible(false);
  }, []);

  const [isModalVisible, setIsModalVisible] = useState(false);

  const handleRefreshPage = () => {
    dispatch(getProjects(filterParams));
  };

  const onApplyFilter = useCallback((localStateModalFilter: FilterParams) => {
    dispatch(
      getProjects({
        ...localStateModalFilter,
        page: 0
      })
    );
    dispatch(setFilterParams({ ...localStateModalFilter, page: 0 }));
    handleCloseModal();
  }, []);

  const updateCurrentPageProjects = (page: number) => {
    dispatch(updateProjects({ ...filterParams, page }));
    dispatch(setFilterParams({ ...filterParams, page }));
  };

  const handleResetFilterOption = (key: string) => {
    const filterUpdated = { ...filterParams, [key]: null, page: 0 };
    dispatch(getProjects(filterUpdated));
    dispatch(resetFilterItem());
  };

  return (
    <View style={styles.container}>
      <View style={styles.screenContainer}>
        <Header
          routeName={ROUTE_NAMES.HOME}
          rightContent={
            <View style={globalStyles.iconsContainer}>
              <NotificationsIconHeaderContainer projectId={projectId} />
            </View>
          }
          showBackButton={false}
        />
        <FilterProjects
          onClick={handleOpenModal}
          filter={filterParams}
          handleResetFilterOption={handleResetFilterOption}
        />
        <ListProjects
          handleRefreshPage={handleRefreshPage}
          projects={projects}
          page={filterParams.page}
          updateCurrentPageProjects={updateCurrentPageProjects}
          isLastPage={last}
          isLoading={loading}
          emptyListMessage={"No Projects Found for the moment"}
        />
        {/*{error && <Error />}*/}
        {isModalVisible && (
          <FilterBottomModal
            filterParams={filterParams}
            onApplyFilter={onApplyFilter}
            onClose={handleCloseModal}
            visible={isModalVisible}
          />
        )}
        {loading && (<Loader show={true} />)}
      </View>
    </View>
  );
};
export default React.memo(ListProjectsWithHeaderWithContainer);
