import {G, Path, Svg} from 'react-native-svg';
import React from 'react';

export const CalendarIcon: React.FC<SvgProps> = props => {
  return (
    <Svg
      style={props.style}
      width="21.709"
      height="21.709"
      viewBox="0 0 21.709 21.709">
      <G id="Calendar" transform="translate(-2 -2)">
        <Path
          id="Tracé_53119"
          data-name="Trac<PERSON> 53119"
          d="M2,10q0,.167,0,.339v5.835a14.848,14.848,0,0,0,.259,3.14A4.519,4.519,0,0,0,3.42,21.605a4.518,4.518,0,0,0,2.292,1.161,14.843,14.843,0,0,0,3.14.259h8.006A14.844,14.844,0,0,0,20,22.766a4.129,4.129,0,0,0,3.453-3.453,14.844,14.844,0,0,0,.259-3.14V10.339q0-.171,0-.339Z"
          transform="translate(0 0.684)"
          fill="#394861"
        />
        <Path
          id="Tracé_53120"
          data-name="Tracé 53120"
          d="M5.245,4.548V3.085a1.085,1.085,0,0,1,2.171,0V4.209c.446-.027.921-.038,1.424-.038h8.006c.5,0,.978.011,1.424.038V3.085a1.085,1.085,0,0,1,2.171,0V4.548a4.239,4.239,0,0,1,1.836,1.044,4.518,4.518,0,0,1,1.161,2.292q.067.3.114.63H2.133q.048-.328.114-.63A4.519,4.519,0,0,1,3.409,5.591,4.239,4.239,0,0,1,5.245,4.548Z"
          transform="translate(0.011)"
          fill="#394861"
        />
      </G>
    </Svg>
  );
};
