import React from 'react';
import {renderWithProviders} from '@src/utils/utils-for-tests';
import Drawer from '@src/components/Drawer';

describe('Drawer', () => {
  it('Should render and match the snapshot', () => {
    const onCloseDrawer = jest.fn();
    const tree = renderWithProviders(
      <Drawer
        onCloseDrawer={onCloseDrawer}
        drawerItems={undefined}
        name={''}
      />,
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
