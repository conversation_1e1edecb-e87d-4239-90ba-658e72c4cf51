import React from 'react';
import {renderWithProviders} from '@src/utils/utils-for-tests';
import CarouselChannels, {ChannelModel} from '@src/components/CarouselChannels';
import {COLLAB_MODULES_TITLES} from '@src/constants/channels/modules';
import ProgressFeatures from '@src/containers/ProgressFeatures';
import ProgressFocusedIcon from '@src/svg/channels/progress/ProgressFocusedIcon';
import ProgressUnfocusedIcon from '@src/svg/channels/progress/ProgressUnfocusedIcon';

jest.mock('@src/components/Channel', () => () => 'Channel');
const updateCarouselPosition = jest.fn();

describe('CarouselChannels', () => {
  const selector = jest.fn();

  const channels: ChannelModel[] = [
    {
      title: 'left',
      isDisplayed: true,
    },
    {
      title: COLLAB_MODULES_TITLES.PROGRESS,
      isDisplayed: true,
      content: <ProgressFeatures />,
      smallIconFocused: <ProgressFocusedIcon />,
      smallIconUnFocused: <ProgressUnfocusedIcon />,
      selector: selector,
    },
    {
      title: 'right',
      isDisplayed: true,
    },
  ];

  const currentPosition = 0;

  it('Should render and match the snapshot', () => {
    const tree = renderWithProviders(
      <CarouselChannels
        channels={channels}
        updateCarouselPosition={updateCarouselPosition}
        currentPosition={currentPosition}
      />,
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
