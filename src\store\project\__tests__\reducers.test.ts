import reducer, {
  cleanUp,
  getFlatten<PERSON><PERSON>,
  getFlattenKpisWithError,
  getFlattenKpisWithSuccess,
  getUserProjectWithProjectIdAndUserId,
  getUserProjectWithProjectIdAndUserIdWithError,
  getUserProjectWithProjectIdAndUserIdWithSuccess,
  initialState,
  setCurrentProject,
  setCurrentProjectId,
  setCurrentProjectWithError,
  setCurrentProjectWithSuccess,
  setReportingDate
} from "@src/store/project/slice";

describe("Project reducer", () => {
  it("should return the initial state", () => {
    expect(reducer(undefined, { type: undefined })).toEqual(initialState);
  });

  describe("should handle getUserProjectWithProjectIdAndUserId", () => {
    it("should handle getUserProjectWithProjectIdAndUserId action", () => {
      const action = getUserProjectWithProjectIdAndUserId({ projectId: 22 });
      const newState = reducer(initialState, action);
      expect(newState).toEqual({
        ...initialState,
        loading: true,
        successFlattenKpis: false,
        errorFlattenKpis: false
      });
    });

    it("should handle getUserProjectWithProjectIdAndUserIdWithSuccess action", () => {
      const action = getUserProjectWithProjectIdAndUserIdWithSuccess({
        featurePrivileges: [],
        profile: {},
        role: {}
      });
      const newState = reducer(initialState, action);

      expect(newState).toEqual({
        ...initialState,
        profile: action.payload.profile,
        role: action.payload.role,
        loading: false,
        successUserProject: true,
        errorUserProject: false
      });
    });

    it("should handle getUserProjectWithProjectIdAndUserIdWithError action", () => {
      const action = getUserProjectWithProjectIdAndUserIdWithError();
      const newState = reducer(initialState, action);

      expect(newState).toEqual({
        ...initialState,
        loading: false,
        successUserProject: false,
        errorUserProject: true
      });
    });
  });

  describe("should handle getFlattenKpis", () => {
    it("should handle getFlattenKpis action", () => {
      const action = getFlattenKpis({ projectId: 22 });
      const newState = reducer(initialState, action);
      expect(newState).toEqual({
        ...initialState,
        loading: true,
        successUserProject: false,
        errorUserProject: false
      });
    });

    it("should handle getFlattenKpisWithSuccess action", () => {
      const action = getFlattenKpisWithSuccess({});
      const newState = reducer(initialState, action);

      expect(newState).toEqual({
        ...initialState,
        flattenKpis: action.payload,
        loading: false,
        successFlattenKpis: true,
        errorFlattenKpis: false
      });
    });

    it("should handle getFlattenKpisWithError action", () => {
      const action = getFlattenKpisWithError();
      const newState = reducer(initialState, action);

      expect(newState).toEqual({
        ...initialState,
        loading: false,
        errorFlattenKpis: true,
        successFlattenKpis: false
      });
    });
  });

  describe("should handle setCurrentProject", () => {
    it("should handle setCurrentProject action", () => {
      const action = setCurrentProject({ projectId: 22 });
      const newState = reducer(initialState, action);
      expect(newState).toEqual({
        ...initialState,
        loading: true,
        success: false,
        error: false
      });
    });

    it("should handle setCurrentProjectWithSuccess action", () => {
      const action = setCurrentProjectWithSuccess({
        project: {},
        reportingDate: { month: 1, year: 2022 }
      });
      const newState = reducer(initialState, action);

      expect(newState).toEqual({
        ...initialState,
        loading: false,
        error: false,
        success: true,
        currentProject: action.payload.project,
        reportingDateFilter: action.payload.reportingDate
      });
    });

    it("should handle setCurrentProjectWithError action", () => {
      const action = setCurrentProjectWithError();
      const newState = reducer(initialState, action);

      expect(newState).toEqual({
        ...initialState,
        loading: false,
        error: true,
        success: false
      });
    });
  });

  it("should handle setCurrentProjectId action", () => {
    const action = setCurrentProjectId({ projectId: 22 });
    const newState = reducer(initialState, action);
    expect(newState).toEqual({
      ...initialState,
      projectId: action.payload
    });
  });

  it("should handle setReportingDate action", () => {
    const action = setReportingDate({ month: 1, year: 2022 });
    const newState = reducer(initialState, action);
    expect(newState).toEqual({
      ...initialState,
      reportingDateFilter: action.payload
    });
  });

  it("should handle cleanUp action", () => {
    const action = cleanUp();
    const newState = reducer(initialState, action);
    expect(newState).toEqual({
      ...initialState
    });
  });
});
