import {Type} from 'class-transformer';

export default class Profile {
  public id?: number;
  public name?: string;
  @Type(() => ProfilePrivilegeList)
  public profilePrivilegeList?: ProfilePrivilegeList[];
  public code?: string;
}

export class ProfilePrivilegeList {
  @Type(() => Privilege)
  public privilege?: Privilege;
  @Type(() => Feature)
  public feature?: Feature;
}

export class Privilege {
  public id?: number;
  public code?: string;
  public label?: string;
}

export class Feature {
  public id?: number;
  public code?: string;
  public label?: string;
  @Type(() => Module)
  public module?: Module;
}

export class Module {
  public id?: number;
  public code?: string;
  public label?: string;
}
