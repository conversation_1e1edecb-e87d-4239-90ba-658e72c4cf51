import {StyleSheet} from 'react-native';
import typography from '@src/theme/fonts';

const style = StyleSheet.create({
  valuePendingContractorChange: {
    color: '#007EFF',
    fontSize: 14,
    fontFamily: typography.fontFamily.montserratSemiBold,
    fontWeight: 'bold',
  },
  MMAD: {
    fontSize: 14,
    color: '#5C6A80',
    fontFamily: typography.fontFamily.montserratRegular,
  },
});

export default style;
