// filterSlice.js
import {createSlice} from '@reduxjs/toolkit';
import {SLICES_NAMES} from '@src/constants/store';
import {Filter} from '@src/models/projectFilter';

export type FilterEnumsProjectType = {
  buEnums: Filter[];
  loading: boolean;
  success: boolean;
  error: boolean;
};

export const initialState: FilterEnumsProjectType = {
  buEnums: [],
  success: false,
  loading: false,
  error: false,
};

const slice = createSlice({
  name: SLICES_NAMES.BU_ENUMS,
  initialState,
  reducers: {
    getBuEnums: (
      state,
      action: {
        payload: {
          types: string;
        };
      },
    ) => {
      return {
        ...state,
        error: false,
        success: false,
        loading: true,
      };
    },
    getBuEnumsWithSuccess: (state, action) => {
      return {
        ...state,
        buEnums: action.payload,
        error: false,
        success: true,
        loading: false,
      };
    },
    getBuEnumsWithError: state => {
      return {
        ...state,
        error: true,
        success: false,
        loading: false,
      };
    },
    cleanUp: state => {
      return state;
    },
  },
});

export default slice.reducer;
export const {getBuEnums, getBuEnumsWithSuccess, getBuEnumsWithError, cleanUp} =
  slice.actions;
