// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {

    def getMyVersionCode = { ->
        def code = project.hasProperty('versionCode') ? versionCode.toInteger() : -1
        println "VersionCode is set to $code"
        return code
    }

    def getMyVersionName = { ->
        def name = project.hasProperty('versionName') ? versionName : "1.0"
        println "VersionName is set to $name"
        return name
    }

    ext {
        buildToolsVersion = "33.0.0"
        minSdkVersion = 21
        compileSdkVersion = 33
        targetSdkVersion = 33

       /*  versionName = getMyVersionName()
        versionCode = getMyVersionCode()

        if (System.properties['os.arch'] == "aarch64") {
            ndkVersion = "24.0.8215888"
        } else {
            ndkVersion = "23.1.7779620"
        }
        */

        // We use NDK 23 which has both M1 support and is the side-by-side NDK version from AGP.
        ndkVersion = "23.1.7779620"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:7.3.1")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath 'com.google.gms:google-services:4.3.15'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.7.1'
        classpath 'com.google.firebase:firebase-appdistribution-gradle:3.0.3'

    }
}
