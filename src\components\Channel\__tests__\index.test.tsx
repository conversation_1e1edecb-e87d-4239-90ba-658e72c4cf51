import React from 'react';
import Channel from '@src/components/Channel';
import {IMAGES} from '@src/constants/images';
import {View} from 'react-native';
import {renderWithProviders} from '@src/utils/utils-for-tests';
import {RootState} from '@src/store/store';

describe('Channel', () => {
  it('should render and match the snapshot', () => {
    const title = 'title';
    const illustration = IMAGES.HSE_ILLUSTRATION_IMAGE;
    const selector = jest.fn((state: RootState) => state.hseChannel);

    const initialState: {
      hseChannel: {
        features: null;
        success: boolean;
        channelStatus: string;
        error: boolean;
        loading: boolean;
      };
    } = {
      hseChannel: {
        error: false,
        success: false,
        loading: false,
        features: null,
        channelStatus: 'red',
      },
    };

    // Create a mock content element
    const mockContent = <View testID="mocked-content">Mocked Content</View>;

    const screen = renderWithProviders(
      <Channel
        title={title}
        illustration={illustration}
        content={mockContent}
        selector={selector}
      />,
      {preloadedState: initialState},
    ).toJSON();

    expect(screen).toMatchSnapshot();
  });
});
