import { createSlice } from "@reduxjs/toolkit";
import { SLICES_NAMES } from "@src/constants/store";
import { ProgressChannelSummary } from "@src/models/channelsSummary/progressChannelSummary";

export type ProgressSliceType = {
  features: ProgressChannelSummary | null;
  channelStatus: string;
  loading: boolean;
  error: boolean;
  success: boolean;
};

export const initialState: ProgressSliceType = {
  features: null,
  channelStatus: "PRIMARY",
  loading: false,
  error: false,
  success: false
};
export const progressSlice = createSlice({
  name: SLICES_NAMES.PROGRESS_CHANNEL,
  initialState,
  reducers: {
    getProgressFeatures: state => {
      return {
        ...state,
        loading: true,
        error: false,
        success: false
      };
    },
    getProgressFeaturesWithSuccess: (
      state,
      action: { payload: { features: ProgressChannelSummary } }
    ) => {
      let finalChannelStatus: any;
      if (action.payload.features.channelStatus !== null) {
        finalChannelStatus = action.payload.features.channelStatus!!;
      } else {
        finalChannelStatus = "PRIMARY";
      }
      return {
        ...state,
        features: action.payload.features,
        channelStatus: finalChannelStatus,
        loading: false,
        error: false,
        success: true
      };
    },
    getProgressFeaturesWithError: state => {
      return {
        ...state,
        features: null,
        channelStatus: "PRIMARY",
        error: true,
        loading: false,
        success: false
      };
    },
    cleanUpProgress: () => {
      return initialState;
    }
  }
});

export const {
  getProgressFeatures,
  getProgressFeaturesWithSuccess,
  getProgressFeaturesWithError,
  cleanUpProgress
} = progressSlice.actions;
export default progressSlice.reducer;
