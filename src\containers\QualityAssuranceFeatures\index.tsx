import { ActivityIndicator, Text, View } from "react-native";
import React from "react";
import { ObjectUtils } from "@src/utils/objectUtils";
import {
  COLLAB_FEATURES_TITLES,
  COLLAB_SUMMARY_FEATURE_KEY_WITH_FEATURES_CODE,
  getSectionValueByKey
} from "@src/constants/channels/features";
import { useSelector } from "react-redux";
import { qaSelector } from "@src/store/channels/qa/selectors";
import style from "@src/containers/QualityAssuranceFeatures/style";
import { ChannelFeaturesDefaultValues } from "@src/constants/channelFeaturesDefaultValues";
import { KEYS_CHANNEL } from "@src/constants/channels/modules";
import { COMMON_COLORS } from "@src/theme/colors";
import { globalStyles } from "@src/theme/style";
import layout from "@src/theme/layout";
import FeatureContainer, { renderFeatureContainers } from "@src/containers/FeatureContainer";

export const Satisfaction = (props: { value: number }) => {
  return (
    <View style={layout.row}>
      <Text
        style={{
          color: COMMON_COLORS.BLUE_40,
          ...style.value
        }}>
        {props.value}
      </Text>
      <Text style={style.percentage}> %</Text>
    </View>
  );
};

export const AverageOverdueDays = (props: { value: number }) => {
  return (
    <View>
      <Text style={{ ...style.value, color: COMMON_COLORS.SEMI_LIGHT_BLUE }}>
        {props.value}
      </Text>
    </View>
  );
};

export const NumbersOfOverdueCAPAS = (props: { value: number }) => {
  return (
    <View>
      <Text style={{ ...style.value, color: COMMON_COLORS.SEMI_DARK_BLUE }}>
        {props.value}
      </Text>
    </View>
  );
};

const QualityAssuranceFeatures = () => {
  const { features, loading } = useSelector(qaSelector);

  function renderDefaultValues() {
    const cashFlowFeatures: FeatureContainer[] = [
      {
        featureKey: null,
        featureTitle: COLLAB_FEATURES_TITLES.QA.clientSatisfaction,
        featureValue: null,
        defaultFeatureValue: ChannelFeaturesDefaultValues.QA.clientSatisfaction
      },
      {
        featureKey: null,
        featureTitle: COLLAB_FEATURES_TITLES.QA.overduePinsNumber,
        featureValue: null,
        defaultFeatureValue: ChannelFeaturesDefaultValues.QA.overduePinsNumber
      },
      {
        featureKey: null,
        featureTitle: COLLAB_FEATURES_TITLES.QA.averageOpenDays,
        featureValue: null,
        defaultFeatureValue: ChannelFeaturesDefaultValues.QA.averageOpenDays
      }
    ];

    return renderFeatureContainers(cashFlowFeatures);
  }

  if (loading) {
    return (
      <View style={globalStyles.loaderContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    );
  }

  return (
    <View>
      {features
        ? ObjectUtils.convertObjectToKeyValuesArray(features).map((item) => {
          if (item.key !== KEYS_CHANNEL.COMMON.CHANNEL_STATUS) {
            const featureKey =
              getSectionValueByKey(COLLAB_SUMMARY_FEATURE_KEY_WITH_FEATURES_CODE, "QA", item.key) || null;
            const featureTitle =
              getSectionValueByKey(COLLAB_FEATURES_TITLES, "QA", item.key) || null;
            return (
              <FeatureContainer
                key={item.key}
                featureKey={featureKey !== undefined ? featureKey : null}
                featureTitle={featureTitle}
                featureValue={item.value}
                defaultFeatureValue={
                  getSectionValueByKey(ChannelFeaturesDefaultValues, "QA", item.key)
                }
              />
            );
          }
          return null;
        })
        : renderDefaultValues()}
    </View>
  );
};

export default QualityAssuranceFeatures;
