import React from 'react';
import {
  DISCUSSION_OBJECT_TYPES,
  NOTIFICATION_OBJECT_TYPES,
} from '@src/constants/notification';
import {MiniActionIcon} from '@src/svg/MiniActionIcon';
import {MiniKpiIcon} from '@src/svg/MiniKpiIcon';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import {ReportIcon} from '@src/svg/ReportIcon';
import {MiniReportIcon} from '@src/svg/MiniReportIcon';
import {ActionIcon} from '@src/svg/ActionIcon';
import {ProgressIcon} from '@src/svg/ProgressIcon';

export const miniNotificationIcon = (type: string, style: {}): any => {
  switch (type) {
    case DISCUSSION_OBJECT_TYPES.RISK:
      return <MiniActionIcon style={style} />;
    case DISCUSSION_OBJECT_TYPES.ACTION:
      return <MiniActionIcon style={style} />;

    case NOTIFICATION_OBJECT_TYPES.NEEDED_SUPPORT_INTERACTION:
      return <MiniReportIcon style={style} />;
    case NOTIFICATION_OBJECT_TYPES.REPORT_PARAMETER_INTERACTION:
      return <MiniReportIcon style={style} />;
    case NOTIFICATION_OBJECT_TYPES.REPORT_MODULE_INTERACTION:
      return <MiniReportIcon style={style} />;

    case NOTIFICATION_OBJECT_TYPES.PROGRESS:
      return <MiniKpiIcon style={style} />;
    default:
      return <MiniActionIcon style={style} />;
  }
};

export const discussionHeaderIcon = (type: string): React.ReactElement => {
  switch (type) {
    case DISCUSSION_OBJECT_TYPES.RISK:
      return <ReportIcon />;
    case DISCUSSION_OBJECT_TYPES.ACTION:
      return <ActionIcon />;
    case DISCUSSION_OBJECT_TYPES.REPORT_PARAMETER:
      return <ReportIcon />;
    case DISCUSSION_OBJECT_TYPES.PROGRESS:
      return <ProgressIcon />;
    default:
      return <ReportIcon />;
  }
};

export const returnFinalBase64String = (image: string) => {
  return `data:image/png;base64,${image}`;
};

const TEST_HEIGHT_ADOBE_XD = 844;
const TEST_WIDTH_ADOBE_XD = 390;

export const correspondentWidth = (width: number): number => {
  return wp(100 * (width / TEST_WIDTH_ADOBE_XD));
};
export const correspondentHeight = (height: number): number => {
  return hp(100 * (height / TEST_HEIGHT_ADOBE_XD));
};
