import {RootState} from '@src/store/store';
import {QaSliceType} from '@src/store/channels/qa/slice';
import {qaSelector} from '@src/store/channels/qa/selectors';

describe('QASelector', () => {
  it('should select the qa state from the root state', () => {
    // Arrange
    const qaState: QaSliceType = {
      features: null,
      channelStatus: 'PRIMARY',
      error: false,
      success: false,
    };

    // @ts-ignore
    const mockRootState: RootState = {
      qaChannel: qaState,
    };

    // Act
    const selectedState = qaSelector(mockRootState);

    // Assert
    expect(selectedState).toEqual(qaState);
  });
});
