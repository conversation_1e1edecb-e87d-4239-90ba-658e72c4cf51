import {useSelector} from 'react-redux';
import {projectSelector} from '@src/store/project/selectors';
import {authenticationSelector} from '@src/store/authentication/selectors';
/*
 * This hook is used to check if the user has the right permissions for this privilege
 * depending on his profile
 */
export const usePrivilegeByFeature = (
  feature: string,
  privilege: string,
): boolean => {
  const {featurePrivileges} = useSelector(projectSelector);
  const {user} = useSelector(authenticationSelector);
  if (featurePrivileges !== null && user !== null) {
    return (
      user.admin ||
      featurePrivileges?.some(
        profilePrivilege =>
          profilePrivilege?.featureCode === feature &&
          profilePrivilege.privilegeCode === privilege,
      )
    );
  } else {
    return false;
  }
};
