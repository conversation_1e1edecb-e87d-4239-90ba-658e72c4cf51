import {renderWithProviders} from '@src/utils/utils-for-tests';
import React from 'react';
import {ItemBottomCarousel} from '@src/components/ItemBottomCarousel';
import {View} from 'react-native';

describe('ItemBottomCarousel', function () {
  it('Should render and match the snapshot', () => {
    const tree = renderWithProviders(
      <ItemBottomCarousel source={<View />} />,
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
