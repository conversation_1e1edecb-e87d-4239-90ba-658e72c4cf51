import reducer, {
  cleanUp,
  getProgramEnums,
  getProgramEnumsWithError,
  getProgramEnumsWithSuccess,
  initialState,
} from '@src/store/filterEnumsProjects/programEnums/slice';
import ProgramSector from '@src/models/programSector';

const programEnumsExamples: ProgramSector[] = [
  {
    type: {
      type: 'array',
      code: 'code',
      label: 'label',
      id: 2,
    },
    creationDate: '2015-12-12',
    name: 'name',
    updateDate: '2015-12-12',
    id: 2,
  },
];
describe('Program Enums reducer', () => {
  it('should return the initial state', () => {
    expect(reducer(undefined, {type: undefined})).toEqual(initialState);
  });

  it('should handle getProgramEnums action', () => {
    const action = getProgramEnums();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      loading: true,
      error: false,
      success: false,
    });
  });

  it('should handle getProgramEnumsWithSuccess action ', () => {
    const action = getProgramEnumsWithSuccess(programEnumsExamples);
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      programEnums: action.payload,
      loading: false,
      error: false,
      success: true,
    });
  });

  it('should handle getProgramEnumsWithError action', () => {
    const action = getProgramEnumsWithError();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      loading: false,
      error: true,
      success: false,
    });
  });

  it('should handle cleanUp action', () => {
    const action = cleanUp();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
    });
  });
});
