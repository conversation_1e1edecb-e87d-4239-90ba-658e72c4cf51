import {StyleSheet} from 'react-native';
import {
  GLOBAL_MARGIN_HORIZONTAL,
  OPACITY,
  PERCENTAGES,
  windowWidth,
} from '@src/constants';
import typography from '@src/theme/fonts';
import layout from '@src/theme/layout';
import {COMMON_COLORS} from '@src/theme/colors';
import {correspondentHeight, correspondentWidth} from '@src/utils/imageUtils';

const DOT_WIDTH = correspondentWidth(9);
const DOT_HEIGHT = correspondentWidth(9);

const styles = StyleSheet.create({
  container: {
    paddingTop: correspondentHeight(16),
    width: windowWidth - 2 * GLOBAL_MARGIN_HORIZONTAL,
  },
  iconTitleTimeNotificationContainer: {
    ...layout.rowSpaceBetweenCenter,
  },
  titleIcon: {
    width: PERCENTAGES['100'],
    height: PERCENTAGES['100'],
  },
  time: {
    fontSize: typography.fontSizes.xs,
    opacity: OPACITY['70'],
    color: COMMON_COLORS.BLUE_20,
    fontFamily: typography.fontFamily.montserratRegular,
  },
  timeContainer: {
    maxWidth: correspondentWidth(78),
    textAlign: 'right',
  },
  content: {
    fontSize: typography.fontSizes.xs + 1,
    fontFamily: typography.fontFamily.montserratRegular,
    color: COMMON_COLORS.BLUE_20,
  },
  leftContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    maxWidth: PERCENTAGES['70'],
    minWidth: PERCENTAGES['70'],
  },
  iconContainer: {
    width: correspondentWidth(24),
    aspectRatio: 1,
  },
  contentContainer: {
    marginTop: typography.fontSizes.sm,
  },
  titleNotificationContainer: {
    marginStart: correspondentWidth(8),
  },
  title: {
    fontSize: typography.fontSizes.xs + 1,
    color: COMMON_COLORS.BLUE_20,
    fontFamily: typography.fontFamily.montserratBold,
  },
  dot: {
    width: DOT_WIDTH,
    height: DOT_HEIGHT,
    borderRadius: DOT_WIDTH / 2,
    backgroundColor: 'red',
    marginStart: correspondentWidth(2),
  },
  bottomLine: {
    backgroundColor: '#DDEBFF',
    height: 1,
    marginTop: correspondentHeight(25),
  },
  notificationContent: {
    fontSize: typography.fontSizes.md,
    fontWeight: '500',
    marginBottom: '4%',
    textAlign: 'center',
  },
  button: {
    backgroundColor: 'blue',
    padding: 12,
    paddingHorizontal: correspondentWidth(26),
    alignItems: 'center',
    borderRadius: 8,
  },
  buttonText: {
    color: COMMON_COLORS.WHITE,
    fontSize: typography.fontSizes.md,
    fontWeight: 'bold',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  deleteIconContainer: {
    height: correspondentHeight(22),
    width: correspondentWidth(22),
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    top: PERCENTAGES['40'],
    right: GLOBAL_MARGIN_HORIZONTAL,
  },

  trashIconContainerFromLeft: {
    position: 'absolute',
    height: '100%',
    left: 0,
    flex: 1,
    backgroundColor: COMMON_COLORS.RED,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: correspondentWidth(28),
  },
  trashIconContainerFromRight: {
    position: 'absolute',
    height: '100%',
    right: 0,
    backgroundColor: 'red',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: correspondentWidth(28),
  },
  details: {
    fontSize: typography.fontSizes.xs,
    color: '#999',
  },
});
export default styles;
