import reducer, {
  cleanUp,
  deleteNotification,
  deleteNotificationWithError,
  deleteNotificationWithSuccess,
  getNotificationsExistence,
  getNotificationsExistenceWithError,
  getNotificationsExistenceWithSuccess,
  getNotificationsList,
  getNotificationsListWithSuccess,
  initialState,
  mark<PERSON><PERSON><PERSON>,
  markAsReadWithError,
  markAsReadWithSuccess,
} from '@src/store/notifications/slice';

describe('Notifications reducer', () => {
  it('should return the initial state', () => {
    expect(reducer(undefined, {type: undefined})).toEqual(initialState);
  });

  describe('get notifications Existence ', () => {
    it('should handle getNotificationsExistence action', () => {
      const action = getNotificationsExistence({projectId: 22});
      const newState = reducer(initialState, action);
      expect(newState).toEqual({
        ...initialState,
        loading: true,
        errorNotificationsExistence: false,
        successNotificationsExistence: false,
      });
    });

    it('should handle getNotificationsExistenceWithSuccess action', () => {
      const payload = true; // Replace with a valid FCM token
      const action = getNotificationsExistenceWithSuccess(payload);
      const newState = reducer(initialState, action);

      expect(newState).toEqual({
        ...initialState,
        notificationExistence: action.payload,
        loading: false,
        errorNotificationsExistence: false,
        successNotificationsExistence: true,
      });
    });

    it('should handle getNotificationsExistenceWithError action', () => {
      const action = getNotificationsExistenceWithError();
      const newState = reducer(initialState, action);

      expect(newState).toEqual({
        ...initialState,
        notificationExistence: false,
        loading: false,
        errorNotificationsExistence: true,
        success: false,
      });
    });
  });

  describe('get notifications list ', () => {
    it('should handle getNotificationsList action', () => {
      const action = getNotificationsList();
      const newState = reducer(initialState, action);
      expect(newState).toEqual({
        ...initialState,
        loading: true,
        error: false,
        success: false,
      });
    });

    it('should handle getNotificationsListWithSuccess action', () => {
      const payload = [{}]; // Replace with a valid FCM token
      const action = getNotificationsListWithSuccess(payload);
      const newState = reducer(initialState, action);

      expect(newState).toEqual({
        ...initialState,
        notifications: action.payload,
        loading: false,
        success: true,
        error: false,
      });
    });
  });

  describe('markAsRead', () => {
    it('should handle markAsRead action', () => {
      const action = markAsRead({});
      const newState = reducer(initialState, action);
      expect(newState).toEqual({
        ...initialState,
        loading: true,
        markAsReadFailure: false,
        markAsReadSuccess: false,
      });
    });

    it('should handle markAsReadWithSuccess action', () => {
      const action = markAsReadWithSuccess();
      const newState = reducer(initialState, action);

      expect(newState).toEqual({
        ...initialState,
        loading: false,
        markAsReadFailure: false,
        markAsReadSuccess: true,
      });
    });

    it('should handle markAsReadWithError action', () => {
      const action = markAsReadWithError();
      const newState = reducer(initialState, action);

      expect(newState).toEqual({
        ...initialState,
        loading: true,
        markAsReadFailure: true,
        markAsReadSuccess: false,
      });
    });
  });

  describe('delete notification', () => {
    it('should handle delete action', () => {
      const action = deleteNotification({});
      const newState = reducer(initialState, action);
      expect(newState).toEqual({
        ...initialState,
        loading: true,
        error: false,
        success: false,
      });
    });

    it('should handle delete with success action', () => {
      const action = deleteNotificationWithSuccess({
        notification: {
          projectId: 2,
          id: 1,
        },
      });
      const newState = reducer(initialState, action);
      const notificationsFiltered = newState.notifications.filter(
        notification => notification.id !== action.payload.notification.id,
      );
      expect(newState).toEqual({
        ...initialState,
        notifications: notificationsFiltered,
        loading: false,
        success: true,
        error: false,
      });
    });

    it('should handle delete with error action', () => {
      const action = deleteNotificationWithError();
      const newState = reducer(initialState, action);

      expect(newState).toEqual({
        ...initialState,
        loading: false,
        error: true,
        success: false,
      });
    });
  });

  it('should handle cleanUp action', () => {
    const action = cleanUp();
    const newState = reducer(initialState, action);
    expect(newState).toEqual({
      ...initialState,
    });
  });
});
