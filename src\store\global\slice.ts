import {createSlice} from '@reduxjs/toolkit';
import {SLICES_NAMES} from '@src/constants/store';
import {CustomTheme, darkTheme, lightTheme} from '@src/theme';

export interface GlobalSliceType {
  isInternetAvailable: boolean;
  discussionId: null | number;
  isApplicationVersionUpdated: boolean | null;
  theme: CustomTheme;
}

export const initialState: GlobalSliceType = {
  isInternetAvailable: false,
  discussionId: null,
  isApplicationVersionUpdated: null,
  theme: lightTheme,
};

const slice = createSlice({
  name: SLICES_NAMES.GLOBAL_SLICE,
  initialState,
  reducers: {
    toggleTheme: state => {
      const newTheme = state.theme.dark ? lightTheme : darkTheme;
      return {
        ...state,
        theme: newTheme,
      };
    },
    setIsApplicationVersionUpdated: state => {
      return {
        ...state,
      };
    },
    setIsApplicationVersionUpdatedWithSuccess: (state, action) => {
      return {
        ...state,
        isApplicationVersionUpdated: action.payload,
      };
    },
    setIsApplicationVersionUpdatedWithError: () => {
      return {
        ...initialState,
      };
    },
    clean: () => {
      return {
        ...initialState,
      };
    },
  },
});
export const {
  clean,
  setIsApplicationVersionUpdated,
  setIsApplicationVersionUpdatedWithSuccess,
  setIsApplicationVersionUpdatedWithError,
  toggleTheme,
} = slice.actions;
export default slice.reducer;
