import {G, Path, Svg} from 'react-native-svg';
import React from 'react';

export const Actions = (props: {style: {}}) => {
  return (
    <Svg
      style={props.style}
      width="16.924"
      height="21.188"
      viewBox="0 0 16.924 21.188">
      <G id="Actions" transform="translate(-4 -2)">
        <Path
          id="primary"
          d="M15.66,6h3.2a1.066,1.066,0,0,1,1.066,1.066V20.924a1.066,1.066,0,0,1-1.066,1.066H6.066A1.066,1.066,0,0,1,5,20.924V7.066A1.066,1.066,0,0,1,6.066,6h3.2"
          transform="translate(0 0.198)"
          fill="none"
          stroke="#66bcff"
          stroke-width="2"
        />
        <Path
          id="secondary"
          d="M14.33,5.132h0A2.132,2.132,0,0,0,12.2,3h0a2.132,2.132,0,0,0-2.132,2.132h0A1.066,1.066,0,0,0,9,6.2V8.33h6.4V6.2A1.066,1.066,0,0,0,14.33,5.132Z"
          transform="translate(0.264)"
          fill="none"
          stroke="#66bcff"
          stroke-width="2"
        />
        <Path
          id="secondary-2"
          data-name="secondary"
          d="M9,14.132l2.132,2.132L15.4,12"
          transform="translate(0.264 0.594)"
          fill="none"
          stroke="#66bcff"
          stroke-width="2"
        />
      </G>
    </Svg>
  );
};
