import React from 'react';
import {renderWithProviders} from '@src/utils/utils-for-tests';
import ListNotifications from '@src/containers/ListNotifications';

describe('List Notifications', () => {
  it('should render  and match the snapshot', () => {
    const toggleDrawer = jest.fn();
    const handleDeleteNotification = jest.fn();
    const handleSetNotifications = jest.fn();
    const handleMarkAsRead = jest.fn();
    const handleOpenModal = jest.fn();
    const handleRefreshNotificationsPage = jest.fn();
    const tree = renderWithProviders(
      <ListNotifications
        notifications={[]}
        toggleDrawer={toggleDrawer}
        handleDeleteNotification={handleDeleteNotification}
        handleSetNotifications={handleSetNotifications}
        handleMarkAsRead={handleMarkAsRead}
        handleOpenModal={handleOpenModal}
        handleRefreshNotificationsPage={handleRefreshNotificationsPage}
      />,
      {},
    );
    expect(tree).toMatchSnapshot();
  });
});
