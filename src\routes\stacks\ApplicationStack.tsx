import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { cleanUp } from "@src/store/notification/slice";
import { notificationSelector } from "@src/store/notification/selectors";
import RNEncryptedStorage from "react-native-encrypted-storage";
import { setFcmToken, setUser } from "@src/store/authentication/slice";
import { DrawerStack } from "./DrawerStack";

/**
 * AppStack component manages the main application stack navigation.
 * It handles resetting the stack and actions when the app is opened from a notification.
 */
export const AppStack = () => {
  const { isOpenedFromNotification, notification } = useSelector(notificationSelector);

  const dispatch = useDispatch();

  // Effect to handle navigation when opened from notification
  useEffect(() => {
    if (isOpenedFromNotification && notification) {
      // Import NavigationService dynamically to avoid circular dependencies
      import("@src/services/NavigationService").then(({ default: NavigationService }) => {
        // Use NavigationService to handle the navigation
        NavigationService.navigateToNotification(notification);
      });
    }

    // Cleanup function
    return () => {
      dispatch(cleanUp());
    };
  }, [isOpenedFromNotification, notification, dispatch]);

  const getFCMToken = async () => {
    const fcmToken = await RNEncryptedStorage.getItem("fcmToken");
    if (fcmToken) {
      dispatch(setFcmToken({ fireBaseToken: fcmToken }));
    }
  };

  // TODO implement the get user connected feature here !

  const getUser = () => {
    dispatch(setUser());
  };

  useEffect(() => {
    getFCMToken();
    getUser();
  }, []);

  // Use the DrawerStack for main navigation
  return <DrawerStack />;
};
