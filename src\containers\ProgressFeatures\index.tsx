import { View } from "react-native";
import { ObjectUtils } from "@src/utils/objectUtils";
import React from "react";
import {
  COLLAB_FEATURES_TITLES,
  COLLAB_SUMMARY_FEATURE_KEY_WITH_FEATURES_CODE,
  getSectionValueByKey
} from "@src/constants/channels/features";
import { useSelector } from "react-redux";
import { progressSelector } from "@src/store/channels/progress/selectors";
import { ChannelFeaturesDefaultValues } from "@src/constants/channelFeaturesDefaultValues";
import { KEYS_CHANNEL } from "@src/constants/channels/modules";
import Loader from "@src/components/Loader";
import FeatureContainer, { renderFeatureContainers } from "@src/containers/FeatureContainer";

const ProgressFeatures = () => {
  const { features, loading } = useSelector(progressSelector);

  function renderDefaultValues() {
    const progressFlowFeatures: FeatureContainer[] = [
      {
        featureKey: null,
        featureTitle: COLLAB_FEATURES_TITLES.PROGRESS.planned,
        defaultFeatureValue: ChannelFeaturesDefaultValues.PROGRESS.planned
      },
      {
        featureKey: null,
        featureTitle: COLLAB_FEATURES_TITLES.PROGRESS.actual,
        defaultFeatureValue: ChannelFeaturesDefaultValues.PROGRESS.actual
      }
    ];
    return renderFeatureContainers(progressFlowFeatures);
  }

  if (loading) {
    return <Loader show={true} />;
  }

  return (
    <View>
      {features
        ? ObjectUtils.convertObjectToKeyValuesArray(features).map((item) => {
          if (
            item.key !== KEYS_CHANNEL.COMMON.CHANNEL_STATUS
          ) {
            const featureKey =
              getSectionValueByKey(COLLAB_SUMMARY_FEATURE_KEY_WITH_FEATURES_CODE, "PROGRESS", item.key) || null;
            const featureTitle =
              getSectionValueByKey(COLLAB_FEATURES_TITLES, "PROGRESS", item.key) || null;

            return (
              <FeatureContainer
                key={item.key}
                featureKey={featureKey !== undefined ? featureKey : null}
                featureTitle={featureTitle}
                featureValue={item.value}
                defaultFeatureValue={
                  getSectionValueByKey(ChannelFeaturesDefaultValues, "PROGRESS", item.key)
                }
              />
            );

          }
          return null;
        })
        : renderDefaultValues()}
    </View>
  );

};

export default ProgressFeatures;
