import React from 'react';
import {<PERSON><PERSON>, <PERSON>dal, Text, View} from 'react-native';
import {styles} from './style';
import {BlurView} from '@react-native-community/blur';
import analytics from '@react-native-firebase/analytics';
import {TEST_IDS} from '@src/constants/strings';

const NotAuthenticatedModal = (props: {show: boolean}) => {
  const {show} = props;
  const handleReconnectButton = async () => {
    //await AuthManager.signInAsync();
    await analytics().logEvent('basket', {
      id: 3745092,
      item: 'mens grey t-shirt',
      description: ['round neck', 'long sleeved'],
      size: 'L',
    });
  };
  return (
    <Modal transparent visible={show} animationType="fade">
      <View style={styles.container} testID={TEST_IDS.VIEW_NOT_AUTHENTICATED}>
        <BlurView style={styles.blurContainer} />
        <View style={styles.content}>
          <Text style={styles.errorText}>Oops, something went wrong!</Text>
          <Text style={styles.errorMessage}>
            {'Check Your authentication !'}
          </Text>
          <Button
            title={'Click here to reconnect '}
            onPress={handleReconnectButton}
          />
        </View>
      </View>
    </Modal>
  );
};

export default NotAuthenticatedModal;
