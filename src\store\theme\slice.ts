/*import {createSlice} from '@reduxjs/toolkit';
import {useColorScheme} from 'react-native';
import {SLICES_NAMES} from '@src/constants/store';

const slice = createSlice({
  name: SLICES_NAMES.THEME_SLICE,
  initialState: {
    // Here I specify a string because maybe in the future we might have more than two themes : light and dark and blue-dark ..
    // by default our device will use the default theme used in it
    themeMode: useColorScheme,
  },
  reducers: {
    switchThemeMode: state => ({
      ...state,
      value: state.themeMode,
    }),
  },
});

export const {switchThemeMode} = slice.actions;
export default slice.reducer;*/
