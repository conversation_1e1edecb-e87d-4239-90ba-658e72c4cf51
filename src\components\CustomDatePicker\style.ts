import {StyleSheet} from 'react-native';
import {COMMON_COLORS} from '@src/theme/colors';
import {correspondentHeight, correspondentWidth} from '@src/utils/imageUtils';

export const SELECTED_TEXT_WHEEL_PICKER = COMMON_COLORS.BLACK;
export const style = StyleSheet.create({
  pickerContainer: {
    width: correspondentHeight(148),
    marginHorizontal: correspondentWidth(12),
    backgroundColor: 'transparent',
  },
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    maxHeight: correspondentHeight(238),
  },
});
