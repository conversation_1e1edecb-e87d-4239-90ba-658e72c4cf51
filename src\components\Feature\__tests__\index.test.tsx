import React from 'react';
import {renderWithProviders} from '@src/utils/utils-for-tests';
import Feature from '@src/components/Feature';
import {View} from 'react-native';

describe('Feature', () => {
  it('Should render and match the snapshot', () => {
    const tree = renderWithProviders(
      <Feature value={<View />} label={'label'} labelStyle={{color: 'red'}} />,
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
