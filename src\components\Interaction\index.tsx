import React from "react";
import { Image, Text, TouchableOpacity, View } from "react-native";
import { IMAGES } from "@src/constants/images";
import styles from "./style";
import { extractUserRegex, isEmailRegex, pattern, regexExtractUserTagged } from "@src/constants/reegexes";
import useGetUserAvatar from "@src/hooks/useGetUserAvatar";
import { returnFinalBase64String } from "@src/utils/imageUtils";
import { windowWidth } from "@src/constants";
import { COMMON_COLORS } from "@src/theme/colors";

type InteractionProps = {
  time: string;
  content: string;
  creator: string;
  isParentComment: boolean;
  userId: string;
  handleClickInteraction: () => void;
};

const Interaction = (props: InteractionProps) => {
  const {
    creator,
    time,
    content,
    isParentComment,
    handleClickInteraction,
    userId
  } = props;
  let containerStyle = {};
  if (!isParentComment) {
    containerStyle = styles.childInteraction;
  }

  const renderReplyText = () => {
    if (!isParentComment) {
      return (
        <TouchableOpacity onPress={handleClickInteraction}>
          <Text style={styles.replyText}>Reply</Text>
        </TouchableOpacity>
      );
    } else {
      return null;
    }
  };

  function formatComment(comment: string) {
    const parts = comment.split(pattern);
    return parts.map((part, index) => {
      if (part.startsWith("@")) {
        const matches = part.match(regexExtractUserTagged);
        let mention = "@";
        if (matches && matches[1]) {
          const match = matches[1].match(isEmailRegex);
          if (match && match[0]) {
            const userFromEmail = match[0].match(extractUserRegex);
            if (userFromEmail) {
              mention += userFromEmail[0] + " ";
            }
          } else {
            mention += matches[1] + " ";
          }
        }
        return (
          <Text key={index} style={{ color: "blue" }}>
            {mention}
          </Text>
        );
      } else {
        return <Text key={index}>{part}</Text>;
      }
    });
  }

  const userAvatarFromHook = useGetUserAvatar({ userIdProps: userId });

  return (
    <View style={{ ...styles.container, ...containerStyle }}>
      <View style={styles.iconTitleTimeNotificationContainer}>
        <View style={styles.leftContainer}>
          <View style={styles.iconContainer}>
            {userAvatarFromHook.length > 0 ? (
              <Image
                style={styles.titleIcon}
                source={{ uri: returnFinalBase64String(userAvatarFromHook) }}
              />
            ) : (
              <Image style={styles.titleIcon} source={IMAGES.USER_AVATAR} />
            )}
          </View>
          <View style={styles.createCommentContainer}>
            <Text style={styles.creator}>{creator} </Text>
          </View>
        </View>
        <Text style={{
          maxWidth: windowWidth * 0.25,
          color: COMMON_COLORS.BLUE_20
        }}>
          {time}
        </Text>
      </View>
      <View style={styles.contentAndBottomCommentContainer}>
        <View style={styles.contentContainer}>
          <Text style={styles.content}>{formatComment(content)}</Text>
        </View>
        <View style={styles.replyTextContainer}>
          <View style={styles.commentBottom}>
            <>{renderReplyText()}</>
          </View>
        </View>
      </View>
    </View>
  );
};

export default Interaction;
