import { Text, View } from "react-native";
import React, { useMemo } from "react";
import style from "./style";
import { CustomLinearGradient } from "@src/components/CustomLinearGradient";
import { LABELS } from "@src/constants/strings";
import { windowHeight } from "@src/constants";
import { CHANNEL_STATUS_VALUE, LINEAR_COLORS_FOR_EACH_STATUS_COLOR } from "@src/constants/channels/channelStatusColor";
import { Project } from "@src/models/project";
import { Gesture, GestureDetector } from "react-native-gesture-handler";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  useDerivedValue,
  interpolate,
  Extrapolate,
  runOnJS
} from "react-native-reanimated";
import { COMMON_COLORS } from "@src/theme/colors";

type OverviewProjectProps = {
  toggleDrawer: () => void;
  currentProject: Project;
  period: {
    year: number;
    month: number;
  };
};
const OverviewProject = (props: OverviewProjectProps) => {
  // Constants for gradient configurations - memoized to avoid recalculation
  const GRADIENT_CONFIG = {
    status: {
      x_start: 0,
      x_end: 0,
      y_start: 0,
      y_end: 0
    },
    content: {
      x_start: 0,
      x_end: 1,
      y_start: 0.5,
      y_end: 0.5
    },
    colors: {
      primary: COMMON_COLORS.PRIMARY,
      secondary: COMMON_COLORS.SECONDARY
    }
  };

  // Animation constants
  const INITIAL_HEIGHT = windowHeight * 0.075;
  const CHILD_HEIGHT = windowHeight * 0.048;

  const { currentProject, period } = props;
  const { name, number, projectSize, program, bu, overallStatus } = currentProject;

  // Create project elements array
  const convertedPeriod = `${period.year}/${period.month}`;
  const elements = useMemo(() => [
    { label: LABELS.PROJECT_SIZE, value: projectSize },
    { label: LABELS.BUSINESS_UNIT, value: bu },
    { label: LABELS.PROGRAM, value: program },
    { label: LABELS.PERIOD, value: convertedPeriod }
  ], [projectSize, bu, program, convertedPeriod]);

  // Calculate heights based on elements
  const PROJECT_PROPERTIES_NUMBER = elements.length;
  const MAX_OVERVIEW_PROJECT_HEIGHT = PROJECT_PROPERTIES_NUMBER * CHILD_HEIGHT + INITIAL_HEIGHT;
  const THRESHOLD = INITIAL_HEIGHT + (MAX_OVERVIEW_PROJECT_HEIGHT - INITIAL_HEIGHT) * 0.3;

  // Get status values
  const overAllStatusValue = CHANNEL_STATUS_VALUE[overallStatus];
  const overAllStatusColors = LINEAR_COLORS_FOR_EACH_STATUS_COLOR[overallStatus];

  // Animation configuration
  const SPRING_CONFIG = {
    damping: 15,      // Lower damping for faster response
    stiffness: 100,   // Lower stiffness for smoother animation
    mass: 0.5,        // Lower mass for faster movement
    overshootClamping: false,
    restDisplacementThreshold: 0.01,
    restSpeedThreshold: 0.01
  };

  // Shared values for animation
  const animationHeight = useSharedValue(INITIAL_HEIGHT);
  const isExpanded = useSharedValue(false);

  // Derived values for additional animations
  const contentOpacity = useDerivedValue(() => {
    return interpolate(
      animationHeight.value,
      [INITIAL_HEIGHT, INITIAL_HEIGHT + (MAX_OVERVIEW_PROJECT_HEIGHT - INITIAL_HEIGHT) * 0.5],
      [0, 1],
      Extrapolate.CLAMP
    );
  });

  // Render project property item
  const renderProjectProperty = (label: string, value: string | undefined) => (
    <View style={style.rowAndJustifySpaceAndAlignCenter}>
      <Text style={style.label}>{label}</Text>
      <Text style={style.value}>{value}</Text>
    </View>
  );

  // Pre-render elements for better performance
  const renderedElements = useMemo(() =>
    elements.map((element, index) => (
      <View key={index}>
        {renderProjectProperty(element.label, element.value)}
      </View>
    )), [elements]);

  // Pan gesture handler with optimized logic
  const panGesture = Gesture.Pan()
    .onChange((event) => {
      // Get current translation and velocity
      const translationY = event.translationY;
      const velocityY = event.velocityY;

      // Determine direction and calculate new height
      if (velocityY > 0) { // Expanding (scrolling down)
        // Use direct calculation with clamping for better performance
        animationHeight.value = Math.min(
          INITIAL_HEIGHT + translationY,
          MAX_OVERVIEW_PROJECT_HEIGHT
        );
      } else { // Collapsing (scrolling up)
        // Use direct calculation with clamping for better performance
        animationHeight.value = Math.max(
          MAX_OVERVIEW_PROJECT_HEIGHT + translationY,
          INITIAL_HEIGHT
        );
      }
    })
    .onEnd((event) => {
      // Determine final state based on velocity and position
      const shouldExpand =
        event.velocityY > 500 || // Fast downward swipe
        (animationHeight.value > THRESHOLD && event.velocityY >= 0) || // Slow downward past threshold
        (animationHeight.value > MAX_OVERVIEW_PROJECT_HEIGHT - THRESHOLD && event.velocityY < 0); // Slow upward but close to max

      // Update expanded state
      isExpanded.value = shouldExpand;

      // Animate to final position with spring physics
      animationHeight.value = withSpring(
        shouldExpand ? MAX_OVERVIEW_PROJECT_HEIGHT : INITIAL_HEIGHT,
        SPRING_CONFIG
      );
    });

  // Animated styles
  const containerAnimatedStyle = useAnimatedStyle(() => ({
    height: animationHeight.value
  }));

  const contentAnimatedStyle = useAnimatedStyle(() => ({
    opacity: contentOpacity.value
  }));

  return (
    <View>
      <GestureDetector gesture={panGesture}>
        <Animated.View>
          {/* Header section */}
          <Animated.View style={style.topContainer}>
            <Animated.View style={style.titleAndStatusProjectContainer}>
              <Text style={style.title} numberOfLines={1}>
                {name ?? ""}
              </Text>
              <CustomLinearGradient
                firstColor={overAllStatusColors.firstColor}
                secondColor={overAllStatusColors.secondColor}
                x_start={GRADIENT_CONFIG.status.x_start}
                y_start={GRADIENT_CONFIG.status.y_start}
                x_end={GRADIENT_CONFIG.status.x_end}
                y_end={GRADIENT_CONFIG.status.y_end}
                style={style.status}>
                <Text style={style.statusText}>{overAllStatusValue}</Text>
              </CustomLinearGradient>
            </Animated.View>
            <Text style={style.reference}>{number}</Text>
          </Animated.View>

          {/* Expandable content section */}
          <Animated.View style={[style.collapsedView, containerAnimatedStyle]}>
            <CustomLinearGradient
              firstColor={GRADIENT_CONFIG.colors.primary}
              secondColor={GRADIENT_CONFIG.colors.secondary}
              x_start={GRADIENT_CONFIG.content.x_start}
              y_start={GRADIENT_CONFIG.content.y_start}
              x_end={GRADIENT_CONFIG.content.x_end}
              y_end={GRADIENT_CONFIG.content.y_end}
              style={style.bottomContainer}>
              <Animated.View style={contentAnimatedStyle}>
                {renderedElements}
              </Animated.View>
              <View style={style.bottomLine} />
            </CustomLinearGradient>
          </Animated.View>
        </Animated.View>
      </GestureDetector>
    </View>
  );
};

export default OverviewProject;
