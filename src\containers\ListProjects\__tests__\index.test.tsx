import React from "react";
import { renderWithProviders } from "@src/utils/utils-for-tests";
import { NavigationContainer } from "@react-navigation/native";
import ListProjects from "@src/containers/ListProjects";

describe("List Projects", () => {
  it("should render  and match the snapshot", () => {
    const handleRefreshPage = jest.fn();
    const updateCurrentPageProjects = jest.fn();
    const tree = renderWithProviders(
      <NavigationContainer>
        <ListProjects
          projects={[]}
          handleRefreshPage={handleRefreshPage}
          page={2}
          updateCurrentPageProjects={updateCurrentPageProjects}
          isLastPage={true}
          isLoading={false}
          emptyListMessage={""} />
      </NavigationContainer>,
      {}
    );
    expect(tree).toMatchSnapshot();
  });
});
