import { call, put, select, takeEvery } from "redux-saga/effects";

import { ChannelsWebServices } from "@src/api/channels";
import baseRequest from "@src/api/request";
import {
  getFlattenKpis,
  getFlattenKpisWithError,
  getFlattenKpisWithSuccess,
  getUserProjectWithProjectIdAndUserId,
  getUserProjectWithProjectIdAndUserIdWithSuccess,
  setCurrentProject,
  setCurrentProjectWithError,
  setCurrentProjectWithSuccess
} from "@src/store/project/slice";
import { ProjectWebServices } from "@src/api/project";
import { authenticationSelector } from "@src/store/authentication/selectors";
import { DateUtils } from "@src/utils/dateUtils";

export function* handleProject(action: { payload: number }) {
  try {
    const { data } = yield call(
      baseRequest.get,
      ProjectWebServices.WS_GET_PROJECT_BY_ID(action.payload)
    );
    const reportingDate = DateUtils.extractMonthAndYear(data.reportingDate);
    yield put(
      setCurrentProjectWithSuccess({
        project: data,
        reportingDate: reportingDate
      })
    );
  } catch (e) {
    yield put(setCurrentProjectWithError());
  }
}

export function* handleFlattenKpis(action: { payload: { projectId: number } }) {
  try {
    const projectId = action.payload.projectId;
    const { data } = yield call(
      baseRequest.get,
      ChannelsWebServices.WS_GET_PROJECT_CHANNEL_KPIS(projectId)
    );
    yield put(getFlattenKpisWithSuccess(data.data));
  } catch (e) {
    yield put(getFlattenKpisWithError());
  }
}

export function* handleUserProject(action: { payload: { projectId: number } }) {
  try {
    const { user } = yield select(authenticationSelector);
    const { projectId } = action.payload;
    const { data } = yield call(
      baseRequest.get,
      ProjectWebServices.WS_GET_USER_PROJECT_BY_USER_ID_AND_PROJECT_ID(
        user.avatar,
        projectId
      )
    );
    yield put(
      getUserProjectWithProjectIdAndUserIdWithSuccess({
        profile: data.profile,
        role: data.role,
        featurePrivileges: data.featurePrivilegeList
      })
    );
  } catch (e) {
    yield put(getUserProjectWithProjectIdAndUserIdWithSuccess({
      featurePrivileges: [],
      role: null,
      profile: null
    }));
  }
}

export default function* saga() {
  yield takeEvery(setCurrentProject, handleProject);
  yield takeEvery(getFlattenKpis, handleFlattenKpis);
  yield takeEvery(getUserProjectWithProjectIdAndUserId, handleUserProject);
}
