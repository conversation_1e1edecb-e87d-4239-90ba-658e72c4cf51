import React, {Component, PropsWithChildren} from 'react';
import {Animated, I18n<PERSON>anager, StyleSheet, View} from 'react-native';

import {RectButton} from 'react-native-gesture-handler';

import Swipeable from 'react-native-gesture-handler/Swipeable';
import {COMMON_COLORS} from '@src/theme/colors';
import {PERCENTAGES} from '@src/constants';
import {Trash} from '@src/svg/Trash';

const AnimatedView = Animated.createAnimatedComponent(View);

export default class SwipeableRow extends Component<
  PropsWithChildren<unknown & {onDelete: () => void}>
> {
  private swipeableRow?: Swipeable;

  render() {
    const {children} = this.props;
    return (
      <Swipeable
        ref={this.updateRef}
        friction={2}
        leftThreshold={80}
        enableTrackpadTwoFingerGesture
        rightThreshold={40}
        renderRightActions={this.renderRightActions}>
        {children}
      </Swipeable>
    );
  }

  private renderRightActions = (
    _progress: Animated.AnimatedInterpolation<number>,
    dragX: Animated.AnimatedInterpolation<number>,
  ) => {
    const scale = dragX.interpolate({
      inputRange: [-80, 0],
      outputRange: [1, 0],
      extrapolate: 'clamp',
    });
    return (
      <RectButton style={styles.rightAction} onPress={this.close}>
        <AnimatedView style={[{transform: [{scale}]}]}>
          <Trash />
        </AnimatedView>
      </RectButton>
    );
  };

  private updateRef = (ref: Swipeable) => {
    this.swipeableRow = ref;
  };

  private close = () => {
    this.swipeableRow?.close();
    this.props.onDelete();
  };
}

const styles = StyleSheet.create({
  rightAction: {
    backgroundColor: COMMON_COLORS.RED,
    width: PERCENTAGES['20'],
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
