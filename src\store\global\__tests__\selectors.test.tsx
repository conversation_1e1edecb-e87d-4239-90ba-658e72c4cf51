import {RootState} from '@src/store/store';
import {GlobalSliceType} from '@src/store/global/slice';
import {lightTheme} from '@src/theme';
import {globalSelector} from '@src/store/global/selectors';

describe('globalSelector', () => {
  it('should select the global state from the root state', () => {
    // Arrange
    const globalState: GlobalSliceType = {
      isInternetAvailable: false,
      discussionId: null,
      isApplicationVersionUpdated: false,
      theme: lightTheme,
    };

    // @ts-ignore
    const mockRootState: RootState = {
      global: globalState,
    };

    // Act
    const selectedState = globalSelector(mockRootState);

    // Assert
    expect(selectedState).toEqual(globalState);
  });
});
