import { View } from "react-native";
import { styles } from "@src/screens/DiscussionDetailsScreen/style";
import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { cleanUp, getDiscussionDetailsByObjectTypeAndObjectId, getDiscussionUsers } from "@src/store/discussion/slice";
import { ROUTE_NAMES } from "@src/constants/navigation";
import { discussionSelector } from "@src/store/discussion/selectors";
import DiscussionSummary from "src/components/DiscussionSummary";
import { ListInteractionsWithAddCommentInput } from "@src/containers/ListInteractionsWithAddCommentInput";
import useScreenFocusAndBlurListener from "@src/hooks/useScreenFocusListener";
import { notificationSelector } from "@src/store/notification/selectors";
import { getNotificationsExistence } from "@src/store/notifications/slice";
import { projectSelector } from "@src/store/project/selectors";
import { MAPPING_NOTIFICATION_OBJECT_TYPE_WITH_DISCUSSION_OBJECT_TYPE } from "@src/constants/notification";
import { globalStyles } from "@src/theme/style";
import NotificationsIconHeaderContainer from "@src/containers/headers/NotificationsIconHeaderContainer";
import ClickableImage from "@src/components/ClickableImage";
import { MenuDrawer } from "@src/svg/MenuDrawer";
import Header from "@src/components/Header";
import ToastMessage from "src/components/Toast";
import { ActivityIndicatorLoader } from "@src/components/ActivityIndicatorLoader";

type ListDiscussionInteractionsWithHeaderContainerProps = {
  toggleDrawer: () => void;
  useRouteHooks: any;
};
export const ListDiscussionInteractionsWithHeaderContainer = ({
                                                                toggleDrawer,
                                                                useRouteHooks
                                                              }: ListDiscussionInteractionsWithHeaderContainerProps) => {
  const { discussion, error, loading, errorUsers, users, successCreateUpdate } =
    useSelector(discussionSelector);

  // Extract route parameters
  const { params } = useRouteHooks;

  const { notification } = useSelector(notificationSelector);
  const dispatch = useDispatch();

  // Determine object type and ID with proper fallbacks
  // Priority: Route params > Notification state > Defaults
  const objectType: string = params?.objectType || notification?.objectType || '';
  const objectId: number = params?.objectId || notification?.objectId || 0;

  // Use drawer status from React Navigation
  const isDrawerOpen = false;

  // Load discussion data when component mounts or when key dependencies change
  useEffect(() => {
    if (
      objectType &&
      objectId &&
      !isDrawerOpen
    ) {
      // Map the object type to the correct discussion object type
      const objectTypeMapped =
        MAPPING_NOTIFICATION_OBJECT_TYPE_WITH_DISCUSSION_OBJECT_TYPE[objectType] || objectType;

      // Fetch discussion details and users in parallel
      dispatch(
        getDiscussionDetailsByObjectTypeAndObjectId({
          objectType: objectTypeMapped,
          objectId
        })
      );

      if (projectId) {
        dispatch(getDiscussionUsers({ idProject: projectId }));
      }
    }
  }, [objectType, objectId, isDrawerOpen]);

  // Refresh data when a comment is successfully created or updated
  useEffect(() => {
    if (successCreateUpdate) {
      // Map the object type to the correct discussion object type
      const objectTypeMapped =
        MAPPING_NOTIFICATION_OBJECT_TYPE_WITH_DISCUSSION_OBJECT_TYPE[objectType] || objectType;

      // Clean up the success state
      dispatch(cleanUp());

      // Refresh discussion details and users
      dispatch(
        getDiscussionDetailsByObjectTypeAndObjectId({
          objectType: objectTypeMapped,
          objectId
        })
      );

      if (projectId) {
        dispatch(getDiscussionUsers({ idProject: projectId }));
      }
    }
  }, [successCreateUpdate, objectType, objectId]);

  const { projectId } = useSelector(projectSelector);

  useScreenFocusAndBlurListener(
    [
      {
        action: projectId !== null ? getNotificationsExistence : null,
        params: { projectId }
      }
    ],
    [{ action: cleanUp }]
  );

  return (
    <View style={styles.container}>
      <Header
        routeName={ROUTE_NAMES.DISCUSSION_DETAILS}
        rightContent={
          <View style={globalStyles.iconsContainer}>
            <NotificationsIconHeaderContainer projectId={projectId!!} />
            <ClickableImage
              SvgComponent={<MenuDrawer style={globalStyles.drawerLogoImage} />}
              onPress={toggleDrawer}
              url={null}
              imageStyle={globalStyles.notificationLogoImage}
            />
          </View>
        }
        showBackButton={true}
      />
      {discussion && (
        <>
          <DiscussionSummary />
          <View style={styles.divider} />
          <ListInteractionsWithAddCommentInput
            objectId={objectId}
            discussion={discussion}
            objectTypeMapped={objectType}
          />
        </>
      )}
      <ActivityIndicatorLoader error={loading} />
      {(error || errorUsers) && <ToastMessage message={"Error While Fetching Discussion"} type={"error"} />}
    </View>
  );
};
