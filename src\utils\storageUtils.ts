import RNEncryptedStorage from 'react-native-encrypted-storage';

export async function getItem(key: string): Promise<any> {
  const value = await RNEncryptedStorage.getItem(key);
  return value || null;
}

export async function setItem(key: string, value: any) {
  await RNEncryptedStorage.setItem(key, value);
}

export async function removeItem(key: string) {
  await RNEncryptedStorage.removeItem(key);
}
