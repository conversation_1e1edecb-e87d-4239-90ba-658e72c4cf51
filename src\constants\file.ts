export const IMAGE_TYPE = {
  AVATAR: 'image',
};

export const FILE_TYPE = {
  FILE: 'file',
};

export const FILE_SIZE_ERROR = {
  logref: 'FileContentErrorException',
  message: 'Max File size is 5 Mb',
  links: [],
};

export const FILE_SIZE_50_ERROR = {
  logref: 'FileContentErrorException',
  message: 'Max File size is 50 Mb',
  links: [],
};

export const FILE_EXTENSION_ERROR = {
  message: 'Bad file extension',
};

export const documentsExtension = ['pdf', 'jpeg', 'jpg', 'png'];
export const EXCEL_TYPE = `"xlsx", "xls", "xltx"`;
export const ZIP_TYPE = 'zip';

export const FILE_IS_REQUIRED = {
  logref: 'FileContentErrorException',
  message: 'File is Required',
  links: [],
};
