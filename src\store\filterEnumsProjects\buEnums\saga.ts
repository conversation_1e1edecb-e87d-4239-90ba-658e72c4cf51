import {call, put, takeEvery} from 'redux-saga/effects';

import {
  getBuEnums,
  getBuEnumsWithError,
  getBuEnumsWithSuccess,
} from '@src/store/filterEnumsProjects/buEnums/slice';
import baseRequest from '@src/api/request';
import {ProjectWebServices} from '@src/api/project';

export function* handleBuEnums(action: {
  payload: {
    types: string;
  };
}) {
  try {
    const {types} = action.payload;
    const {data} = yield call(
      baseRequest.get,
      ProjectWebServices.WS_GET_ENUMS_BY_TYPE,
      {params: {types}},
    );
    yield put(getBuEnumsWithSuccess(data.BU));
  } catch (e) {
    yield getBuEnumsWithError();
  }
}

export default function* saga() {
  yield takeEvery(getBuEnums, handleBuEnums);
}
