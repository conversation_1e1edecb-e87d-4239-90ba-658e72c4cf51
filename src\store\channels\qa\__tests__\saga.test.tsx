import {call} from 'redux-saga/effects';
import {expectSaga} from 'redux-saga-test-plan';
import baseRequest from '@src/api/request';
import {ChannelsWebServices} from '@src/api/channels';
import {handleQaFeatures} from '@src/store/channels/qa/saga';
import {QualityChannelSummary} from '@src/models/channelsSummary/qualityChannelSummary';
import {
  getQAFeaturesWithError,
  getQAFeaturesWithSuccess,
} from '@src/store/channels/qa/slice';

describe('qa saga', () => {
  const projectId = 22;
  const reportingDateFilter = {};
  const features: QualityChannelSummary = {
    channelStatus: 'PRIMARY',
  };
  it('should yield an api call', () => {
    expectSaga(handleQaFeatures)
      .provide([
        [
          call(
            baseRequest.get,
            ChannelsWebServices.WS_GET_QUALITY_SUMMARY(projectId),
          ),
          {params: reportingDateFilter},
        ],
      ])
      .run();
  });

  it('should handle qa features successfully', () => {
    expectSaga(handleQaFeatures)
      .provide([
        [
          call(
            baseRequest.get,
            ChannelsWebServices.WS_GET_QUALITY_SUMMARY(projectId),
          ),
          {params: reportingDateFilter},
        ],
      ])
      .put(getQAFeaturesWithSuccess({features}))
      .run();
  });

  it('should handle qa features with error', () => {
    expectSaga(handleQaFeatures)
      .provide([
        [
          call(
            baseRequest.get,
            ChannelsWebServices.WS_GET_QUALITY_SUMMARY(projectId),
          ),
          {throw: true},
        ],
      ])
      .put(getQAFeaturesWithError())
      .run();
  });
});
