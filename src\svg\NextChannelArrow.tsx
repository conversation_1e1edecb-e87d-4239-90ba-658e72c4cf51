import React from 'react';
import {G, Path, Svg} from 'react-native-svg';

const NextChannelArrow = (props: {style: {}}) => (
  <Svg
    style={props.style}
    width="9.257"
    height="17.047"
    viewBox="0 0 9.257 17.047">
    <G
      id="style_doutone"
      data-name="style=doutone"
      transform="translate(-14.349 -3.937)">
      <G id="arrow-long-right" transform="translate(1.95 4.25)">
        <Path
          id="vector_Stroke__2"
          data-name="vector (Stroke)_2"
          d="M14.183,4.483a.8.8,0,0,0,0,1.124l6.666,6.666a.265.265,0,0,1,0,.375l-6.666,6.666a.795.795,0,1,0,1.124,1.124l6.666-6.666a1.854,1.854,0,0,0,0-2.622L15.306,4.483A.8.8,0,0,0,14.183,4.483Z"
          transform="translate(-1.238 -4.25)"
          fill="#003493"
          stroke="#003493"
          stroke-width="0.5"
          fill-rule="evenodd"
        />
      </G>
    </G>
  </Svg>
);

export default NextChannelArrow;
