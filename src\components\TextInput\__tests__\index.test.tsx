import {renderWithProviders} from '@src/utils/utils-for-tests';
import React from 'react';
import InputText from '@src/components/TextInput';

describe('InputText', function () {
  const onChangeText = jest.fn();
  it('Should render and match the snapshot', () => {
    const tree = renderWithProviders(
      <InputText
        placeholder={''}
        label={''}
        value={undefined}
        onChangeText={onChangeText}
      />,
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
