import React from "react";
import { CPI } from "@src/components/CPI";
import { Forecast } from "@src/components/Forecast";
import { RevisedBudget } from "@src/components/RevisedBudget";
import { DSO } from "@src/components/DSO";
import { PassDueContractPayment } from "@src/components/PassDueContractPayment";
import { PassDueJESAPayment } from "@src/components/PassDueJESAPayment";
import { useFeatureVisibility } from "@src/hooks/feature/UseProjectPrivilegeByFeature";
import Feature from "@src/components/Feature";
import { View } from "react-native";
import { COLLAB_FEATURES_TITLES } from "@src/constants/channels/features";
import { TRCFR } from "@src/components/TRCFR";
import { ForecastSOP } from "@src/components/ForecastSOP";
import { PlannedSOP } from "@src/components/PlannedSOP";
import { SPI } from "@src/components/SPI";
import { AverageOverdueDays, NumbersOfOverdueCAPAS, Satisfaction } from "@src/containers/QualityAssuranceFeatures";
import { Actual } from "@src/components/Actual";
import { Planned } from "@src/components/Planned";
import { HeadCountContractor } from "@src/components/HeadCountContractor";
import { HeadCountJesa } from "@src/components/HeadCountJesa";
import { CriticalRisk } from "@src/components/CriticalRisk";
import { Evolution } from "@src/components/Evolution";
import { PendingContractorChange } from "@src/components/PendingContractorCange";
import { PendingJesaChange } from "@src/components/PendingJesaChange";

interface FeatureContainer {
  // NULL : the feature will be displayed in the summary
  featureKey: string | null;
  featureTitle: string;
  featureValue?: any;
  defaultFeatureValue?: any;
}

const FeatureContainer: React.FC<FeatureContainer> = ({
                                                        featureKey,
                                                        featureTitle,
                                                        featureValue,
                                                        defaultFeatureValue
                                                      }) => {
  const isVisible = useFeatureVisibility(featureKey);

  if (!isVisible) return null;
  let featureComponent;
  let finalFeatureValue = featureValue ?? defaultFeatureValue;


  switch (featureTitle) {
    // COST
    case COLLAB_FEATURES_TITLES.COST.cpi:
      featureComponent = <CPI value={finalFeatureValue} />;
      break;
    case COLLAB_FEATURES_TITLES.COST.forecast:
      featureComponent = <Forecast value={finalFeatureValue} />;
      break;
    case COLLAB_FEATURES_TITLES.COST.revisedBudgetTic:
      featureComponent = <RevisedBudget value={finalFeatureValue} />;
      break;

    // CASH FLOW
    case COLLAB_FEATURES_TITLES.CASH_FLOW.dso:
      featureComponent = <DSO value={finalFeatureValue} />;
      break;
    case COLLAB_FEATURES_TITLES.CASH_FLOW.passDueJesaPayment:
      featureComponent =
        <PassDueContractPayment value={finalFeatureValue} />;
      break;
    case COLLAB_FEATURES_TITLES.CASH_FLOW.passDueContractorPayment:
      featureComponent = <PassDueJESAPayment value={finalFeatureValue} />;
      break;

    // HSE
    case COLLAB_FEATURES_TITLES.HSE.trir:
      featureComponent = <TRCFR value={finalFeatureValue} />;
      break;

    case COLLAB_FEATURES_TITLES.HSE.openSor:
      featureComponent = <TRCFR value={finalFeatureValue} />;
      break;

    // SCHEDULING
    case COLLAB_FEATURES_TITLES.SCHEDULING.spi:
      featureComponent = <SPI value={finalFeatureValue} />;
      break;

    case COLLAB_FEATURES_TITLES.SCHEDULING.plannedSop:
      featureComponent = <PlannedSOP value={finalFeatureValue} />;
      break;

    case COLLAB_FEATURES_TITLES.SCHEDULING.forecastSop:
      featureComponent = <ForecastSOP value={finalFeatureValue} />;
      break;

    // Quality Assurance
    case COLLAB_FEATURES_TITLES.QA.clientSatisfaction:
      featureComponent = <Satisfaction value={finalFeatureValue} />;
      break;

    case COLLAB_FEATURES_TITLES.QA.averageOpenDays:
      featureComponent = <AverageOverdueDays value={finalFeatureValue} />;
      break;

    case COLLAB_FEATURES_TITLES.QA.overduePinsNumber:
      featureComponent = <NumbersOfOverdueCAPAS value={finalFeatureValue} />;
      break;

    // Progress
    case COLLAB_FEATURES_TITLES.PROGRESS.actual:
      featureComponent = <Actual value={finalFeatureValue} />;
      break;

    case COLLAB_FEATURES_TITLES.PROGRESS.planned:
      featureComponent = <Planned value={finalFeatureValue} />;
      break;

    // Human Capital
    case COLLAB_FEATURES_TITLES.HUMAN_CAPITAL.jesaHeadCount:
      featureComponent = <HeadCountJesa value={finalFeatureValue} />;
      break;
    case COLLAB_FEATURES_TITLES.HUMAN_CAPITAL.contractorHeadCount:
      featureComponent = <HeadCountContractor value={finalFeatureValue} />;
      break;

    // RISK - RISK_BOARD
    case COLLAB_FEATURES_TITLES.RISK.criticalRisks:
      featureComponent = <CriticalRisk value={finalFeatureValue} />;
      break;

    // RISK - RISK_MATRIX_EVOL
    case COLLAB_FEATURES_TITLES.RISK.evolution:
      featureComponent = <Evolution value={finalFeatureValue} />;
      break;

    // Change Management
    case COLLAB_FEATURES_TITLES.CHANGE_MANAGEMENT.pendingJesaChange:
      featureComponent = <PendingJesaChange value={finalFeatureValue} />;
      break;

    case COLLAB_FEATURES_TITLES.CHANGE_MANAGEMENT.pendingContractorChange:
      featureComponent = <PendingContractorChange value={finalFeatureValue} />;
      break;

    default:
      return null;
  }

  return <Feature label={featureTitle} value={featureComponent} />;
};

export default FeatureContainer;

// Define a reusable function to render feature containers
export function renderFeatureContainers(features: FeatureContainer[]) {
  return (
    <View>
      {features.map((item, index) => (
        <FeatureContainer
          key={index}
          featureKey={item.featureKey}
          featureTitle={item.featureTitle}
          featureValue={item.featureValue ?? item.defaultFeatureValue}
        />
      ))}
    </View>
  );
}
