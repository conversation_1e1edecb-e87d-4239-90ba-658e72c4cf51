import { createSlice } from "@reduxjs/toolkit";
import { HseChannelSummary } from "@src/models/channelsSummary/hseChannelSummary";
import { SLICES_NAMES } from "@src/constants/store";

export type HseSliceType = {
  features: HseChannelSummary | null;
  channelStatus: string;
  loading: boolean;
  error: boolean;
  success: boolean;
};

export const initialState: HseSliceType = {
  features: null,
  channelStatus: "PRIMARY",
  error: false,
  loading: false,
  success: false
};
export const slice = createSlice({
  name: SLICES_NAMES.HSE_CHANNEL,
  initialState,
  reducers: {
    getHseFeatures: state => {
      return { ...state, loading: true };
    },
    getHseFeaturesWithSuccess: (
      state,
      action: {
        payload: {
          features: HseChannelSummary;
        };
      }
    ) => {
      let finalChannelStatus: any;
      if (action.payload.features.channelStatus !== null) {
        finalChannelStatus = action.payload.features.channelStatus!!;
      } else {
        finalChannelStatus = "PRIMARY";
      }
      return {
        ...state,
        features: action.payload.features,
        channelStatus: finalChannelStatus,
        error: false,
        loading: false,
        success: true
      };
    },
    getHseFeaturesWithError: state => {
      return {
        ...state,
        features: null,
        channelStatus: "PRIMARY",
        error: true,
        loading: false,
        success: false
      };
    },
    cleanUpHse: () => {
      return initialState;
    }
  }
});

export const {
  getHseFeatures,
  getHseFeaturesWithSuccess,
  getHseFeaturesWithError,
  cleanUpHse
} = slice.actions;
export default slice.reducer;
