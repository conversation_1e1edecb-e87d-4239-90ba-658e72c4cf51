type NotificationObjectMapping = { [key: string]: string };

export const MAPPING_NOTIFICATION_OBJECT_TYPE_WITH_DISCUSSION_OBJECT_TYPE: NotificationObjectMapping =
  {
    Progress: "OVERALL_PRG",
    Action: "ACTION",
    "Action Interaction": "ACTION",
    Risk: "RISK",
    "Document Discussion": "DOCUMENT",
    "Risk Interaction": "RISK",
    "Report Parameter Interaction": "REPORT_PARAMETER",
    "Report Module Interaction": "REPORT_MODULE",
    TIMESHEET: "TIMESHEET",
    "TimeSheet Interaction": "TIMESHEET",
    "TimeSheet Labor Correction": "TIMESHEET",
    "Professional Services Cost Interaction": "PROF_SERVICES_COST",
    "Professional Services Hour Interaction": "PROF_SERVICES_HOURS",
    "Professional Services Interaction": "PROF_SERVICES_REVENUE",
    PROF_SERVICES_COST_AM: "PROF_SERVICES_COST_AM",
    PROF_SERVICES_REVENUE: "PROF_SERVICES_REVENUE",
    "Needed Support Interaction": "NEEDED_SUPPORT",
    IMPACT_SOP_DATE: "IMPACT_SOP_DATE",
    OVERALL_PRG: "OVERALL_PRG",
    ENGINEERING_PRG: "ENGINEERING_PRG",
    PROCUREMENT_PRG: "PROCUREMENT_PRG",
    CONSTRUCTION_PRG: "CONSTRUCTION_PRG",
    COMMISSIONING_PRG: "COMMISSIONING_PRG",
    OPS_PRG: "OPS_PRG",
    MEE_PRG: "MEE_PRG",
    MES_PRG: "MES_PRG",
    TAR_PRG: "TAR_PRG",
    "Pm Key Action Interaction": "IMPACT_SOP_DATE"
  };

export const INTERACTION_PRG_SUBJECT = {
  OVERALL_CHART: "Progress Overall Chart",
  OVERALL_COMMENT: "Progress Overall Comments",
  OVERALL_KPIS: "Progress Overall KPIs",
  ENGINEERING_LAST_3_MONTH: "Progress Engineering Last 3 months",
  ENGINEERING_S_CURVE: "Progress Engineering S-Curve",
  CUMULATIVE_PRG_PY_DISCIPLINE: "Cumulative Progress by Discipline",
  PROCUREMENT_LAST_3_MONTH: "Progress Procurement Last 3 months",
  PROCUREMENT_S_CURVE: "Progress Procurement S-Curve",
  NBR_OF_CONTRACTS_PACKAGES: "Number of Contracts and Packages by Period",
  PACKAGES_DELAYED_IMPACT_COMP_SCHEDULE:
    "Packages Delayed and Impact on Completion Schedule",
  CONSTRUCTION_LAST_3_MONTH: "Progress Construction Last 3 months",
  CONSTRUCTION_S_CURVE: "Progress Construction S-Curve",
  CONSTRUCTION_CUMULATIVE_BY_AREA: "Cumulative Progress by Area",
  EQUIPMENT_NUMBER_OF_INSPECTION: "Equipment - Number of Inspections",
  EQUIPMENT_NCR_AVERAGE_DAYS_OPEN: "Equipment - NCR Average Days Open",
  CONTRACTOR_AUDITS: "Contractor Audits",
  NCR_AVERAGE_DAYS_OPEN: "NCR Average Days Open",
  COMMISSIONING_LAST_3_MONTH: "Commissioning Last 3 months",
  COMMISSIONING_S_CURVE: "Commissioning S-Curve",
  COMMISSIONING_CUMULATIVE_PRG_PY_AREA: "Cumulative Progress By Area",
  CONSTRUCTION_COMMENT: "Construction Comments",
  OPS_LAST_3_MONTH: "Progress OPS Last 3 months",
  OPS_S_CURVE: "Progress OPS S-Curve",
  TAR_LAST_3_MONTH: "Progress TAR Last 3 months",
  TAR_S_CURVE: "Progress TAR S-Curve",
  MAINTENANCE_PLANING_SCHEDULING: "Maintenance planning & scheduling KPIs"
};

export const NOTIFICATION_OBJECT_TYPES = {
  ACTION: "Action",
  RISK: "Risk",
  ACTION_INTERACTION: "Action Interaction",
  RISK_INTERACTION: "Risk Interaction",
  TIMESHEET_LABOR_CORRECTION: "TimeSheet Labor Correction",
  TIMESHEET_INTERACTION: "TimeSheet Interaction",
  PROFESSIONAL_SERVICES_INTERACTION: "Professional Services Interaction",
  PROGRESS: "Progress",
  REPORT_MODULE_INTERACTION: "Report Module Interaction",
  REPORT_PARAMETER_INTERACTION: "Report Parameter Interaction",
  NEEDED_SUPPORT_INTERACTION: "Needed Support Interaction"
};

export enum DISCUSSION_OBJECT_TYPES {
  ACTION = "ACTION",
  RISK = "RISK",
  REPORT_PARAMETER = "REPORT_PARAMETER",
  PROGRESS = "OVERALL_PRG",
}

export enum MobileAlertType {
  REDIRECTION = "REDIRECTION",
  NO_REDIRECTION = "NO_REDIRECTION",
  NOT_READY_YET = "NOT_READY_YET",
}
