import {call, put, select, takeEvery} from 'redux-saga/effects';
import {
  getCostFeatures,
  getCostFeaturesWithError,
  getCostFeaturesWithSuccess,
} from './slice';
import {projectSelector} from '@src/store/project/selectors';
import baseRequest from '@src/api/request';
import {ChannelsWebServices} from '@src/api/channels';

export function* handleCostFeatures() {
  try {
    const {projectId, reportingDateFilter} = yield select(projectSelector);

    const {data} = yield call(
      baseRequest.get,
      ChannelsWebServices.WS_GET_COST_SUMMARY(projectId),
      {params: reportingDateFilter},
    );
    yield put(getCostFeaturesWithSuccess({features: data}));
  } catch (e) {
    yield put(getCostFeaturesWithError());
  }
}

export default function* saga() {
  yield takeEvery(getCostFeatures, handleCostFeatures);
}
