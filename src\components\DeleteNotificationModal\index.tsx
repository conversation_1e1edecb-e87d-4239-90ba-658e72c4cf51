import React from 'react';
import {Modal, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {PERCENTAGES, windowHeight, windowWidth} from '@src/constants';
import typography from '@src/theme/fonts';
import {BlurView} from '@react-native-community/blur';
import { COMMON_COLORS } from "@src/theme/colors";

type DeleteNotificationModelProps = {
  visible: boolean;
  onClose: () => void;
  onDelete: () => void;
};
const DeleteNotificationModal = ({
  visible,
  onClose,
  onDelete,
}: DeleteNotificationModelProps) => {
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}>
      <View style={styles.modalContainer}>
        <BlurView
          style={styles.blurContainer}
          blurType="dark"
          blurAmount={20}
          reducedTransparencyFallbackColor="white"
        />
        <View style={styles.modalContent}>
          <Text style={styles.title}>Are you sure ?</Text>
          <Text style={styles.subTitle}>
            Do you really want tot delete this notification
          </Text>
          <TouchableOpacity style={styles.deleteButton} onPress={onDelete}>
            <Text style={styles.deleteButtonText}>Delete</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeButtonText}>Cancel</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 52, 147, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  blurContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  modalContent: {
    backgroundColor: 'white',
    paddingHorizontal: windowWidth * 0.13,
    paddingVertical: windowWidth * 0.1,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    width: windowWidth * 0.88,
  },
  modalText: {
    fontSize: 16,
    marginBottom: 10,
    textAlign: 'center',
  },
  not_updated_illustration: {
    alignSelf: 'center',
    marginBottom: windowHeight * 0.03,
  },
  title: {
    color: '#003493',
    fontWeight: '700',
    fontSize: 18,
    alignSelf: 'center',
    fontFamily: typography.fontFamily.montserratSemiBold,
    marginBottom: windowHeight * 0.02,
  },
  subTitle: {
    color: '#5F6A7E',
    fontSize: 12,
    alignSelf: 'center',
    textAlign: 'center',
    fontFamily: typography.fontFamily.montserratRegular,
    marginBottom: windowHeight * 0.035,
  },
  deleteButton: {
    marginBottom: windowHeight * 0.01,
    backgroundColor: '#FF3C2F',
    width: PERCENTAGES['100'],
    padding: 11,
    borderRadius: 14,
  },
  deleteButtonText: {
    color: 'white',
    fontSize: 13,
    alignSelf: 'center',
    textAlign: 'center',
    fontFamily: typography.fontFamily.montserratBold,
  },
  closeButton: {
    marginBottom: windowHeight * 0.01,
    backgroundColor: COMMON_COLORS.GREY_10,
    padding: 11,
    borderRadius: 14,
    width: PERCENTAGES['100'],
  },
  closeButtonText: {
    color: '#394861',
    fontSize: 13,
    alignSelf: 'center',
    textAlign: 'center',
    fontFamily: typography.fontFamily.montserratBold,
  },
});

export default DeleteNotificationModal;
