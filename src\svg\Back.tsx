import {G, Path, Svg} from 'react-native-svg';
import React from 'react';

export const Back = (props: {style: {}}) => {
  return (
    <Svg
      style={props.style}
      width="8.481"
      height="16.121"
      viewBox="0 0 8.481 16.121">
      <G id="Back" transform="translate(-7.75 -3.939)">
        <G id="arrow-short-left" transform="translate(8 4.25)">
          <Path
            id="vector_Stroke_"
            data-name="vector (Stroke)"
            d="M15.775,4.484a.75.75,0,0,1-.029,1.06L9.89,11.093a1.25,1.25,0,0,0,0,1.815l5.856,5.548a.75.75,0,0,1-1.032,1.089L8.859,14a2.75,2.75,0,0,1,0-3.993l5.856-5.548A.75.75,0,0,1,15.775,4.484Z"
            transform="translate(-8 -4.25)"
            fill="#003493"
            stroke="#003493"
            stroke-width="0.5"
            fill-rule="evenodd"
          />
          <Path
            id="vector_Stroke__2"
            data-name="vector (Stroke)_2"
            d="M15.775,19.516a.75.75,0,0,1-1.06.029L8.859,14A2.743,2.743,0,0,1,8,12a.75.75,0,0,1,1.5,0,1.243,1.243,0,0,0,.39.907l5.856,5.548A.75.75,0,0,1,15.775,19.516Z"
            transform="translate(-8 -4.25)"
            fill="#003493"
            stroke="#003493"
            stroke-width="0.5"
            fill-rule="evenodd"
          />
        </G>
      </G>
    </Svg>
  );
};
