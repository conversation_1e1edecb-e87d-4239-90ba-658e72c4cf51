import { StyleSheet } from "react-native";
import typography from "@src/theme/fonts";
import layout from "@src/theme/layout";
import { correspondentHeight, correspondentWidth } from "@src/utils/imageUtils";
import { COMMON_COLORS } from "@src/theme/colors";
import { windowHeight } from "@src/constants";

export const globalStyles = StyleSheet.create({
  featureKey: {
    color: COMMON_COLORS.BLUE_20,
    fontSize: 14,
    fontFamily: typography.fontFamily.montserratSemiBold,
    fontWeight: "bold"
  },
  notificationLogoImage: {
    flex: 1,
    width: correspondentWidth(22),
    height: correspondentHeight(22),
    marginStart: correspondentWidth(15)
  },
  drawerLogoImage: {
    flex: 1,
    width: correspondentWidth(22),
    height: correspondentHeight(22),
    marginStart: correspondentWidth(15)
  },
  calendarIcon: {
    flex: 1,
    marginStart: correspondentWidth(15),
    width: correspondent<PERSON>idth(22),
    height: correspondentWidth(22)
  },
  routeName: {
    color: COMMON_COLORS.BLUE_40,
    fontFamily: typography.fontFamily.montserratBold,
    fontWeight: "bold",
    fontSize: 18
  },
  routeContainer: {
    ...layout.rowCenter
  },
  notificationRead: {
    backgroundColor: "white"
  },
  notificationUnRead: {
    backgroundColor: "#FAFCFF"
  },
  blurViewModal: {
    flex: 1,
    backgroundColor: COMMON_COLORS.BLUE_40,
    opacity: 0.4,
    height: windowHeight
  },
  screenContainerPaddingTop: {
    paddingTop: windowHeight * 0.025
  },
  screenContainerPaddingTopForAbstractScreen: {
    paddingTop: correspondentHeight(10)
  },
  backImage: {
    width: correspondentWidth(8),
    height: correspondentHeight(15),
    marginEnd: correspondentWidth(18),
    resizeMode: "contain"
  },
  iconsContainer: {
    ...layout.rowCenter
  },
  loaderContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: "center",
    alignItems: "center"
  }
});

export const backgroundColorNotificationContainer = (isRead: boolean) => {
  return !isRead ? "#FAFCFF" : COMMON_COLORS.WHITE;
};

export const svgChannelIconWidth = (input: number) => correspondentWidth(input);
export const svgChannelIconHeight = (input: number) =>
  correspondentHeight(input);
