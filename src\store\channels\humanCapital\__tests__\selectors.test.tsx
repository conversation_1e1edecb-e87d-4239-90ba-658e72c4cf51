import {RootState} from '@src/store/store';
import {HumanCapSliceType} from '@src/store/channels/humanCapital/slice';
import {humanCapSelector} from '@src/store/channels/humanCapital/selectors';

describe('HumanCapSelector', () => {
  it('should select the human cap state from the root state', () => {
    // Arrange
    const humanCapState: HumanCapSliceType = {
      features: null,
      channelStatus: 'PRIMARY',
      loading: false,
      error: false,
      success: false,
    };

    // @ts-ignore
    const mockRootState: RootState = {
      humanCapChannel: humanCapState,
    };

    // Act
    const selectedState = humanCapSelector(mockRootState);

    // Assert
    expect(selectedState).toEqual(humanCapState);
  });
});
