// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DiscussionSummary Should render and match the snapshot 1`] = `
<View
  style={
    {
      "backgroundColor": "white",
      "borderRadius": 20,
      "elevation": 3,
      "marginBottom": 8,
      "paddingBottom": 26,
      "paddingStart": 25,
      "shadowColor": "#000",
      "shadowOpacity": 0.05,
      "shadowRadius": 16,
    }
  }
>
  <View
    style={
      {
        "alignItems": "center",
        "flexDirection": "row",
        "marginEnd": 25,
      }
    }
  >
    <Image
      style={
        {
          "marginEnd": 18,
        }
      }
    />
    <Text
      style={
        {
          "color": "#394861",
          "fontFamily": "Montserrat-Regular",
          "fontSize": 14,
          "fontWeight": "800",
          "maxWidth": 412.50000000000006,
        }
      }
    >
       
    </Text>
    <Text
      style={
        {
          "color": "#5F6A7E",
          "fontFamily": "Montserrat-Regular",
          "fontSize": 14,
          "fontStyle": "italic",
          "fontWeight": "300",
        }
      }
    />
  </View>
  <View
    style={
      {
        "marginEnd": 25,
      }
    }
  />
</View>
`;
