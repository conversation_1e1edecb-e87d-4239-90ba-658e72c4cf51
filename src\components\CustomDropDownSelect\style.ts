import {StyleSheet} from 'react-native';
import {PERCENTAGES, windowHeight} from '@src/constants';
import {COMMON_COLORS} from '@src/theme/colors';
import typography from '@src/theme/fonts';
import layout from '@src/theme/layout';
import {correspondentHeight, correspondentWidth} from '@src/utils/imageUtils';

const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  blurContainer: {
    position: 'absolute',
    backgroundColor: '#003493',
    opacity: 0.3,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  container: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  content: {
    backgroundColor: 'white',
    padding: 20,
    paddingTop: 12,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  closeButton: {
    alignSelf: 'flex-end',
    marginBottom: 10,
  },
  line: {
    width: '28%',
    height: 4,
    backgroundColor: '#E9EEF5',
    alignSelf: 'center',
    marginVertical: 12,
    borderRadius: 11,
  },
  title: {
    fontSize: 18,
    fontWeight: '900',
    color: '#003493',
  },
  notificationContent: {
    fontSize: typography.fontSizes.sm,
    fontWeight: '400',
    marginBottom: '6%',
    marginTop: '5%',
    textAlign: 'center',
    color: COMMON_COLORS.BLUE_20,
  },
  button: {
    backgroundColor: COMMON_COLORS.PRIMARY,
    padding: 12,
    paddingHorizontal: 26,
    alignItems: 'center',
    borderRadius: 14,
    width: PERCENTAGES['50'],
    alignSelf: 'flex-end',
  },

  buttonText: {
    color: COMMON_COLORS.WHITE,
    fontSize: 13,
    fontWeight: 'bold',
  },

  twoButtonsContainer: {
    marginTop: 30,
    ...layout.rowVCenterSpaceBetween,
  },
  filterFieldsTitle: {
    fontSize: 18,
    color: COMMON_COLORS.BLUE_40,
    fontWeight: '700',
    marginBottom: 30,
  },
  dropdown: {
    borderWidth: 1,
    borderColor: COMMON_COLORS.GREY_30,
    height: windowHeight * 0.052,
    borderRadius: 14,
    paddingHorizontal: 18,
    paddingVertical: 4,
    marginBottom: 8,
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: COMMON_COLORS.GREY_30,
    borderRadius: 14,
    paddingHorizontal: 8,
    marginBottom: 4,
  },
  placeholderStyle: {
    fontSize: 12,
  },
  selectedTextStyle: {fontSize: 12},
  itemTextStyle: {
    fontSize: 12,
    backgroundColor: COMMON_COLORS.PRIMARY,
  },
  subTitle: {
    fontSize: 13,
    color: COMMON_COLORS.PRIMARY,
  },
  input: {
    borderWidth: 1,
    borderColor: 'gray',
    borderRadius: 8,
    paddingHorizontal: correspondentWidth(18),
    paddingVertical: correspondentHeight(4),
    marginBottom: correspondentHeight(8),
  },
  label: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: correspondentHeight(8),
    color: 'gray',
  },
  placeholder: {
    color: 'gray',
    opacity: 0.5,
    fontWeight: 'bold',
    fontSize: 12,
  },
  nonItemSelectedTextStyle: {
    borderRadius: 14,
    textAlign: 'center',
    textAlignVertical: 'center',
    color: 'gray',
    opacity: 0.5,
    fontWeight: 'bold',
    fontSize: 12,
  },
});

export default styles;
