import {call, put, takeEvery} from 'redux-saga/effects';
import baseRequest from '@src/api/request';
import {ProjectWebServices} from '@src/api/project';
import {
  getSectorEnums,
  getSectorEnumsWithError,
  getSectorEnumsWithSuccess,
} from '@src/store/filterEnumsProjects/sectorEnums/slice';

export function* handleSectorEnums(action: {
  payload: {
    types: string;
  };
}) {
  try {
    const {types} = action.payload;
    const {data} = yield call(
      baseRequest.get,
      ProjectWebServices.WS_GET_ENUMS_BY_TYPE,
      {params: {types}},
    );
    yield put(getSectorEnumsWithSuccess(data.SECTOR));
  } catch (e) {
    yield getSectorEnumsWithError();
  }
}

export default function* saga() {
  yield takeEvery(getSectorEnums, handleSectorEnums);
}
