import {useSelector} from 'react-redux';
import {projectSelector} from '@src/store/project/selectors';

export const useProjectPrivilegeByModule = (module: string): boolean => {
  const {flattenKpis} = useSelector(projectSelector);
  if (flattenKpis !== null) {
    const flattenKpisValues = Object.values(flattenKpis);
    return flattenKpisValues.some(features => {
      const filteredFeatures = features.filter(
        feature => feature?.feature?.module?.code === module,
      );
      return filteredFeatures.some(feature => feature.isIn);
    });
  } else {
    return false;
  }
};
