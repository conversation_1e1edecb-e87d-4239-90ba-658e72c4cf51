import reducer, {
  cleanUp,
  initialState,
  setNotification,
  setOpenedFromNotification,
} from '@src/store/notification/slice';

describe('Notification reducer', () => {
  it('should return the initial state', () => {
    expect(reducer(undefined, {type: undefined})).toEqual(initialState);
  });

  it('should handle setNotification action', () => {
    const action = setNotification({});
    const newState = reducer(initialState, action);
    expect(newState).toEqual({
      ...initialState,
      notification: {},
    });
  });

  it('should handle setOpenedFromNotification action', () => {
    const action = setOpenedFromNotification(true);
    const newState = reducer(initialState, action);
    expect(newState).toEqual({
      ...initialState,
      isOpenedFromNotification: true,
    });
  });

  it('should handle cleanUp action', () => {
    const action = cleanUp();
    const newState = reducer(initialState, action);
    expect(newState).toEqual({
      ...initialState,
    });
  });
});
