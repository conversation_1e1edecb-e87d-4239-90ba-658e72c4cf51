// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Interaction Should render and match the snapshot 1`] = `
<View
  style={
    {
      "backgroundColor": "#F1F7FF",
      "marginBottom": 16,
      "marginStart": 37,
      "paddingHorizontal": 14,
      "paddingVertical": 14,
    }
  }
>
  <View
    style={
      {
        "alignItems": "center",
        "flexDirection": "row",
        "justifyContent": "space-between",
      }
    }
  >
    <View
      style={
        {
          "alignItems": "center",
          "flexDirection": "row",
          "maxWidth": "70%",
          "minWidth": "70%",
        }
      }
    >
      <View
        style={
          {
            "aspectRatio": 1,
            "borderRadius": 12,
            "width": 63.75000000000001,
          }
        }
      >
        <Image
          source={
            {
              "testUri": "../../../assets/images/avatar_user.png",
            }
          }
          style={
            {
              "borderRadius": 12,
              "height": "100%",
              "width": "100%",
            }
          }
        />
      </View>
      <View
        style={
          {
            "marginStart": 15,
            "maxWidth": 375,
          }
        }
      >
        <Text
          style={
            {
              "color": "#3C485F",
              "fontSize": 14,
              "fontWeight": "bold",
            }
          }
        >
           
        </Text>
      </View>
    </View>
    <Text
      style={
        [
          {
            "color": "#3C485F",
            "maxWidth": 187.5,
          },
        ]
      }
    />
  </View>
  <View
    style={
      {
        "marginStart": 75,
      }
    }
  >
    <View
      style={{}}
    >
      <Text
        style={
          {
            "color": "#3C485F",
            "fontSize": 14,
          }
        }
      >
        <Text />
      </Text>
    </View>
    <View
      style={
        {
          "alignItems": "center",
          "flexDirection": "row",
          "justifyContent": "space-between",
        }
      }
    >
      <View
        style={
          {
            "flexDirection": "row",
            "marginTop": 13.34,
          }
        }
      >
        <View
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": undefined,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={true}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "opacity": 1,
            }
          }
        >
          <Text
            style={
              {
                "color": "#007CFF",
                "fontSize": 13,
                "fontWeight": "bold",
              }
            }
          >
            Reply
          </Text>
        </View>
      </View>
    </View>
  </View>
</View>
`;
