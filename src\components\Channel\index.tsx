import { Text, View } from "react-native";
import Image from "@src/components/ScalableImage";

import { CHANNEL_STATUS_COLOR_VALUE } from "@src/constants/channels/channelStatusColor";
import React, { ReactElement } from "react";
import { useSelector } from "react-redux";
import style from "@src/components/Channel/style";
import LinearGradient from "react-native-linear-gradient";
import { RootState } from "@src/store/store";
import { HseChannelSummary } from "@src/models/channelsSummary/hseChannelSummary";
import { ProgressChannelSummary } from "@src/models/channelsSummary/progressChannelSummary";
import { QualityChannelSummary } from "@src/models/channelsSummary/qualityChannelSummary";
import { CashFlowChannelSummary } from "@src/models/channelsSummary/cashChannelSummary";
import { SchedulingChannelSummary } from "@src/models/channelsSummary/schedulingChannelSummary";
import { RiskChannelSummary } from "@src/models/channelsSummary/riskChannelSummary";
import { CostChannelSummary } from "@src/models/channelsSummary/costChannelSummary";
import { HumanCapitalChannelSummary } from "@src/models/channelsSummary/humanCapitalChannelSummary";
import { ChangeManagementChannelSummary } from "@src/models/channelsSummary/changeManagementChannelSummary";

const Channel = (props: {
  title: string;
  illustration: number;
  content: ReactElement;
  selector: (
    state: RootState
  ) =>
    | SchedulingChannelSummary
    | RiskChannelSummary
    | CostChannelSummary
    | HumanCapitalChannelSummary
    | ChangeManagementChannelSummary
    | HseChannelSummary
    | ProgressChannelSummary
    | QualityChannelSummary
    | CashFlowChannelSummary;
}) => {
  const { title, illustration, content, selector } = props;

  const { channelStatus } = useSelector(selector);
  
  const gradientColors = channelStatus
    ? CHANNEL_STATUS_COLOR_VALUE[channelStatus]
    : CHANNEL_STATUS_COLOR_VALUE.PRIMARY;

  return (
    <LinearGradient
      colors={gradientColors}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 0 }}
      style={style.gradientBorder}>
      <View style={style.container}>
        <Text style={style.channelTitle}>{title}</Text>
        <Image
          style={style.svgChannel}
          height={style.svgChannelDimensions.height}
          width={style.svgChannelDimensions.width}
          source={illustration}
        />
        {content}
      </View>
    </LinearGradient>
  );
};

export default Channel;
