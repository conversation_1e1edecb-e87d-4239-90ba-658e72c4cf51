import { Text, View } from "react-native";
import { styles } from "./style";
import { useSelector } from "react-redux";
import { discussionSelector } from "@src/store/discussion/selectors";
import React from "react";
import { DateUtils } from "@src/utils/dateUtils";
import { CalendarDiscussionIcon } from "@src/svg/CalendarDiscussionIcon";

const DiscussionKPIBody = () => {
  const { discussion } = useSelector(discussionSelector);
  if (!discussion) {
    return null;
  }
  const { periodOfData, description } = discussion;
  console.log(discussion);
  const month = DateUtils.convertMonthNumberToName(periodOfData.month);
  return (
    <>
      <View style={styles.oneInfoDetail}>
        <Text>KPI NAME</Text>
        <Text>{description}</Text>
      </View>
      <View style={styles.oneInfoDetail}>
        <Text>Period</Text>
        <View style={styles.dateContainer}>
          <View style={styles.dateIcon}>
            <CalendarDiscussionIcon />
          </View>
          <Text style={styles.periodValue}>
            {month ?? ""} {periodOfData.year ?? ""}
          </Text>
        </View>
      </View>
    </>
  );
};

export default DiscussionKPIBody;
