import {G, Path, Svg} from 'react-native-svg';
import React from 'react';

export const NotificationUnfocused = (props: {style: {}}) => {
  return (
    <Svg
      style={props.style}
      width="18.845"
      height="21.633"
      viewBox="0 0 18.845 21.633">
      <G id="Notification" transform="translate(-2.609 -1.365)">
        <G id="notification-bell-new" transform="translate(2.886 1.636)">
          <Path
            id="vector_Stroke_"
            data-name="vector (Stroke)"
            d="M8.741,18.83a.754.754,0,0,1,.754.754,1.116,1.116,0,0,0,.14.532,1.636,1.636,0,0,0,.45.513,2.441,2.441,0,0,0,.747.382,3.1,3.1,0,0,0,1.849,0,2.441,2.441,0,0,0,.747-.382,1.637,1.637,0,0,0,.45-.513,1.116,1.116,0,0,0,.14-.532.754.754,0,1,1,1.508,0,2.623,2.623,0,0,1-.319,1.246,3.14,3.14,0,0,1-.858.993,3.945,3.945,0,0,1-1.209.624,4.608,4.608,0,0,1-2.766,0,3.946,3.946,0,0,1-1.209-.624,3.141,3.141,0,0,1-.858-.993,2.623,2.623,0,0,1-.319-1.246A.754.754,0,0,1,8.741,18.83Z"
            transform="translate(-2.86 -1.547)"
            fill="#66bcff"
            stroke="#66bcff"
            stroke-width="0.5"
            fill-rule="evenodd"
          />
          <Path
            id="vector_Stroke__2"
            data-name="vector (Stroke)_2"
            d="M8.18,2.511A8.359,8.359,0,0,1,14.3,1.987a.754.754,0,0,1-.432,1.444,6.851,6.851,0,0,0-5.017.43A5.137,5.137,0,0,0,6,8.46V9.788a8.612,8.612,0,0,1-1.038,4.1l-.228.421a2.828,2.828,0,0,0,1.973,4.126,27.9,27.9,0,0,0,10.141,0l.161-.03a2.938,2.938,0,0,0,2.132-4.126l-.266-.574a8.8,8.8,0,0,1-.754-2.655.754.754,0,1,1,1.5-.179,7.289,7.289,0,0,0,.625,2.2l.266.574a4.446,4.446,0,0,1-3.225,6.242l-.137-.741.137.741-.161.03a29.4,29.4,0,0,1-10.69,0,4.335,4.335,0,0,1-3.025-6.326l.228-.421A7.1,7.1,0,0,0,4.5,9.788V8.46A6.644,6.644,0,0,1,8.18,2.511Z"
            transform="translate(-2.886 -1.636)"
            fill="#66bcff"
            stroke="#66bcff"
            stroke-width="0.5"
            fill-rule="evenodd"
          />
          <Path
            id="vector_Stroke__3"
            data-name="vector (Stroke)_3"
            d="M17.877,4.295a1.759,1.759,0,1,0,1.759,1.759A1.759,1.759,0,0,0,17.877,4.295ZM14.61,6.054a3.267,3.267,0,1,1,3.267,3.267A3.267,3.267,0,0,1,14.61,6.054Z"
            transform="translate(-2.826 -1.63)"
            fill="#66bcff"
            stroke="#66bcff"
            stroke-width="0.5"
            fill-rule="evenodd"
          />
        </G>
      </G>
    </Svg>
  );
};
