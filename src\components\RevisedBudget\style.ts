import {StyleSheet} from 'react-native';
import typography from '@src/theme/fonts';
import {COMMON_COLORS} from '@src/theme/colors';

const style = StyleSheet.create({
  valuePendingContractorChange: {
    color: '#007EFF',
    fontSize: typography.fontSizes.md,
    fontFamily: typography.fontFamily.montserratSemiBold,
    fontWeight: 'bold',
  },
  MMAD: {
    fontSize: 14,
    color: COMMON_COLORS.BLUE_20,
    fontFamily: typography.fontFamily.montserratRegular,
  },
});

export default style;
