import { Image, Text } from "react-native";
import React, { FC } from "react";
import { IMAGES } from "@src/constants/images";
import { Suggestion } from "react-native-controlled-mentions";
import { styles } from "@src/components/SuggestionItem/style";
import { TouchableOpacity } from "react-native-gesture-handler";
import { returnFinalBase64String } from "@src/utils/imageUtils";
import useGetUserAvatar from "@src/hooks/useGetUserAvatar";

type SuggestionItemProps = {
  newSuggestionObject: Suggestion;
  onSuggestionPress: (suggestion: Suggestion) => void;
  userId: number;
  displayName: string;
};

export const SuggestionItem: FC<SuggestionItemProps> = props => {
  const { newSuggestionObject, onSuggestionPress, displayName, userId } = props;
  const userAvatarFromHook = useGetUserAvatar({
    userIdProps: userId.toString()
  });
  return (
    <TouchableOpacity
      activeOpacity={1}
      onPress={() => {
        onSuggestionPress(newSuggestionObject);
      }}
      style={styles.taggedPersonContainer}>
      {userAvatarFromHook.length > 0 ? (
        <Image
          style={styles.userAvatar}
          source={{ uri: returnFinalBase64String(userAvatarFromHook) }}
        />
      ) : (
        <Image style={styles.userAvatar} source={IMAGES.USER_AVATAR} />
      )}
      <Text style={styles.userName}>{displayName}</Text>
    </TouchableOpacity>
  );
};
