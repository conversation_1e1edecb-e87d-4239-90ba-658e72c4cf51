import {expectSaga} from 'redux-saga-test-plan';
import {call} from 'redux-saga/effects';
import baseRequest from '@src/api/request';
import {
  getFlattenKpisWithError,
  getFlattenKpisWithSuccess,
  getUserProjectWithProjectIdAndUserIdWithError,
  getUserProjectWithProjectIdAndUserIdWithSuccess,
  setCurrentProjectWithError,
  setCurrentProjectWithSuccess,
} from '@src/store/project/slice';
import {
  handleFlattenKpis,
  handleProject,
  handleUserProject,
} from '@src/store/project/saga';
import {ProjectWebServices} from '@src/api/project';
import {ChannelsWebServices} from '@src/api/channels';

describe('projectSagas', () => {
  const userId = '1234';
  const projectId = 22;
  describe('setCurrentProject', () => {
    it('should yield an api call', () => {
      expectSaga(handleProject, {payload: 22})
        .provide([
          [
            call(baseRequest.get, ProjectWebServices.WS_GET_PROJECT_BY_ID(22)),
            {},
          ],
        ])
        .run();
    });

    it('should handle setCurrentProjectWithSuccess successfully', () => {
      expectSaga(handleProject, {payload: 22})
        .provide([
          [
            call(baseRequest.get, ProjectWebServices.WS_GET_PROJECT_BY_ID(22)),
            {},
          ],
        ])
        .put(
          setCurrentProjectWithSuccess({
            project: {},
            reportingDate: {
              year: 2022,
              month: 12,
            },
          }),
        )
        .run();
    });

    it('should handle setCurrentProjectWithError sagas ', () => {
      expectSaga(handleProject, {payload: 22})
        .provide([
          [
            call(baseRequest.get, ProjectWebServices.WS_GET_PROJECT_BY_ID(22)),
            {},
          ],
        ])
        .put(setCurrentProjectWithError())
        .run();
    });
  });

  describe('get getFlattenKpis', () => {
    it('should yield an api call', () => {
      expectSaga(handleFlattenKpis, {payload: {projectId}})
        .provide([
          [
            call(
              baseRequest.get,
              ChannelsWebServices.WS_GET_PROJECT_CHANNEL_KPIS(projectId),
            ),
            {},
          ],
        ])
        .run();
    });

    it('should handle getFlattenKpisWithSuccess successfully', () => {
      expectSaga(handleFlattenKpis, {payload: {projectId}})
        .provide([
          [
            call(
              baseRequest.get,
              ChannelsWebServices.WS_GET_PROJECT_CHANNEL_KPIS(projectId),
            ),
            {},
          ],
        ])
        .put(getFlattenKpisWithSuccess({}))
        .run();
    });

    it('should handle getFlattenKpisWithError successfully', () => {
      expectSaga(handleFlattenKpis, {payload: {projectId}})
        .provide([
          [
            call(
              baseRequest.get,
              ChannelsWebServices.WS_GET_PROJECT_CHANNEL_KPIS(projectId),
            ),
            {},
          ],
        ])
        .put(getFlattenKpisWithError())
        .run();
    });
  });

  describe('handleUserProject', () => {
    it('should yield an api call', () => {
      expectSaga(handleUserProject, {payload: {projectId}})
        .provide([
          [
            call(
              baseRequest.get,
              ProjectWebServices.WS_GET_USER_PROJECT_BY_USER_ID_AND_PROJECT_ID(
                userId,
                projectId,
              ),
            ),
            {},
          ],
        ])
        .run();
    });

    it('should handle getUserProjectWithProjectIdAndUserIdWithSuccess successfully', () => {
      expectSaga(handleUserProject, {payload: {projectId}})
        .provide([
          [
            call(
              baseRequest.get,
              ProjectWebServices.WS_GET_USER_PROJECT_BY_USER_ID_AND_PROJECT_ID(
                userId,
                projectId,
              ),
            ),
            {},
          ],
        ])
        .put(
          getUserProjectWithProjectIdAndUserIdWithSuccess({
            profile: {},
            role: {},
          }),
        )
        .run();
    });

    it('should handle getUserProjectWithProjectIdAndUserIdWithError successfully', () => {
      expectSaga(handleUserProject, {payload: {projectId}})
        .provide([
          [
            call(
              baseRequest.get,
              ProjectWebServices.WS_GET_USER_PROJECT_BY_USER_ID_AND_PROJECT_ID(
                userId,
                projectId,
              ),
            ),
            {},
          ],
        ])
        .put(getUserProjectWithProjectIdAndUserIdWithError())
        .run();
    });
  });
});
