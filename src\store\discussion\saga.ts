import { call, put, takeEvery } from "redux-saga/effects";
import {
  createUpdateDiscussion,
  createUpdateDiscussionWithError,
  createUpdateDiscussionWithSuccess,
  getDiscussionDetailsByObjectTypeAndObjectId,
  getDiscussionDetailsByObjectTypeAndObjectIdWithError,
  getDiscussionDetailsByObjectTypeAndObjectIdWithSuccess,
  getDiscussionUsers,
  getDiscussionUsersWithError,
  getDiscussionUsersWithSuccess,
  InteractionParams
} from "@src/store/discussion/slice";
import baseRequest from "@src/api/request";
import { DiscussionWebServices } from "@src/api/discussion";
import { ProjectWebServices } from "@src/api/project";
import { Interaction } from "@src/models/interaction";
import { transformString } from "@src/utils/stringUtils";

export function* handleGetDiscussionDetails(action: {
  payload: { objectType: string; objectId: number };
}) {
  try {
    const { objectType, objectId } = action.payload;
    const { data } = yield call(
      baseRequest.get,
      DiscussionWebServices.GET_DISCUSSION_DETAILS_BY_OBJECT_TYPE_AND_OBJECT_ID,
      {
        params: {
          kpiType: objectType,
          objectId: objectId
        }
      }
    );
    yield put(
      getDiscussionDetailsByObjectTypeAndObjectIdWithSuccess({
        data,
        type: objectType,
        objectId: objectId
      })
    );
  } catch (e) {
    yield put(getDiscussionDetailsByObjectTypeAndObjectIdWithError());
  }
}

export function* handleGetDiscussionUsers(action: {
  payload: { idProject: number };
}) {
  try {
    const { idProject } = action.payload;
    const { data } = yield call(
      baseRequest.get,
      ProjectWebServices.WS_GET_USERS_IN_ONE_PROJECT(idProject),
      { params: { idProject } }
    );
    yield put(getDiscussionUsersWithSuccess(data));
  } catch (e) {
    yield put(getDiscussionUsersWithError());
  }
}

export function* handleCreateUpdateDiscussion(action: {
  payload: { payload: Interaction; params: InteractionParams };
}) {
  try {
    const { params, payload } = action.payload;
    const transformedString = transformString(payload.comment);
    const { data } = yield call(
      baseRequest.post,
      DiscussionWebServices.CREATE_UPDATE_DISCUSSION_INTERACTION(params.projectId),
      {
        ...payload,
        comment: transformedString
      }
    );

    yield put(createUpdateDiscussionWithSuccess(data));
  } catch (e) {
    yield put(createUpdateDiscussionWithError());
  }
}

export default function* saga() {
  // same as incrementNumber.type or incrementNumber
  yield takeEvery(
    getDiscussionDetailsByObjectTypeAndObjectId,
    handleGetDiscussionDetails
  );
  yield takeEvery(createUpdateDiscussion, handleCreateUpdateDiscussion);
  yield takeEvery(getDiscussionUsers, handleGetDiscussionUsers);
}
