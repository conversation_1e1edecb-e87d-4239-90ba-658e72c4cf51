import {renderWithProviders} from '@src/utils/utils-for-tests';
import React from 'react';
import {HeadCountContractor} from '@src/components/HeadCountContractor';

describe('HeadCountContractor', function () {
  it('Should render and match the snapshot', () => {
    const tree = renderWithProviders(
      <HeadCountContractor value={'2'} />,
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
