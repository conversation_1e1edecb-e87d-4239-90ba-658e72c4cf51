import { createSlice } from "@reduxjs/toolkit";
import { Discussion } from "@src/models/discussion";
import { SLICES_NAMES } from "@src/constants/store";
import { DISCUSSION_OBJECT_TYPES } from "@src/constants/notification";
import { Interaction } from "@src/models/interaction";
import { User } from "@src/models/user";

type DiscussionSliceType = {
  loading: boolean;
  error: boolean;
  success: boolean;
  errorUsers: boolean;
  successUsers: boolean;
  errorCreateUpdate: boolean;
  successCreateUpdate: boolean;
  discussion: Discussion | null;
  users: User[] | null;
};

export type InteractionParams = {
  type: DISCUSSION_OBJECT_TYPES;
  projectId: number;
  entityId?: number | null | undefined;
  parameterCode?: string | null | undefined;
  month?: number | null | undefined;
  year?: number | null | undefined;
  subject?: string | null | undefined;
  workOrderId?: number | null | undefined;
  [key: string]: string | null | undefined | number | DISCUSSION_OBJECT_TYPES;
};

export const initialState: DiscussionSliceType = {
  loading: false,
  success: false,
  error: false,
  errorUsers: false,
  successUsers: false,
  errorCreateUpdate: false,
  successCreateUpdate: false,
  discussion: null,
  users: null
};
export const slice = createSlice({
  name: SLICES_NAMES.DISCUSSION,
  initialState,
  reducers: {
    getDiscussionDetailsByObjectTypeAndObjectId: (state, action) => {
      return {
        ...state,
        loading: true,
        success: false,
        error: false
      };
    },
    getDiscussionDetailsByObjectTypeAndObjectIdWithSuccess: (
      state,
      action: {
        payload: { data: Discussion; type: string; objectId: number };
      }
    ) => {
      const discussionId = action.payload.data.entityId || action.payload.data.discussionId;
      return {
        ...state,
        discussion: {
          ...action.payload.data,
          discussionId: discussionId,
          type: action.payload.type,
          objectId: action.payload.objectId
        },
        loading: false,
        success: true,
        error: false
      };
    },
    getDiscussionDetailsByObjectTypeAndObjectIdWithError: state => {
      return {
        ...state,
        loading: false,
        success: false,
        error: true,
        discussion: null,
        users: null
      };
    },
    getDiscussionUsers: (state, action) => {
      return {
        ...state,
        loading: true,
        errorUsers: false,
        successUsers: false
      };
    },
    getDiscussionUsersWithSuccess: (state, action) => {
      return {
        ...state,
        loading: false,
        errorUsers: false,
        successUsers: true,
        users: action.payload
      };
    },
    getDiscussionUsersWithError: state => {
      return {
        ...state,
        loading: false,
        errorUsers: true,
        successUsers: false
      };
    },
    createUpdateDiscussion: (state, action) => {
      return {
        ...state,
        loading: true,
        errorCreateUpdate: false,
        successCreateUpdate: false
      };
    },
    createUpdateDiscussionWithSuccess: (
      state,
      action: { payload: { interactions: Interaction } }
    ) => {
      if (state.discussion !== null) {
        return {
          ...state,
          loading: false,
          successCreateUpdate: true,
          errorCreateUpdate: false,
          discussion: {
            ...state.discussion,
            interactions: [
              ...state.discussion.interactions,
              action.payload.interactions
            ]
          }
        };
      }
    },
    createUpdateDiscussionWithError: state => {
      return {
        ...state,
        loading: false,
        successCreateUpdate: false,
        errorCreateUpdate: true
      };
    },
    cleanUp: () => {
      return initialState;
    }
  }
});

export const {
  getDiscussionUsersWithSuccess,
  getDiscussionUsersWithError,
  getDiscussionUsers,
  getDiscussionDetailsByObjectTypeAndObjectId,
  getDiscussionDetailsByObjectTypeAndObjectIdWithError,
  getDiscussionDetailsByObjectTypeAndObjectIdWithSuccess,
  createUpdateDiscussion,
  cleanUp,
  createUpdateDiscussionWithSuccess,
  createUpdateDiscussionWithError
} = slice.actions;
export default slice.reducer;
