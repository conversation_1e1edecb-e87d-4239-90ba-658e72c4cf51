import React from 'react';
import {renderWithProviders} from '@src/utils/utils-for-tests';
import {NavigationContainer} from '@react-navigation/native';
import {MainChannelCarouselAndBottomCarouselContainer} from '@src/containers/MainChannelCarouselAndBottomCarouselContainer';

describe('Main channel carousel and bottom carousel', () => {
  it('should render  and match the snapshot', () => {
    const updateCarouselPositionHandler = jest.fn();
    const tree = renderWithProviders(
      <NavigationContainer>
        <MainChannelCarouselAndBottomCarouselContainer
          bottomCarouselChannels={[]}
          mainCarouselChannels={[]}
          updateCarouselPositionHandler={updateCarouselPositionHandler}
        />
      </NavigationContainer>,
      {},
    );
    expect(tree).toMatchSnapshot();
  });
});
