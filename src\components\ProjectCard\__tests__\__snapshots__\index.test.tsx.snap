// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Project Card Should render and match the snapshot 1`] = `
<View
  accessibilityState={
    {
      "busy": undefined,
      "checked": undefined,
      "disabled": undefined,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  onClick={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    {
      "alignItems": "center",
      "borderRadius": 22,
      "marginBottom": 26.68,
      "opacity": 1,
      "paddingBottom": 20.009999999999998,
      "paddingTop": 200.1,
    }
  }
  testID="project-card"
>
  <View
    accessibilityIgnoresInvertColors={true}
    style={
      [
        {
          "bottom": 0,
          "left": 0,
          "position": "absolute",
          "right": 0,
          "top": 0,
        },
      ]
    }
  >
    <Image
      blurRadius={2}
      borderRadius={22}
      source={
        {
          "testUri": "../../../assets/images/group_masque_image.png",
        }
      }
      style={
        [
          {
            "bottom": 0,
            "left": 0,
            "position": "absolute",
            "right": 0,
            "top": 0,
          },
          {
            "height": undefined,
            "width": undefined,
          },
          undefined,
        ]
      }
    />
  </View>
  <View
    accessibilityState={
      {
        "busy": undefined,
        "checked": undefined,
        "disabled": undefined,
        "expanded": undefined,
        "selected": undefined,
      }
    }
    accessibilityValue={
      {
        "max": undefined,
        "min": undefined,
        "now": undefined,
        "text": undefined,
      }
    }
    accessible={true}
    collapsable={false}
    focusable={true}
    onClick={[Function]}
    onResponderGrant={[Function]}
    onResponderMove={[Function]}
    onResponderRelease={[Function]}
    onResponderTerminate={[Function]}
    onResponderTerminationRequest={[Function]}
    onStartShouldSetResponder={[Function]}
    style={
      {
        "backgroundColor": "white",
        "borderRadius": 20,
        "opacity": 1,
        "paddingBottom": 20.009999999999998,
        "paddingHorizontal": 37.5,
        "paddingTop": 33.35,
        "width": "95%",
      }
    }
  >
    <View
      style={
        {
          "alignItems": "center",
          "flexDirection": "row",
          "justifyContent": "space-between",
          "marginBottom": 30.014999999999997,
        }
      }
    >
      <Image
        background={false}
        height={66.7}
        onSize={[Function]}
        source={
          {
            "testUri": "../../../assets/images/avatar_user.png",
          }
        }
        style={
          [
            undefined,
            {
              "height": NaN,
              "width": NaN,
            },
          ]
        }
      />
    </View>
    <View
      style={
        {
          "alignItems": "center",
          "flexDirection": "row",
          "justifyContent": "space-between",
        }
      }
    >
      <View
        style={{}}
      >
        <Text
          style={
            {
              "color": "#00338D",
              "fontSize": 15,
              "fontWeight": "900",
              "maxWidth": "100%",
            }
          }
        >
          title
        </Text>
        <Text
          style={
            {
              "color": "#394861",
              "fontSize": 12,
            }
          }
        >
          reference
        </Text>
      </View>
      <View
        style={
          {
            "borderRadius": 4,
          }
        }
      >
        <Image
          background={false}
          onSize={[Function]}
          source={
            {
              "testUri": "../../../assets/images/next_arrow_icon_image.png",
            }
          }
          style={
            [
              undefined,
              {
                "height": NaN,
                "width": NaN,
              },
            ]
          }
        />
      </View>
    </View>
  </View>
</View>
`;
