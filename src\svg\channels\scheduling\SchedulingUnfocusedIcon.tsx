import React from 'react';
import {G, Path, Svg} from 'react-native-svg';
import {svgChannelIconHeight, svgChannelIconWidth} from '@src/theme/style';

const SchedulingUnfocusedIcon = () => (
  <Svg
    width={svgChannelIconWidth(27.062)}
    height={svgChannelIconHeight(27.062)}
    viewBox="0 0 27.062 27.062">
    <G
      id="Groupe_61265"
      data-name="Groupe 61265"
      transform="translate(0.25 0.25)"
      opacity="0.3">
      <Path
        id="Tracé_9355"
        data-name="Tracé 9355"
        d="M728.611,290.352l1.288-1.288a.778.778,0,0,0-.485-1.326l-2.39-.2-.2-2.39a.778.778,0,0,0-1.325-.485l-1.288,1.288a13.279,13.279,0,1,0,4.4,4.4Zm-3.082-2.03a.779.779,0,0,0,.71.71l1.375.116-1.861,1.861-2.03-.171-.171-2.03,1.861-1.861Zm-5.6,8.959a2.648,2.648,0,1,1-1.409-2.338l-1.787,1.788a.778.778,0,1,0,1.1,1.1l1.788-1.787a2.627,2.627,0,0,1,.308,1.237Zm-.277-3.469a4.207,4.207,0,1,0,1.1,1.1l2.222-2.222a7.323,7.323,0,1,1-1.1-1.1Zm5.922,11.759a11.723,11.723,0,1,1-2.5-18.486l-.878.878a.779.779,0,0,0-.228.53c0,.028,0,.057,0,.085l.1,1.242a8.878,8.878,0,1,0,2.665,2.665l1.242.1.065,0h.042l.05,0,.031,0,.049-.009.031-.008.047-.014.03-.011.045-.02.029-.013.044-.025.026-.015.045-.032.021-.015a.765.765,0,0,0,.061-.055l.878-.878a11.73,11.73,0,0,1-1.9,14.084Zm0,0"
        transform="translate(-704 -284)"
        fill="#007cff"
        stroke="#fff"
        stroke-width="0.5"
      />
    </G>
  </Svg>
);

export default SchedulingUnfocusedIcon;
