import React from 'react';
import {G, Path, Svg} from 'react-native-svg';
import {svgChannelIconHeight, svgChannelIconWidth} from '@src/theme/style';

const CostFocusedIcon = () => (
  <Svg
    id="Groupe_1357"
    data-name="Groupe 1357"
    width={svgChannelIconWidth(28.379)}
    height={svgChannelIconHeight(26.96)}
    viewBox="0 0 28.379 26.96">
    <G id="Groupe_1314" data-name="Groupe 1314" transform="translate(0 0)">
      <Path
        id="Tracé_9338"
        data-name="Tracé 9338"
        d="M1507.449,629.431h-1.759a.557.557,0,1,0,0,1.113h1.759a.557.557,0,1,0,0-1.113Z"
        transform="translate(-1488.979 -607.873)"
        fill="#007cff"
      />
      <Path
        id="Tracé_9339"
        data-name="Trac<PERSON> 9339"
        d="M1329.442,526.256a7.027,7.027,0,1,0,7.388,7.018A7.218,7.218,0,0,0,1329.442,526.256Zm0,12.924a5.913,5.913,0,1,1,6.216-5.905A6.074,6.074,0,0,1,1329.442,539.18Z"
        transform="translate(-1322.054 -513.332)"
        fill="#007cff"
      />
      <Path
        id="Rectangle_363"
        data-name="Rectangle 363"
        d="M1.466,0h0A1.465,1.465,0,0,1,2.931,1.465V7.446A1.465,1.465,0,0,1,1.466,8.912h0A1.465,1.465,0,0,1,0,7.446V1.466A1.466,1.466,0,0,1,1.466,0Z"
        transform="translate(21.813 13.759)"
        fill="#007cff"
      />
      <Path
        id="Tracé_9340"
        data-name="Tracé 9340"
        d="M1513.521,582.834h-1.173a.557.557,0,1,0,0,1.113h1.173a.557.557,0,1,0,0-1.113Z"
        transform="translate(-1495.051 -565.176)"
        fill="#007cff"
      />
      <Path
        id="Tracé_9341"
        data-name="Tracé 9341"
        d="M1452.85,410.433h-10.32a1.947,1.947,0,0,0-1.993,1.894v2.785a1.947,1.947,0,0,0,1.993,1.893h10.32a1.947,1.947,0,0,0,1.993-1.893v-2.785A1.947,1.947,0,0,0,1452.85,410.433Zm.822,4.679a.8.8,0,0,1-.822.78h-10.32a.8.8,0,0,1-.821-.78v-2.785a.8.8,0,0,1,.821-.78h10.32a.8.8,0,0,1,.822.78Z"
        transform="translate(-1430.1 -407.202)"
        fill="#007cff"
      />
      <Path
        id="Tracé_9342"
        data-name="Tracé 9342"
        d="M1383.663,557.784l-.229-.081v-.232a.586.586,0,1,0-1.172,0v.232l-.229.081a2.033,2.033,0,0,0,.051,3.886l1.146.375a.933.933,0,0,1,.659,1.057.972.972,0,0,1-.985.794h-.111a.979.979,0,0,1-1-.952.587.587,0,0,0-1.172,0,2.083,2.083,0,0,0,1.414,1.935l.229.081v.232a.586.586,0,1,0,1.171,0v-.232l.229-.081a2.033,2.033,0,0,0-.051-3.886l-1.146-.375a.933.933,0,0,1-.659-1.057.972.972,0,0,1,.985-.794h.111a.979.979,0,0,1,1,.951.587.587,0,0,0,1.172,0A2.082,2.082,0,0,0,1383.663,557.784Z"
        transform="translate(-1375.461 -541.39)"
        fill="#007cff"
      />
      <Path
        id="Tracé_9343"
        data-name="Tracé 9343"
        d="M1507.449,536.242h-1.759a.557.557,0,1,0,0,1.113h1.759a.557.557,0,1,0,0-1.113Z"
        transform="translate(-1488.979 -522.483)"
        fill="#007cff"
      />
      <Path
        id="Tracé_9344"
        data-name="Tracé 9344"
        d="M1418.383,371.827H1401.73a2.406,2.406,0,0,0-2.462,2.339v8.44a.587.587,0,0,0,1.172,0v-8.44a1.261,1.261,0,0,1,1.291-1.226h16.653a1.261,1.261,0,0,1,1.29,1.226v22.282a1.261,1.261,0,0,1-1.29,1.226h-11.229a.557.557,0,1,0,0,1.113h11.229a2.406,2.406,0,0,0,2.462-2.339V374.166A2.406,2.406,0,0,0,1418.383,371.827Z"
        transform="translate(-1392.466 -371.827)"
        fill="#007cff"
      />
    </G>
  </Svg>
);

export default CostFocusedIcon;
