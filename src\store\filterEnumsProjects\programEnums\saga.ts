import {call, put, takeEvery} from 'redux-saga/effects';
import baseRequest from '@src/api/request';
import {ProjectWebServices} from '@src/api/project';
import {
  getProgramEnums,
  getProgramEnumsWithError,
  getProgramEnumsWithSuccess,
} from '@src/store/filterEnumsProjects/programEnums/slice';

export function* handleProgramEnums(action: {
  payload: {
    types: string;
  };
}) {
  try {
    const {types} = action.payload;
    const {data} = yield call(
      baseRequest.get,
      ProjectWebServices.WS_GET_ENUMS_BY_TYPE,
      {params: {types}},
    );
    yield put(getProgramEnumsWithSuccess(data.PROGRAM));
  } catch (e) {
    yield getProgramEnumsWithError();
  }
}

export default function* saga() {
  yield takeEvery(getProgramEnums, handleProgramEnums);
}
