import React, { useEffect } from "react";
import Toast from "react-native-toast-message";

type ToastProps = {
  type: "error" | "info" | "success";
  message: string;
};

const ToastMessage: React.FC<ToastProps> = ({ type, message }) => {

  useEffect(() => {
    showToast();
  }, [type, message]);

  const showToast = () => {
    Toast.show({
      type: type,
      text1: message
    });
  };

  return null;
};

export default ToastMessage;

