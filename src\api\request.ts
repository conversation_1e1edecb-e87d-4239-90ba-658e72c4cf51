import axios from "axios";
import { AxiosConfigs } from "@src/constants/api";
import { AuthManager } from "@src/auth/AuthManager";
import { ProjectWebServices } from "@src/api/project";
import { UserWebServices } from "@src/api/user";
import Config from "react-native-config";

export const getToken = async () => {
  return await AuthManager.getAccessTokenAsync();
};

const defaultResponseInterceptorErrorHandler = (err: any) => {
  // re-map new error data model to the old one
  const data = [
    { message: err.response.data.detail, logref: err.response.data.title }
  ];
  const error = { ...err };
  error.response = { ...error.response, data };
  return Promise.reject(error);
};
export const makeRequest = (
  baseURL: string,
  responseInterceptorErrorHandler = defaultResponseInterceptorErrorHandler
) => {
  const request = axios.create({
    baseURL,
    timeout: AxiosConfigs.TIMEOUT
  });
  request.interceptors.request.use(
    async config => {
      const tempConf = { ...config };
      const token = await getToken();
      if (token != null) {
        tempConf.headers.Authorization = `Bearer ${token}`;
      }
      return tempConf;
    },
    error => Promise.reject(error)
  );

  request.interceptors.response.use(
    res => res,
    responseInterceptorErrorHandler
  );
  return request;
};

const baseRequest = makeRequest(
  `${AxiosConfigs.API_BASE_URL}${AxiosConfigs.PROXY_COLLAB}`
);
export default baseRequest;

export enum FileTypes {
  PROJECT_PICTURE,
  CLIENT_PICTURE,
}

export const getImageUrl = (fileUrl: string, fileType: string) => {
  return (
    `${AxiosConfigs.API_BASE_URL}${AxiosConfigs.PROXY_COLLAB}` +
    ProjectWebServices.WS_GET_IMAGE_FROM_SERVER +
    `?fileUrl=${fileUrl}&typeFile=${fileType}`
  );
};


export const userAvatarUrl = (finalUserId: string) => {
  return (
    `${AxiosConfigs.API_BASE_URL}${AxiosConfigs.PROXY_COLLAB}` +
    UserWebServices.GET_USER_AVATAR +
    "?userId=" +
    finalUserId
  );
};

export const STATIC_TOKEN_FOR_CURRENT_VERSION =
  Config.COLLAB_MOBILE_API_TOKEN;
