import reducer, {
  createUpdateDiscussion,
  createUpdateDiscussionWithError,
  createUpdateDiscussionWithSuccess,
  getDiscussionDetailsByObjectTypeAndObjectId,
  getDiscussionDetailsByObjectTypeAndObjectIdWithError,
  getDiscussionDetailsByObjectTypeAndObjectIdWithSuccess,
  getDiscussionUsers,
  getDiscussionUsersWithError,
  getDiscussionUsersWithSuccess,
  initialState,
} from '@src/store/discussion/slice';
import {MAPPING_NOTIFICATION_OBJECT_TYPE_WITH_DISCUSSION_OBJECT_TYPE} from '@src/constants/notification';

const discussionObjectExample = {
  id: 1,
  title: 'Example Object',
  description: 'This is an example object with nested data.',
  type: 'Example',
  objectId: 123,
  periodeOfData: {
    month: 9,
    year: 2023,
  },
  discussionId: 456,
  interactions: [],
};
describe('Discussion reducer', () => {
  it('should return the initial state', () => {
    expect(reducer(undefined, {type: undefined})).toEqual({
      loading: false,
      success: false,
      error: false,
      errorUsers: false,
      successUsers: false,
      errorCreateUpdate: false,
      successCreateUpdate: false,
      discussion: null,
      users: null,
    });
  });

  it('should handle getDiscussionDetailsByObjectTypeAndObjectId action', () => {
    const action = getDiscussionDetailsByObjectTypeAndObjectId({
      objectType:
        MAPPING_NOTIFICATION_OBJECT_TYPE_WITH_DISCUSSION_OBJECT_TYPE.objectTypeMapped,
      objectId: 2,
    });
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      loading: true,
      success: false,
      error: false,
    });
  });

  it('should handle getDiscussionDetailsByObjectTypeAndObjectIdWithSuccess action', () => {
    const action = getDiscussionDetailsByObjectTypeAndObjectIdWithSuccess({
      data: discussionObjectExample,
      type: 'type',
      objectId: 2,
    });
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      discussion: {
        ...action.payload.data,
        type: action.payload.type,
        objectId: action.payload.objectId,
      },
      loading: false,
      success: true,
      error: false,
    });
  });

  it('should handle getDiscussionDetailsByObjectTypeAndObjectIdWithError action', () => {
    const action = getDiscussionDetailsByObjectTypeAndObjectIdWithError();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      loading: false,
      success: false,
      error: true,
    });
  });

  it('should handle getDiscussionUsers action', () => {
    const action = getDiscussionUsers({
      idProject: 2,
    });
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      loading: true,
      errorUsers: false,
      successUsers: false,
    });
  });

  it('should handle getDiscussionUsersWithSuccess action', () => {
    const action = getDiscussionUsersWithSuccess({
      users: [],
    });
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      users: action.payload,
      loading: false,
      errorUsers: false,
      successUsers: true,
    });
  });

  it('should handle getDiscussionUsersWithSuccessWithError action', () => {
    const action = getDiscussionUsersWithError();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      loading: false,
      errorUsers: true,
      successUsers: false,
    });
  });

  it('should handle createUpdate action', () => {
    const action = createUpdateDiscussion({
      params: {
        type: 'type',
        projectId: 2,
        entityId: 2,
      },
      payload: {
        discussionId: 2,
        comment: 'text',
        listMemberTags: [],
      },
    });
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      loading: true,
      errorCreateUpdate: false,
      successCreateUpdate: false,
    });
  });

  it('should handle createUpdateWithSuccess action when the discussion is not null', () => {
    const action = createUpdateDiscussionWithSuccess({
      interactions: {
        id: 2,
        comment: 'text',
        userId: '2',
        listMemberTags: [],
        creationDate: 'date',
        displayName: 'display name',
        replies: [],
      },
    });
    const newState = reducer(
      {...initialState, discussion: discussionObjectExample},
      action,
    );

    expect(newState).toEqual({
      ...initialState,
      loading: false,
      successCreateUpdate: true,
      errorCreateUpdate: false,
      discussion: {
        ...discussionObjectExample,
        interactions: [action.payload.interactions],
      },
    });
  });

  it('should handle createUpdateWithSuccess action when the discussion is null', () => {
    const action = createUpdateDiscussionWithSuccess({
      interactions: {
        id: 2,
        comment: 'text',
        userId: '2',
        listMemberTags: [],
        creationDate: 'date',
        displayName: 'display name',
        replies: [],
      },
    });
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
    });
  });

  it('should handle createUpdateWithError action', () => {
    const action = createUpdateDiscussionWithError();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      users: null,
      loading: false,
      successCreateUpdate: false,
      errorCreateUpdate: true,
    });
  });
});
