import React from 'react';
import {renderWithProviders} from '@src/utils/utils-for-tests';
import NotificationsIconHeaderContainer from '@src/containers/headers/NotificationsIconHeaderContainer';

describe('Notifications Icon Header', () => {
  it('should render and match the snapshot', () => {
    const tree = renderWithProviders(<NotificationsIconHeaderContainer />, {});
    expect(tree).toMatchSnapshot();
  });
});
