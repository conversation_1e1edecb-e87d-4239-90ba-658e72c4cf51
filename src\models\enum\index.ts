export enum SECTOR {
  SECTOR = 'SECTOR',
  BU = 'BU',
}

export enum FILTER_TYPES {
  BU = 'BU',
  SECTOR = 'SECTOR',
  PROGRAM = 'PROGRAM',
}

export enum PRIVILEGE {
  EDIT = 'EDIT',
  ADD = 'ADD',
  DELETE = 'DELETE',
  SEE = 'SEE',
}

export enum MODULES {}

export enum FEATURES {}

export enum PLATFORM {
  COLLAB = 'COLLAB',
  CSP = 'CSP',
  JPASS = 'JPASS',
}

export enum NOTIFICATION_STATUS {
  READ = 'READ',
  UNREAD = 'UNREAD',
  DELETED = 'DELETED',
}

export enum NOTIFICATION_TYPE {
  KPI = 'KPI',
  REPORT = 'REPORT',
  ACTION = 'Action',
}
