import { View } from "react-native";
import React, { useCallback, useState } from "react";
import { CHANNEL_STATUS_VALUE, LINEAR_COLORS_FOR_EACH_STATUS_COLOR } from "@src/constants/channels/channelStatusColor";
import { Project } from "@src/models/project";
import ProjectCard from "@src/components/ProjectCard";
import { navigate } from "@src/utils/navigationUtils";
import { ROUTE_NAMES } from "@src/constants/navigation";
import { EmptyList, List } from "@src/components/List";
import { wait } from "@src/utils/timeUtils";

type ListProjectsProps = {
  projects: Project[];
  page: number;
  updateCurrentPageProjects: (arg: number) => void;
  isLastPage: boolean;
  handleRefreshPage: () => void;
  isLoading: boolean;
  emptyListMessage: string
};

const ListProjects = (props: ListProjectsProps) => {
  const {
    projects,
    page,
    isLastPage,
    updateCurrentPageProjects,
    handleRefreshPage,
    isLoading,
    emptyListMessage
  } = props;
  const onProjectClicked = (id: number, reportingDate: string) => {
    navigate(ROUTE_NAMES.PROJECT_HEALTH_DETAILS, {
      projectId: id,
      reportingDate
    });
  };
  const renderItem = (props: { item: Project }) => {
    const { item } = props;
    return (
      <ProjectCard
        onClick={() => {
          onProjectClicked(item.id, item.reportingDate);
        }}
        clientLogo={item.clientLogo}
        projectPictureLink={item.picture}
        title={item.name}
        reference={item.number}
        colorName={CHANNEL_STATUS_VALUE[item.overallStatus]}
        linearColors={LINEAR_COLORS_FOR_EACH_STATUS_COLOR[item.overallStatus]}
      />
    );
  };


  const [isRefreshing, setIsRefreshing] = useState(false);

  const onRefresh = useCallback(() => {
    setIsRefreshing(true);
    wait(2000).then(() => setIsRefreshing(false));
    handleRefreshPage();
  }, []);
  const keyExtractor = useCallback((item: any, i: number) => `${i}-${item.id}`, []);
  return (
    <View style={{ minHeight: "94%", paddingBottom: 250 }}>
      <List
        showsVerticalScrollIndicator={false}
        refreshing={isRefreshing}
        onRefresh={onRefresh}
        data={projects}
        extraData={isRefreshing}
        keyExtractor={keyExtractor}
        renderItem={renderItem}
        ListEmptyComponent={<EmptyList isLoading={isLoading} emptyListMessage={emptyListMessage} />}
        onEndReached={() => {
          if (!isLoading) {
            console.log("the end is reached of the current items displayed in the flat list ! , and this the value of loading ", isLoading);
            if (isLastPage) {
              // TODO
              console.log("the end is reached !");
            } else {
              console.log("the end is not reached !");
              let newPage = page;
              updateCurrentPageProjects(++newPage);
            }
          }
        }}
      />
    </View>
  );
};

export default React.memo(ListProjects);
