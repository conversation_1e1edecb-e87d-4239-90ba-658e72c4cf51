import {StyleSheet} from 'react-native';
import typography from '@src/theme/fonts';
import {COMMON_COLORS} from '@src/theme/colors';

const style = StyleSheet.create({
  MMAD: {
    fontSize: 14,
    fontFamily: typography.fontFamily.montserratRegular,
    color: COMMON_COLORS.BLUE_20,
  },
  valueHeadCountJESA: {
    color: '#003493',
    fontSize: typography.fontSizes.sm,
    fontFamily: typography.fontFamily.montserratSemiBold,
    fontWeight: 'bold',
  },
});

export default style;
