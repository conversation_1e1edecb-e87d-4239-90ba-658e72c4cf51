import React, {memo, useEffect} from 'react';
import {
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import styles from '@src/components/NotificationBottomModal/styles';
import {NotificationModel} from '@src/models/notification';
import {MobileAlertType} from '@src/constants/notification';
import Modal from 'react-native-modal';
import {globalStyles} from '@src/theme/style';

type FilterBottomModalProps = {
  children?: React.ReactNode;
  onClose?: () => void;
  visible?: boolean;
  notification: NotificationModel | null;
  handleMarkAsRead: (notificationId: number) => void;
};

export const NotificationBottomModal = (props: FilterBottomModalProps) => {
  const {visible, onClose, notification, handleMarkAsRead} = props;

  useEffect(() => {
    if (!onClose) {
      return;
    }
  }, [onClose]);
  const title = notification?.title;
  const message = notification?.message;
  const notReadyYetMessage =
    notification?.mobileAlertType === MobileAlertType.NOT_READY_YET
      ? ' Not ready yet '
      : null;

  if (notification === null) {
    return null;
  }

  return (
    <>
      <Modal
        statusBarTranslucent={false}
        onDismiss={onClose}
        collapsable={true}
        onBackdropPress={onClose}
        onBackButtonPress={onClose}
        isVisible={visible}
        style={styles.modal}>
        <View style={styles.container}>
          <TouchableWithoutFeedback
            onPress={() => {
              if (onClose) {
                onClose();
              }
            }}>
            <View style={globalStyles.blurViewModal} />
          </TouchableWithoutFeedback>
          <View style={styles.content}>
            <View style={styles.line} />
            <Text style={styles.title}>{title}</Text>
            <Text style={styles.notificationContent}>{message}</Text>
            {notification?.mobileAlertType ===
              MobileAlertType.NOT_READY_YET && (
              <Text style={styles.notificationContent}>
                {notReadyYetMessage}
              </Text>
            )}
            <TouchableOpacity
              style={{...styles.button}}
              onPress={() => {
                if (onClose) {
                  onClose();
                  handleMarkAsRead(notification?.id!!);
                }
              }}>
              <Text style={styles.buttonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
      {/*{visible && (*/}
      {/*  <BlurView*/}
      {/*    blurAmount={1}*/}
      {/*    blurType={'dark'}*/}
      {/*    blurRadius={12}*/}
      {/*    style={bottomSheetStyles.blurView}*/}
      {/*  />*/}
      {/*)}*/}
    </>
  );
};

export default memo(NotificationBottomModal);
