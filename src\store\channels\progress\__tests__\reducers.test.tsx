import reducer, {
  cleanUpProgress,
  getProgressFeatures,
  getProgressFeaturesWithError,
  getProgressFeaturesWithSuccess,
  initialState,
} from '@src/store/channels/progress/slice';
import {ProgressChannelSummary} from '@src/models/channelsSummary/progressChannelSummary';

const features: ProgressChannelSummary = {
  channelStatus: 'PRIMARY',
};
describe('Progress reducer', () => {
  it('should return the initial state', () => {
    expect(reducer(undefined, {type: undefined})).toEqual(initialState);
  });

  it('should handle getProgressFeatures action', () => {
    const action = getProgressFeatures();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      loading: true,
      error: false,
      success: false,
    });
  });

  it('should handle getProgressFeaturesWithSuccess action when the channel status is not null', () => {
    const action = getProgressFeaturesWithSuccess({features});
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      features: action.payload.features,
      channelStatus: action.payload.features.channelStatus,
      loading: false,
      error: false,
      success: true,
    });
  });

  it('should handle getProgressFeaturesWithSuccess action when the channel status is null', () => {
    const action = getProgressFeaturesWithSuccess({
      features: {
        ...features,
        channelStatus: null,
      },
    });
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      features: action.payload.features,
      loading: false,
      error: false,
      success: true,
    });
  });

  it('should handle getProgressFeaturesWithError action', () => {
    const action = getProgressFeaturesWithError();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      loading: false,
      error: true,
      success: false,
    });
  });

  it('should handle cleanUp action', () => {
    const action = cleanUpProgress();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
    });
  });
});
