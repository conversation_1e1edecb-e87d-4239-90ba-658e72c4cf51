import {RootState} from '@src/store/store';
import {CashFlowSliceType} from '@src/store/channels/cashFlow/slice';
import {cashFlowSelector} from '@src/store/channels/cashFlow/selectors';

describe('CashFlowSelector', () => {
  it('should select the cash flow state from the root state', () => {
    // Arrange
    const cashFlowState: CashFlowSliceType = {
      features: null,
      channelStatus: 'PRIMARY',
      loading: false,
      error: false,
      success: false,
    };

    // @ts-ignore
    const mockRootState: RootState = {
      cashFlowChannel: cashFlowState,
    };

    // Act
    const selectedState = cashFlowSelector(mockRootState);

    // Assert
    expect(selectedState).toEqual(cashFlowState);
  });
});
