import {RootState} from '@src/store/store';
import {RiskSliceType} from '@src/store/channels/risk/slice';
import {riskSelector} from '@src/store/channels/risk/selectors';

describe('RiskSelector', () => {
  it('should select the risk state from the root state', () => {
    // Arrange
    const riskState: RiskSliceType = {
      features: null,
      channelStatus: 'PRIMARY',
      loading: false,
      error: false,
      success: false,
    };

    // @ts-ignore
    const mockRootState: RootState = {
      riskChannel: riskState,
    };

    // Act
    const selectedState = riskSelector(mockRootState);

    // Assert
    expect(selectedState).toEqual(riskState);
  });
});
