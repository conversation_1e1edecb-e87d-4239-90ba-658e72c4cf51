import { useSelector } from "react-redux";
import { authenticationSelector } from "@src/store/authentication/selectors";
import { projectSelector } from "@src/store/project/selectors";

export const usePrivilegeByModule = (module: string, privilege: string) => {
  const { user } = useSelector(authenticationSelector);
  const { featurePrivileges } = useSelector(projectSelector);
  if (featurePrivileges !== null && user !== null) {
    return (
      user.admin ||
      featurePrivileges?.some(
        profilePrivilege =>
          profilePrivilege?.moduleCode === module &&
          profilePrivilege.privilegeCode === privilege
      )
    );
  } else {
    return false;
  }
};
