import {Text, View} from 'react-native';
import React from 'react';
import layout from '@src/theme/layout';
import style from './style';
import {format} from 'date-fns';
import {CalendarDiscussionIcon} from '@src/svg/CalendarDiscussionIcon';

export const ForecastSOP = (props: {value: string | null}) => {
  const finalValue =
    !props.value || props.value === '-'
      ? '-'
      : format(new Date(props.value), 'dd MMM, yyyy');
  return (
    <View style={layout.rowCenter}>
      <CalendarDiscussionIcon />
      <Text style={style.date}> {finalValue}</Text>
    </View>
  );
};
