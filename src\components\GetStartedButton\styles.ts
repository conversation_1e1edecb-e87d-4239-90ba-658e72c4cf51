import {StyleSheet} from 'react-native';
import {windowHeight, windowWidth} from '@src/constants';
import typography from '@src/theme/fonts';
import {correspondentHeight, correspondentWidth} from '@src/utils/imageUtils';
import {COMMON_COLORS} from '@src/theme/colors';

const style = StyleSheet.create({
  container: {
    backgroundColor: 'blue',
    borderRadius: 22,
    flexDirection: 'row',
    alignSelf: 'center',
    width: windowWidth * 0.5,
    justifyContent: 'space-around',
    alignItems: 'center',
    height: windowHeight * 0.065,
    paddingRight: 6,
    paddingStart: windowWidth * 0.08,
  },
  text: {
    color: COMMON_COLORS.WHITE,
    fontWeight: 'bold',
    textAlign: 'center',
    fontSize: typography.fontSizes.md,
    fontFamily: typography.fontFamily.montserratBold,
  },
  arrow: {
    height: correspondentHeight(36),
    width: correspondentWidth(36),
  },
});
export default style;
