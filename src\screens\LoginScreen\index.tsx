import { SafeAreaView, Text, View } from "react-native";
import React from "react";
import styles from "./style";
import { TEST_IDS, TITLES } from "@src/constants/strings";
import GetStartedButton from "@src/components/GetStartedButton";
import { AuthManager } from "@src/auth/AuthManager";
import { IllustrationLogin } from "@src/svg/IllustrationLogin";
import { CollabLogo } from "@src/svg/CollabLogo";
import { login } from "@src/store/authentication/slice";
import { useDispatch } from "react-redux";
import { setItem } from "@src/utils/storageUtils";
import { USER_TOKEN_KEY } from "@src/constants/auth";
import { JESALogo } from "@src/svg/JESALogo";

const LoginScreen = () => {
  const dispatch = useDispatch();
  const handleButtonClick = async () => {
    try {
      const token = await AuthManager.signInAsync();
      if (token) {
        dispatch(login({ userToken: token }));
        await setItem(USER_TOKEN_KEY, token);
      } else {
        console.log("Error in login !");
      }
    } catch (_) {
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <CollabLogo style={styles.logoImage} />

      <View>
        <Text style={styles.title}>{TITLES.WELCOME_BACK}</Text>
        <Text style={styles.subTitle}>
          {TITLES.PLEASE_USE_JESA_FOR_YOUR_CREDENTIALS}
        </Text>
      </View>

      <IllustrationLogin style={styles.vectorImage} />

      <GetStartedButton
        testID={TEST_IDS.GET_STARTED_BUTTON}
        onClick={handleButtonClick}
      />
      <JESALogo style={styles.jesa_logo} />
    </SafeAreaView>
  );
};

export default LoginScreen;
