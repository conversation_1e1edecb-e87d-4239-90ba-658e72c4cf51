import React from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {setIsApplicationVersionUpdated} from '@src/store/global/slice';
import {globalSelector} from '@src/store/global/selectors';

const useCheckApplicationVersion = (
  setIsVersionUpdated: (value: boolean) => void,
) => {
  const dispatch = useDispatch();
  const {isApplicationVersionUpdated} = useSelector(globalSelector);
  React.useEffect(() => {
    dispatch(setIsApplicationVersionUpdated());
    if (isApplicationVersionUpdated) {
      setIsVersionUpdated(isApplicationVersionUpdated);
    }
  }, []);
};

export default useCheckApplicationVersion;
