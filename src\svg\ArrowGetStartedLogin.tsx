import {Circle, G, Path, Svg} from 'react-native-svg';
import React from 'react';

export const ArrowGetStartedLogin = (props: {style: {}}) => {
  return (
    <Svg style={props.style} viewBox="0 0 36 36">
      <G
        id="Groupe_61213"
        data-name="Groupe 61213"
        transform="translate(-1199.852 -617)">
        <Circle
          id="Ellipse_3113"
          data-name="Ellipse 3113"
          cx="18"
          cy="18"
          r="18"
          transform="translate(1199.852 617)"
          fill="#fff"
        />
        <Path
          id="Go"
          d="M18.706,10.9,4.049,10.891a1.049,1.049,0,0,0,0,2.1l14.767.006a1.769,1.769,0,0,1-.264.307l-6.767,6.257A1.048,1.048,0,1,0,13.209,21.1l6.767-6.256a3.844,3.844,0,0,0,.055-5.594L13.325,2.8a1.048,1.048,0,1,0-1.453,1.511l6.7,6.446A1.859,1.859,0,0,1,18.706,10.9Z"
          transform="translate(1205.747 623.059)"
          fill="#003493"
          fill-rule="evenodd"
        />
      </G>
    </Svg>
  );
};
