import {G, Path, Svg, Text, TSpan} from 'react-native-svg';
import React from 'react';
import {svgChannelIconHeight, svgChannelIconWidth} from '@src/theme/style';

export const JESALogo = (props: {style: {}}) => {
  return (
    <Svg
      style={props.style}
      height={svgChannelIconHeight(28.763)}
      width={svgChannelIconWidth(162)}
      viewBox="0 0 162 28.763">
      <G
        id="JESA_Logo"
        data-name="JESA Logo"
        transform="translate(22.447 -0.947)"
        opacity="0.1">
        <G id="Jesa" transform="translate(23.553 0.947)">
          <Path
            id="Tracé_7"
            data-name="Tracé 7"
            d="M924.2,270.26l-2.155-5.283c-.044.088-.069.131-.087.175q-2.133,5.272-4.261,10.544a.248.248,0,0,1-.278.18c-1.919-.006-3.839,0-5.758,0h-.214c.037-.083.059-.138.087-.192q3.572-7.146,7.14-14.295a.367.367,0,0,1,.4-.236q3.085.013,6.169,0a.269.269,0,0,1,.288.172q3.578,7.182,7.165,14.359c.026.051.048.1.083.179-.081,0-.146.01-.211.01q-2.963,0-5.926,0a.233.233,0,0,1-.265-.171c-.269-.691-.557-1.376-.827-2.066a.266.266,0,0,0-.3-.2c-1.764.007-3.527,0-5.291,0h-.245c.032-.088.051-.146.074-.2.385-.937.774-1.872,1.153-2.811a.25.25,0,0,1,.283-.174c.909.007,1.82,0,2.729,0Z"
            transform="translate(-865.722 -261.15)"
            fill="#003493"
          />
          <Path
            id="Tracé_8"
            data-name="Tracé 8"
            d="M635.55,270.989c.458-.023.891-.047,1.326-.067,1.349-.062,2.7-.12,4.048-.186.151-.007.213.021.243.169a2.554,2.554,0,0,0,1.876,2.049,5,5,0,0,0,1.7.187,3.214,3.214,0,0,0,1.42-.309,2.566,2.566,0,0,0,.66-.49.957.957,0,0,0-.067-1.409,2.374,2.374,0,0,0-.937-.542,18.089,18.089,0,0,0-2.607-.58,19.6,19.6,0,0,1-4.2-1.126,5.447,5.447,0,0,1-1.9-1.236,2.945,2.945,0,0,1-.137-3.975,5.2,5.2,0,0,1,2.389-1.606,10.468,10.468,0,0,1,2.664-.56,23.3,23.3,0,0,1,3.116-.09,12.958,12.958,0,0,1,3.984.66,4.346,4.346,0,0,1,3.014,3.437.839.839,0,0,1,0,.125l-5.49.245c-.061-.171-.109-.323-.169-.471a1.938,1.938,0,0,0-1.261-1.159,4.487,4.487,0,0,0-2.64-.055,1.269,1.269,0,0,0-.8.6.61.61,0,0,0,.2.869,2.726,2.726,0,0,0,.845.375c.791.176,1.594.306,2.392.454a27.435,27.435,0,0,1,4.27,1.026,6.179,6.179,0,0,1,2.092,1.165,3.388,3.388,0,0,1,.776,4.259,5.489,5.489,0,0,1-3.034,2.46,10.487,10.487,0,0,1-3.154.648c-.944.051-1.892.077-2.837.056a14.041,14.041,0,0,1-3.951-.559,5.1,5.1,0,0,1-3.792-4.177C635.571,271.121,635.563,271.065,635.55,270.989Z"
            transform="translate(-605.76 -261.198)"
            fill="#003493"
          />
          <Path
            id="Tracé_9"
            data-name="Tracé 9"
            d="M430.72,272.908c0,.074.01.13.01.185,0,.929,0,1.858,0,2.786,0,.147-.038.195-.206.195q-5.757-.007-11.515,0c-.055,0-.11,0-.182-.009V261.32h11.9v2.964H430.5c-1.851,0-3.7,0-5.552,0-.181,0-.229.045-.228.209q.014,1.205,0,2.41c0,.162.055.2.221.2q2.767-.007,5.533,0h.24v2.8h-.229c-1.845,0-3.689,0-5.533,0-.176,0-.234.037-.232.205q.014,1.307,0,2.616c0,.155.042.2.217.2,1.851-.006,3.7,0,5.552,0C430.561,272.908,430.634,272.908,430.72,272.908Z"
            transform="translate(-401.616 -261.31)"
            fill="#003493"
          />
          <Path
            id="Tracé_10"
            data-name="Tracé 10"
            d="M124.655,271.189l1.432-.07c1.368-.063,2.735-.122,4.1-.191.144-.007.162.04.174.149a8.175,8.175,0,0,0,.156,1.1,1.53,1.53,0,0,0,1.523,1.1,4.288,4.288,0,0,0,.8-.053,1.435,1.435,0,0,0,1.189-1.18,4.909,4.909,0,0,0,.126-1.047c.01-2.921.005-5.842.005-8.762V262H140.1c0,.064.009.125.009.186,0,2.6.007,5.2,0,7.805a12.906,12.906,0,0,1-.255,2.579,4.354,4.354,0,0,1-2.3,2.966,8.333,8.333,0,0,1-3.319,1,18.708,18.708,0,0,1-4.19-.072,8.014,8.014,0,0,1-2.285-.6,4.832,4.832,0,0,1-2.951-3.574c-.067-.318-.1-.641-.148-.962A1.05,1.05,0,0,1,124.655,271.189Z"
            transform="translate(-124.653 -261.949)"
            fill="#003493"
          />
        </G>
        <Text
          id="Project_Management_platform"
          data-name="Project Management platform"
          transform="translate(-22.447 26.71)"
          fill="#003493"
          font-size="12"
          font-family="Roboto-Medium, Roboto"
          font-weight="500">
          <TSpan x="0" y="0">
            Project Management platform
          </TSpan>
        </Text>
      </G>
    </Svg>
  );
};
