import {RootState} from '@src/store/store';
import {ChangeManagementSliceType} from '@src/store/channels/changeManagement/slice';
import {changeManagementSelector} from '@src/store/channels/changeManagement/selectors';

describe('ChangeManagementSelector', () => {
  it('should select the change management state from the root state', () => {
    // Arrange
    const changeManagementState: ChangeManagementSliceType = {
      features: null,
      channelStatus: 'PRIMARY',
      loading: false,
      error: false,
      success: false,
    };

    // @ts-ignore
    const mockRootState: RootState = {
      changeManagementChannel: changeManagementState,
    };

    // Act
    const selectedState = changeManagementSelector(mockRootState);

    // Assert
    expect(selectedState).toEqual(changeManagementState);
  });
});
