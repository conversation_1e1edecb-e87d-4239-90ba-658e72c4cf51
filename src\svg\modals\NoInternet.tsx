import {G, Path, Svg} from 'react-native-svg';
import React from 'react';

export const NoInternet = (props: {style: {}}) => {
  return (
    <Svg
      style={props.style}
      width="100.441"
      height="108.015"
      viewBox="0 0 100.441 108.015">
      <G id="Illustration" transform="translate(18973.131 -91.709)">
        <Path
          id="Tracé_53099"
          data-name="Tracé 53099"
          d="M349.226,69.082c.462.917,1.053,1.762,1.431,2.728a12.838,12.838,0,0,1,.837,3.589c.325,3.728-1.108,6.838-3.384,9.64a38.963,38.963,0,0,0-3.327,4.087,24.115,24.115,0,0,0-3.129,8.189,30.345,30.345,0,0,0-.406,7.106,33.506,33.506,0,0,0,.937,6.191,55.251,55.251,0,0,0,3.522,10c1.035,2.276,2.3,4.429,3.444,6.643a23.246,23.246,0,0,1,2.3,6.668,15.173,15.173,0,0,1-2.016,10.949,21.794,21.794,0,0,1-7.751,7.181,38.113,38.113,0,0,1-10.58,4.156,39.69,39.69,0,0,1-6.732,1.1c-1.306.088-2.61.24-3.921.209a26.9,26.9,0,0,1-10.754-2.43,43.379,43.379,0,0,1-7.94-4.862c-1.865-1.407-3.649-2.92-5.411-4.459-2.535-2.214-5.034-4.477-7.735-6.491a23.247,23.247,0,0,0-6.9-3.831,11.187,11.187,0,0,0-8.667.626,8.7,8.7,0,0,1-9.718-.914,17.479,17.479,0,0,1-4.592-5.541,26.422,26.422,0,0,1-2.869-8.638,28.143,28.143,0,0,1-.153-6.836,22.394,22.394,0,0,1,2.693-8.8,20.6,20.6,0,0,1,4.726-5.739,39.814,39.814,0,0,1,5.335-3.517,49.369,49.369,0,0,0,5.946-4.225,21.2,21.2,0,0,0,5.815-7.051,13.094,13.094,0,0,0,1.121-4.2,17.862,17.862,0,0,0-.743-6.415,70.673,70.673,0,0,1-1.816-7.169,11.393,11.393,0,0,1,.42-6.146,11.765,11.765,0,0,1,3.188-4.162,26.626,26.626,0,0,1,5.609-3.8,30.334,30.334,0,0,1,6.714-2.543,32.056,32.056,0,0,1,5.475-.817,20.684,20.684,0,0,1,7.947,1.027,17.892,17.892,0,0,1,7.986,5.345c1.093,1.258,2.137,2.564,3.329,3.725a23.718,23.718,0,0,0,4.948,3.855,10.43,10.43,0,0,0,8.458.919,28.442,28.442,0,0,0,2.911-1.029,8,8,0,0,1,6.229.07,16.138,16.138,0,0,1,5.378,3.5c.121.115.261.221.273.411a3.673,3.673,0,0,0-2.627,2.733c-.14.561.109.906.785,1.136.617.209,1,.108,1.2-.443A2.556,2.556,0,0,1,349.226,69.082ZM320.71,143.007a4.273,4.273,0,0,0,4.412-3.324c.168-.729.257-1.476.379-2.215q.761-4.6,1.519-9.209c.583-3.569,1.156-7.14,1.74-10.709.506-3.088,1.026-6.174,1.531-9.262.62-3.791,1.227-7.585,1.849-11.377.655-3.994,1.325-7.986,1.983-11.979.67-4.068,1.317-8.14,2.007-12.2a4.312,4.312,0,0,0-3.454-5.008c-2.568-.436-5.142-.842-7.712-1.267q-5.99-.99-11.978-1.987c-3.846-.634-7.708-1.181-11.535-1.913a4.437,4.437,0,0,0-5.5,3.9c-.053.763-.241,1.516-.366,2.275q-.763,4.632-1.526,9.263-.883,5.381-1.764,10.763-.923,5.631-1.845,11.263-.816,4.993-1.629,9.987-.929,5.659-1.866,11.317c-.626,3.809-1.224,7.623-1.878,11.427a4.344,4.344,0,0,0,3.662,5.062c2.187.323,4.366.7,6.548,1.062q6.019.988,12.038,1.98c3.125.514,6.247,1.041,9.374,1.537C318.11,142.606,319.505,142.906,320.71,143.007Zm-51.1-29.295a6.939,6.939,0,0,0-.593.466,7.04,7.04,0,0,0-1.712,2.937,1.2,1.2,0,0,0,.254,1.206,1.294,1.294,0,0,0,2.281-.455,4.709,4.709,0,0,1,1.54-2.236,2.313,2.313,0,0,1,2.533-.477,27.877,27.877,0,0,0,3.246,1.1,1.074,1.074,0,0,0,1.342-.716,1.156,1.156,0,0,0-.869-1.6q-4.707-1.621-9.416-3.234-3.752-1.285-7.507-2.561a1.188,1.188,0,0,0-.856,2.217,1.621,1.621,0,0,0,.37.134c.433.083.485.241.243.633a13.848,13.848,0,0,0-1.495,3.449,1.322,1.322,0,1,0,2.517.773,15,15,0,0,1,1.092-2.586c.675-1.247.66-1.236,1.977-.8.43.144.389.293.169.6a10.646,10.646,0,0,0-1.6,3.332,1.311,1.311,0,0,0,1.537,1.684,1.428,1.428,0,0,0,1.082-1.171,8.176,8.176,0,0,1,1.686-3.023c.139-.163.254-.362.556-.237C268.457,113.344,268.944,113.484,269.614,113.712Zm-5.463-6.876c.766.261,1.455.451,2.106.729a1.712,1.712,0,0,0,1.874-.139,4.99,4.99,0,0,1,1.223-.555,12.879,12.879,0,0,1,8.157-.387,1.329,1.329,0,1,0,.688-2.553,15.347,15.347,0,0,0-7.175-.292A15.59,15.59,0,0,0,264.151,106.836Zm5.389,1.834c.485.168.834.291,1.185.411a32.822,32.822,0,0,0,5.482,1.6c.929.144,1.629-.122,1.83-.851.23-.83-.179-1.457-1.146-1.731a10.509,10.509,0,0,0-4.446-.284A9.014,9.014,0,0,0,269.54,108.669Zm6.718,10.366A2.219,2.219,0,0,0,274,116.8a2.258,2.258,0,0,0-.011,4.515A2.218,2.218,0,0,0,276.258,119.035ZM343.23,73.641a1.4,1.4,0,0,0,1.392,1.446,1.431,1.431,0,1,0,.03-2.861A1.387,1.387,0,0,0,343.23,73.641Z"
          transform="translate(-19228.709 42.2)"
          fill="#dde8fe"
        />
        <Path
          id="Tracé_53100"
          data-name="Tracé 53100"
          d="M1052.374,167.967a2.556,2.556,0,0,0-2.216,1.722c-.2.551-.579.652-1.2.443-.676-.229-.925-.575-.785-1.136a3.673,3.673,0,0,1,2.627-2.733,3.562,3.562,0,0,0,1.516-.519,1.216,1.216,0,0,0,.113-2.07,1.975,1.975,0,0,0-2.438-.2c-.608.367-1,.341-1.4-.094a1.053,1.053,0,0,1,.007-1.477,2.448,2.448,0,0,1,1.853-.926,5,5,0,0,1,4.085,1.635,2.9,2.9,0,0,1-.563,4.558A9.938,9.938,0,0,1,1052.374,167.967Z"
          transform="translate(-19931.857 -56.685)"
          fill="#699bfe"
        />
        <Path
          id="Tracé_53101"
          data-name="Tracé 53101"
          d="M552.2,244.788c-1.2-.1-2.6-.4-4.006-.623-3.127-.5-6.25-1.023-9.375-1.537q-6.019-.99-12.038-1.98c-2.182-.358-4.361-.738-6.548-1.062a4.344,4.344,0,0,1-3.662-5.062c.654-3.8,1.252-7.618,1.878-11.427q.93-5.659,1.866-11.317.819-4.993,1.629-9.987.921-5.632,1.845-11.263.882-5.382,1.764-10.763.761-4.632,1.526-9.263c.125-.758.313-1.512.366-2.275a4.437,4.437,0,0,1,5.5-3.9c3.827.732,7.689,1.279,11.535,1.913q5.99.987,11.978,1.987c2.57.425,5.143.831,7.712,1.267a4.312,4.312,0,0,1,3.454,5.008c-.69,4.065-1.337,8.137-2.007,12.2-.658,3.993-1.328,7.985-1.983,11.979-.622,3.791-1.229,7.585-1.849,11.377-.505,3.088-1.025,6.174-1.531,9.262-.584,3.569-1.157,7.14-1.74,10.709q-.753,4.605-1.519,9.209c-.122.739-.21,1.487-.379,2.215A4.273,4.273,0,0,1,552.2,244.788Zm-3.053-47.379a2.8,2.8,0,0,0,.192,1.1c.344.877,1.061,1,1.6.241a3.73,3.73,0,0,0,.555-3.463.9.9,0,0,0-.707-.72.954.954,0,0,0-.9.473A4.143,4.143,0,0,0,549.146,197.409Zm-13.783-2.355a2.541,2.541,0,0,0,.284,1.414.758.758,0,0,0,1.3.2,3.77,3.77,0,0,0,.675-3.774c-.361-.691-.892-.78-1.431-.214A3.409,3.409,0,0,0,535.363,195.054Z"
          transform="translate(-19460.199 -59.581)"
          fill="#007eff"
        />
        <Path
          id="Tracé_53102"
          data-name="Tracé 53102"
          d="M295.7,574.2c-.669-.228-1.157-.368-1.622-.561-.3-.125-.417.075-.556.237a8.177,8.177,0,0,0-1.686,3.023,1.428,1.428,0,0,1-1.082,1.171,1.311,1.311,0,0,1-1.537-1.684,10.646,10.646,0,0,1,1.6-3.332c.22-.307.262-.456-.169-.6-1.317-.44-1.3-.452-1.977.8a14.992,14.992,0,0,0-1.092,2.586,1.322,1.322,0,1,1-2.517-.773,13.851,13.851,0,0,1,1.495-3.449c.243-.392.19-.55-.243-.633a1.623,1.623,0,0,1-.37-.134,1.188,1.188,0,0,1,.856-2.217q3.757,1.269,7.507,2.561,4.709,1.614,9.416,3.234a1.156,1.156,0,0,1,.869,1.6,1.074,1.074,0,0,1-1.342.716,27.877,27.877,0,0,1-3.246-1.1,2.313,2.313,0,0,0-2.532.477,4.709,4.709,0,0,0-1.54,2.236,1.294,1.294,0,0,1-2.281.455,1.2,1.2,0,0,1-.254-1.206,7.04,7.04,0,0,1,1.712-2.937A6.933,6.933,0,0,1,295.7,574.2Z"
          transform="translate(-19254.801 -418.292)"
          fill="#699bfe"
        />
        <Path
          id="Tracé_53103"
          data-name="Tracé 53103"
          d="M331.56,530.229a15.59,15.59,0,0,1,6.873-3.2,15.346,15.346,0,0,1,7.175.292,1.329,1.329,0,1,1-.688,2.553,12.879,12.879,0,0,0-8.157.387,4.991,4.991,0,0,0-1.223.555,1.712,1.712,0,0,1-1.874.139C333.015,530.68,332.326,530.49,331.56,530.229Z"
          transform="translate(-19296.119 -381.193)"
          fill="#699bfe"
        />
        <Path
          id="Tracé_53104"
          data-name="Tracé 53104"
          d="M379.332,566.272a9.012,9.012,0,0,1,2.906-.854,10.51,10.51,0,0,1,4.446.284c.966.274,1.375.9,1.146,1.731-.2.728-.9.995-1.83.85a32.827,32.827,0,0,1-5.482-1.6C380.166,566.564,379.817,566.44,379.332,566.272Z"
          transform="translate(-19338.502 -415.402)"
          fill="#699bfe"
        />
        <Path
          id="Tracé_53105"
          data-name="Tracé 53105"
          d="M403.406,648.23a2.218,2.218,0,0,1-2.268,2.282,2.257,2.257,0,0,1,.011-4.515A2.219,2.219,0,0,1,403.406,648.23Z"
          transform="translate(-19355.857 -486.995)"
          fill="#699bfe"
        />
        <Path
          id="Tracé_53106"
          data-name="Tracé 53106"
          d="M1032.521,252.292a1.387,1.387,0,0,1,1.422-1.416,1.431,1.431,0,1,1-.03,2.861A1.4,1.4,0,0,1,1032.521,252.292Z"
          transform="translate(-19918.002 -136.45)"
          fill="#699bfe"
        />
        <Path
          id="Tracé_53107"
          data-name="Tracé 53107"
          d="M805.831,435.9a4.143,4.143,0,0,1,.745-2.369.954.954,0,0,1,.9-.473.9.9,0,0,1,.707.72,3.731,3.731,0,0,1-.556,3.463c-.542.761-1.259.636-1.6-.241A2.8,2.8,0,0,1,805.831,435.9Z"
          transform="translate(-19716.885 -298.068)"
          fill="#fefefe"
        />
        <Path
          id="Tracé_53108"
          data-name="Tracé 53108"
          d="M683.639,415.809a3.409,3.409,0,0,1,.829-2.379c.538-.566,1.069-.477,1.431.214a3.77,3.77,0,0,1-.675,3.774.758.758,0,0,1-1.3-.2A2.541,2.541,0,0,1,683.639,415.809Z"
          transform="translate(-19608.477 -280.336)"
          fill="#fefefe"
        />
        <Path
          id="Tracé_53109"
          data-name="Tracé 53109"
          d="M708.4,527.323a6.181,6.181,0,0,1,4.629,2.315c.156.188.293.381.078.588-.243.234-.446.071-.619-.126a5.675,5.675,0,0,0-2.458-1.732,5.241,5.241,0,0,0-4.352.388c-.148.081-.3.154-.449.236-.2.111-.411.155-.542-.072a.382.382,0,0,1,.173-.57A6.981,6.981,0,0,1,708.4,527.323Z"
          transform="translate(-19628.207 -380.443)"
          fill="#fefefe"
        />
        <Path
          id="Tracé_53156"
          data-name="Tracé 53156"
          d="M865.59,424.5c-.522.171-.9.333-1.3.421a4.793,4.793,0,0,0-4.015,3.731c-.08.334-.216.654-.327.981-.06.178-.058.448-.319.421-.226-.023-.25-.276-.293-.462a6.4,6.4,0,0,0-1.631-3.4,5.863,5.863,0,0,0-2.48-1.24c-.465-.145-.933-.283-1.571-.477,1.1-.393,2.05-.675,2.951-1.069a4.37,4.37,0,0,0,2.385-2.858c.158-.491.317-.981.473-1.472.035-.111.008-.274.173-.283.184-.01.23.149.274.291.138.446.29.888.41,1.339.57,2.14,2.143,3.127,4.144,3.627A3.083,3.083,0,0,1,865.59,424.5Z"
          transform="translate(-19738.279 -240.943)"
          fill="#3398ff"
        />
        <Path
          id="Tracé_53157"
          data-name="Tracé 53157"
          d="M495.842,768.973a3.771,3.771,0,0,0-3.376-3.343,3.7,3.7,0,0,0,3.324-3.324,3.825,3.825,0,0,0,3.4,3.288A3.786,3.786,0,0,0,495.842,768.973Z"
          transform="translate(-19457.969 -641.426)"
          fill="#3398ff"
        />
      </G>
    </Svg>
  );
};
