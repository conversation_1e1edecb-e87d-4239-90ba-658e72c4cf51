import {Path, Svg} from 'react-native-svg';
import React from 'react';

export const AllProjectsUnfocused = (props: {style: {}}) => {
  return (
    <Svg
      style={props.style}
      width="20.555"
      height="20.555"
      viewBox="0 0 20.555 20.555">
      <Path
        id="All_projects"
        data-name="All projects"
        d="M5.472,2H7.778c.407,0,.773,0,1.077.025a2.607,2.607,0,0,1,1.01.264,2.57,2.57,0,0,1,1.1,1.1,2.607,2.607,0,0,1,.264,1.01c.025.3.025.67.025,1.077V7.778c0,.407,0,.773-.025,1.077a2.607,2.607,0,0,1-.264,1.01,2.57,2.57,0,0,1-1.1,1.1,2.607,2.607,0,0,1-1.01.264c-.3.025-.67.025-1.077.025H5.472c-.407,0-.772,0-1.077-.025a2.607,2.607,0,0,1-1.01-.264,2.57,2.57,0,0,1-1.1-1.1,2.607,2.607,0,0,1-.264-1.01C2,8.55,2,8.184,2,7.778V5.472C2,5.065,2,4.7,2.025,4.4a2.607,2.607,0,0,1,.264-1.01,2.569,2.569,0,0,1,1.1-1.1A2.607,2.607,0,0,1,4.4,2.025C4.7,2,5.065,2,5.472,2ZM4.341,4.11l-.008,0a.514.514,0,0,0-.219.219l0,.008a.307.307,0,0,0-.01.033,1.314,1.314,0,0,0-.027.192c-.017.209-.018.488-.018.944V7.74c0,.456,0,.736.018.944a1.314,1.314,0,0,0,.027.192.308.308,0,0,0,.01.033l0,.008a.514.514,0,0,0,.219.219l.008,0,.033.01a1.316,1.316,0,0,0,.192.027c.209.017.488.018.944.018H7.74c.456,0,.736,0,.944-.018a1.316,1.316,0,0,0,.192-.027l.033-.01.008,0a.514.514,0,0,0,.219-.219l0-.008a.308.308,0,0,0,.01-.033,1.316,1.316,0,0,0,.027-.192c.017-.209.018-.488.018-.944V5.51c0-.456,0-.736-.018-.944a1.316,1.316,0,0,0-.027-.192.307.307,0,0,0-.01-.033l0-.008a.514.514,0,0,0-.219-.219l-.008,0L8.876,4.1a1.314,1.314,0,0,0-.192-.027c-.209-.017-.488-.018-.944-.018H5.51c-.456,0-.736,0-.944.018a1.314,1.314,0,0,0-.192.027ZM16.777,2h2.306c.407,0,.773,0,1.077.025a2.606,2.606,0,0,1,1.01.264,2.569,2.569,0,0,1,1.1,1.1A2.607,2.607,0,0,1,22.53,4.4c.025.3.025.67.025,1.077V7.778c0,.407,0,.773-.025,1.077a2.607,2.607,0,0,1-.264,1.01,2.57,2.57,0,0,1-1.1,1.1,2.606,2.606,0,0,1-1.01.264c-.3.025-.67.025-1.077.025H16.777c-.407,0-.773,0-1.077-.025a2.606,2.606,0,0,1-1.01-.264,2.57,2.57,0,0,1-1.1-1.1,2.607,2.607,0,0,1-.264-1.01c-.025-.3-.025-.67-.025-1.077V5.472c0-.407,0-.772.025-1.077a2.607,2.607,0,0,1,.264-1.01,2.569,2.569,0,0,1,1.1-1.1,2.606,2.606,0,0,1,1.01-.264C16,2,16.371,2,16.777,2ZM15.646,4.11l-.008,0a.514.514,0,0,0-.219.219l0,.008a.313.313,0,0,0-.01.033,1.308,1.308,0,0,0-.027.192c-.017.209-.018.488-.018.944V7.74c0,.456,0,.736.018.944a1.309,1.309,0,0,0,.027.192.315.315,0,0,0,.01.033l0,.008a.514.514,0,0,0,.219.219l.008,0,.033.01a1.318,1.318,0,0,0,.192.027c.209.017.488.018.944.018h2.23c.456,0,.736,0,.944-.018a1.316,1.316,0,0,0,.192-.027l.033-.01.008,0a.514.514,0,0,0,.219-.219l0-.008a.315.315,0,0,0,.01-.033,1.308,1.308,0,0,0,.027-.192c.017-.209.018-.488.018-.944V5.51c0-.456,0-.736-.018-.944a1.308,1.308,0,0,0-.027-.192.313.313,0,0,0-.01-.033l0-.008a.514.514,0,0,0-.219-.219l-.008,0-.033-.01a1.314,1.314,0,0,0-.192-.027c-.209-.017-.488-.018-.944-.018h-2.23c-.456,0-.736,0-.944.018a1.316,1.316,0,0,0-.192.027Zm-10.174,9.2H7.778c.407,0,.773,0,1.077.025a2.607,2.607,0,0,1,1.01.264,2.57,2.57,0,0,1,1.1,1.1,2.606,2.606,0,0,1,.264,1.01c.025.3.025.67.025,1.077v2.306c0,.407,0,.773-.025,1.077a2.606,2.606,0,0,1-.264,1.01,2.57,2.57,0,0,1-1.1,1.1,2.607,2.607,0,0,1-1.01.264c-.3.025-.67.025-1.077.025H5.472c-.407,0-.773,0-1.077-.025a2.607,2.607,0,0,1-1.01-.264,2.569,2.569,0,0,1-1.1-1.1,2.606,2.606,0,0,1-.264-1.01C2,19.855,2,19.489,2,19.083V16.777c0-.407,0-.773.025-1.077a2.606,2.606,0,0,1,.264-1.01,2.569,2.569,0,0,1,1.1-1.1A2.607,2.607,0,0,1,4.4,13.33C4.7,13.305,5.065,13.305,5.472,13.305Zm-1.131,2.11-.008,0a.514.514,0,0,0-.219.219l0,.008a.309.309,0,0,0-.01.033,1.316,1.316,0,0,0-.027.192c-.017.209-.018.488-.018.944v2.23c0,.456,0,.736.018.944a1.314,1.314,0,0,0,.027.192.309.309,0,0,0,.01.033l0,.008a.514.514,0,0,0,.219.219l.008,0,.033.01a1.308,1.308,0,0,0,.192.027c.209.017.488.018.944.018H7.74c.456,0,.736,0,.944-.018a1.308,1.308,0,0,0,.192-.027l.033-.01.008,0a.514.514,0,0,0,.219-.219l0-.008a.309.309,0,0,0,.01-.033,1.316,1.316,0,0,0,.027-.192c.017-.209.018-.488.018-.944v-2.23c0-.456,0-.736-.018-.944a1.318,1.318,0,0,0-.027-.192.309.309,0,0,0-.01-.033l0-.008a.514.514,0,0,0-.219-.219h0l-.006,0-.033-.01a1.309,1.309,0,0,0-.192-.027c-.209-.017-.488-.018-.944-.018H5.51c-.456,0-.736,0-.944.018a1.308,1.308,0,0,0-.192.027Zm12.437-2.11h2.306c.407,0,.773,0,1.077.025a2.606,2.606,0,0,1,1.01.264,2.57,2.57,0,0,1,1.1,1.1,2.606,2.606,0,0,1,.264,1.01c.025.3.025.67.025,1.077v2.306c0,.407,0,.773-.025,1.077a2.606,2.606,0,0,1-.264,1.01,2.57,2.57,0,0,1-1.1,1.1,2.606,2.606,0,0,1-1.01.264c-.3.025-.67.025-1.077.025H16.777c-.407,0-.773,0-1.077-.025a2.606,2.606,0,0,1-1.01-.264,2.57,2.57,0,0,1-1.1-1.1,2.606,2.606,0,0,1-.264-1.01c-.025-.3-.025-.67-.025-1.077V16.777c0-.407,0-.773.025-1.077a2.606,2.606,0,0,1,.264-1.01,2.57,2.57,0,0,1,1.1-1.1,2.606,2.606,0,0,1,1.01-.264C16,13.305,16.371,13.305,16.777,13.305Zm-1.131,2.11-.008,0a.514.514,0,0,0-.219.219l0,.008a.315.315,0,0,0-.01.033,1.311,1.311,0,0,0-.027.192c-.017.209-.018.488-.018.944v2.23c0,.456,0,.736.018.944a1.308,1.308,0,0,0,.027.192.315.315,0,0,0,.01.033l0,.008a.514.514,0,0,0,.219.219l.008,0,.033.01a1.31,1.31,0,0,0,.192.027c.209.017.488.018.944.018h2.23c.456,0,.736,0,.944-.018a1.308,1.308,0,0,0,.192-.027l.033-.01.008,0a.514.514,0,0,0,.219-.219l0-.008a.315.315,0,0,0,.01-.033,1.308,1.308,0,0,0,.027-.192c.017-.209.018-.488.018-.944v-2.23c0-.456,0-.736-.018-.944a1.31,1.31,0,0,0-.027-.192.315.315,0,0,0-.01-.033l0,0,0,0a.514.514,0,0,0-.219-.219l-.008,0-.033-.01a1.308,1.308,0,0,0-.192-.027c-.209-.017-.488-.018-.944-.018h-2.23c-.456,0-.736,0-.944.018a1.311,1.311,0,0,0-.192.027Z"
        transform="translate(-2 -2)"
        fill="#66bcff"
        fill-rule="evenodd"
      />
    </Svg>
  );
};
