import {StyleSheet} from 'react-native';
import {GLOBAL_MARGIN_HORIZONTAL, windowWidth} from '@src/constants';
import {correspondentHeight, correspondentWidth} from '@src/utils/imageUtils';
import {COMMON_COLORS} from '@src/theme/colors';

export const style = StyleSheet.create({
  container: {
    marginTop: correspondentHeight(23),
    width: windowWidth,
    marginHorizontal: -GLOBAL_MARGIN_HORIZONTAL,
    height: '94%',
  },
  rowBack: {
    flex: 1,
    height: '100%',
  },
  backRightBtn: {
    alignItems: 'flex-end',
    justifyContent: 'center',
    position: 'absolute',
    bottom: 0,
    top: 0,
    width: correspondentWidth(75),
    paddingRight: correspondentWidth(17),
  },
  backRightBtnLeft: {
    backgroundColor: '#1f65ff',
    right: correspondentWidth(75),
  },
  backRightBtnRight: {
    backgroundColor: COMMON_COLORS.RED,
    right: 0,
    borderTopRightRadius: 5,
    borderBottomRightRadius: 5,
  },
  trashIconContainerFromRight: {
    position: 'absolute',
    height: '100%',
    marginTop: correspondentHeight(16),
    paddingHorizontal: correspondentHeight(60),
    right: 0,
    backgroundColor: 'red',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
