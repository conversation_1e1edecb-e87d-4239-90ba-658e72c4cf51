// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Drawer Should render and match the snapshot 1`] = `
<RCTSafeAreaView
  style={
    {
      "backgroundColor": "#007CFF",
      "flex": 1,
      "justifyContent": "space-around",
      "marginTop": -22,
      "paddingHorizontal": 20,
      "paddingStart": 30,
    }
  }
>
  <View
    style={
      {
        "alignItems": "center",
        "flexDirection": "row",
        "justifyContent": "space-between",
      }
    }
  >
    <View
      style={
        {
          "alignItems": "center",
          "flexDirection": "row",
        }
      }
    >
      <View
        style={
          {
            "borderColor": "white",
            "borderRadius": 35,
            "borderWidth": 2,
            "height": 70,
            "overflow": "hidden",
            "padding": 3,
            "width": 70,
          }
        }
      >
        <Image
          source={
            {
              "testUri": "../../../assets/images/avatar_user.png",
            }
          }
          style={
            {
              "borderRadius": 35,
              "height": "100%",
              "resizeMode": "cover",
              "width": "100%",
            }
          }
        />
      </View>
      <View
        style={
          {
            "marginStart": 6,
          }
        }
      >
        <Text
          style={
            {
              "color": "white",
            }
          }
        />
      </View>
    </View>
    <View
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": undefined,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessibilityValue={
        {
          "max": undefined,
          "min": undefined,
          "now": undefined,
          "text": undefined,
        }
      }
      accessible={true}
      collapsable={false}
      focusable={true}
      onBlur={[Function]}
      onClick={[Function]}
      onFocus={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "alignItems": "center",
          "alignSelf": "center",
          "height": 80.03999999999999,
          "justifyContent": "center",
        }
      }
      testID="close-menu-icon"
    >
      <Image
        source={
          {
            "testUri": "../../../assets/images/close_button_image.png",
          }
        }
        style={
          {
            "aspectRatio": 1,
            "marginRight": 15,
            "width": 15,
          }
        }
      />
    </View>
  </View>
  <View
    style={
      {
        "justifyContent": "center",
      }
    }
  >
    <RCTScrollView
      getItem={[Function]}
      getItemCount={[Function]}
      keyExtractor={[Function]}
      onContentSizeChange={[Function]}
      onLayout={[Function]}
      onMomentumScrollBegin={[Function]}
      onMomentumScrollEnd={[Function]}
      onScroll={[Function]}
      onScrollBeginDrag={[Function]}
      onScrollEndDrag={[Function]}
      removeClippedSubviews={false}
      renderItem={[Function]}
      scrollEventThrottle={50}
      stickyHeaderIndices={[]}
      viewabilityConfigCallbackPairs={[]}
    >
      <View />
    </RCTScrollView>
  </View>
  <View
    accessibilityState={
      {
        "busy": undefined,
        "checked": undefined,
        "disabled": undefined,
        "expanded": undefined,
        "selected": undefined,
      }
    }
    accessibilityValue={
      {
        "max": undefined,
        "min": undefined,
        "now": undefined,
        "text": undefined,
      }
    }
    accessible={true}
    collapsable={false}
    focusable={false}
    onClick={[Function]}
    onResponderGrant={[Function]}
    onResponderMove={[Function]}
    onResponderRelease={[Function]}
    onResponderTerminate={[Function]}
    onResponderTerminationRequest={[Function]}
    onStartShouldSetResponder={[Function]}
    style={
      {
        "alignItems": "center",
        "bottom": -33,
        "flexDirection": "row",
        "opacity": 1,
      }
    }
  >
    <Image
      source={
        {
          "testUri": "../../../assets/images/Logout.png",
        }
      }
      style={
        {
          "height": 21,
          "marginEnd": 12,
          "resizeMode": "contain",
          "width": 21,
        }
      }
    />
    <View
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": undefined,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessibilityValue={
        {
          "max": undefined,
          "min": undefined,
          "now": undefined,
          "text": undefined,
        }
      }
      accessible={true}
      collapsable={false}
      focusable={true}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "opacity": 1,
        }
      }
    >
      <Text
        style={
          {
            "color": "white",
          }
        }
      >
        Log Out !
      </Text>
    </View>
  </View>
</RCTSafeAreaView>
`;
