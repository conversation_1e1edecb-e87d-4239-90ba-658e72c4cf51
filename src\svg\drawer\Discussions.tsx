import {G, Path, Svg} from 'react-native-svg';
import React from 'react';

export const Discussions = (props: {style: {}}) => {
  return (
    <Svg style={props.style} width="20" height="20" viewBox="0 0 20 20">
      <G id="Discussions" transform="translate(-2 -2)">
        <Path
          id="secondary"
          d="M8,17h9l4,4V7a1,1,0,0,0-1-1H17"
          fill="none"
          stroke="#fff"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
        />
        <Path
          id="primary"
          d="M17,4v8a1,1,0,0,1-1,1H7L3,17V4A1,1,0,0,1,4,3H16A1,1,0,0,1,17,4Z"
          fill="none"
          stroke="#fff"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
        />
      </G>
    </Svg>
  );
};
