// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`NotAuthenticatedModal Should render and match the snapshot 1`] = `
<Modal
  animationType="fade"
  hardwareAccelerated={false}
  transparent={true}
  visible={true}
>
  <View
    style={
      {
        "alignItems": "center",
        "flex": 1,
        "justifyContent": "center",
      }
    }
    testID="View Not Authenticated"
  >
    <BlurView
      blurAmount={10}
      blurType="dark"
      style={
        [
          {
            "backgroundColor": "transparent",
          },
          {
            "bottom": 0,
            "left": 0,
            "position": "absolute",
            "right": 0,
            "top": 0,
          },
        ]
      }
    />
    <View
      style={
        {
          "backgroundColor": "red",
          "padding": 12,
        }
      }
    >
      <Text
        style={
          {
            "fontSize": 24,
            "fontWeight": "bold",
            "marginBottom": 10,
            "textAlign": "center",
          }
        }
      >
        Oops, something went wrong!
      </Text>
      <Text
        style={
          {
            "fontSize": 16,
            "textAlign": "center",
          }
        }
      >
        Check Your authentication !
      </Text>
      <View
        accessibilityRole="button"
        accessibilityState={
          {
            "busy": undefined,
            "checked": undefined,
            "disabled": undefined,
            "expanded": undefined,
            "selected": undefined,
          }
        }
        accessibilityValue={
          {
            "max": undefined,
            "min": undefined,
            "now": undefined,
            "text": undefined,
          }
        }
        accessible={true}
        collapsable={false}
        focusable={true}
        onClick={[Function]}
        onResponderGrant={[Function]}
        onResponderMove={[Function]}
        onResponderRelease={[Function]}
        onResponderTerminate={[Function]}
        onResponderTerminationRequest={[Function]}
        onStartShouldSetResponder={[Function]}
        style={
          {
            "opacity": 1,
          }
        }
      >
        <View
          style={
            [
              {},
            ]
          }
        >
          <Text
            style={
              [
                {
                  "color": "#007AFF",
                  "fontSize": 18,
                  "margin": 8,
                  "textAlign": "center",
                },
              ]
            }
          >
            Click here to reconnect 
          </Text>
        </View>
      </View>
    </View>
  </View>
</Modal>
`;
