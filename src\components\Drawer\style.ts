import {StyleSheet, Dimensions} from 'react-native';
import {windowHeight} from '@src/constants';
import {correspondentHeight, correspondentWidth} from '@src/utils/imageUtils';
import {COMMON_COLORS} from '@src/theme/colors';

// Get the screen dimensions to ensure full coverage
const {height: screenHeight} = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    height: screenHeight, // Use full screen height
    width: '65%', // Set width to match drawer width
    backgroundColor: 'transparent', // Make container transparent
    paddingHorizontal: 20,
    paddingStart: 30,
    paddingTop: windowHeight * 0.06,
    paddingBottom: windowHeight * 0.05,
    justifyContent: 'space-between',
    zIndex: 2, // Higher than drawer background but lower than screen
  },
  profileInformationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
    borderRadius: 70 / 2,
  },
  imageContainer: {
    padding: 3,
    width: 70,
    height: 70,
    borderRadius: 70 / 2,
    borderColor: 'white',
    borderWidth: 2,
    overflow: 'hidden',
  },
  fullNameAndPositionContainer: {
    marginStart: 6,
  },
  position: {
    fontWeight: '100',
    color: 'white',
    fontSize: 13,
  },
  fullName: {
    color: 'white',
  },
  X_button: {
    marginRight: 15,
    width: 15,
    aspectRatio: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  drawerItemsContainer: {
    flex: 1, // Take up available space
    justifyContent: 'center',
    marginVertical: 20, // Add vertical margin
  },
  drawerItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 40,
  },
  iconImage: {
    width: correspondentWidth(21),
    height: correspondentHeight(21),
    marginEnd: correspondentWidth(12),
    resizeMode: 'contain',
  },
  textRouteName: {
    fontSize: 16,
  },
  logOutButtonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: correspondentHeight(10), // Use margin instead of absolute positioning
  },
  logOutText: {
    color: 'white',
  },
});

export default styles;
