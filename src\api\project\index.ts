export const ProjectWebServices = {
  WS_GET_PROGRAM_ENUMS: '/programSector/PROGRAM',
  WS_GET_PROJECT_BY_ID: (projectId: number) =>
    `projects/find-by-id-mobile/${projectId}`,
  WS_GET_SECTOR_ENUMS: '/programSector/SECTOR',
  WS_GET_BU_ENUMS: '/enumerations/PROJECT_BU',
  WS_GET_PROJECTS: '/projects/mobile-search',
  WS_GET_ENUMS_BY_TYPE: '/properties-data/list-by-types',
  WS_GET_IMAGE_FROM_SERVER: '/files/download-by-url',
  WS_GET_USER_PROJECT_BY_USER_ID_AND_PROJECT_ID: (
    userId: string,
    projectId: number | null,
  ) => {
    return `/user-project/${userId}/project/${projectId}`;
  },
  WS_GET_USERS_IN_ONE_PROJECT: (idProject: number) =>
    `/user-project/${idProject}`,
};
