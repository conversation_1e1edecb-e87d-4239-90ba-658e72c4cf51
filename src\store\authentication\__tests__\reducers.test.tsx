import reducer, {
  initialState,
  login,
  logOut,
  setFcmToken,
  setFcmTokenWithError,
  setFcmTokenWithSuccess,
  setUserToken,
} from '@src/store/authentication/slice';

describe('Authentication reducer', () => {
  it('should return the initial state', () => {
    expect(reducer(undefined, {type: undefined})).toEqual(initialState);
  });

  it('should handle login action', () => {
    const action = login({userToken: 'USER_TOKEN'});
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      isAuthenticated: true,
      userToken: 'USER_TOKEN',
    });
  });

  it('should handle logout action', () => {
    const action = logOut();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      isAuthenticated: false,
      userToken: null,
    });
  });

  it('should handle setUserToken action', () => {
    const action = setUserToken('USER_TOKEN');
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      userToken: 'USER_TOKEN',
    });
  });

  it('should handle setFcmToken action', () => {
    const action = setFcmToken({fcmToken: 'FCM_TOKEN'});
    const newState = reducer(initialState, action);
    expect(newState).toEqual({
      ...initialState,
      loading: true,
      error: false,
      success: false,
    });
  });

  it('should handle setFcmTokenWithSuccess action', () => {
    const payload = 'fcmToken'; // Replace with a valid FCM token
    const action = setFcmTokenWithSuccess(payload);
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      fcmToken: action.payload,
      error: false,
      success: true,
      loading: false,
    });
  });

  it('should handle setFcmTokenWithError action', () => {
    const action = setFcmTokenWithError();
    const newState = reducer(initialState, action);

    expect(newState).toEqual({
      ...initialState,
      error: true,
      success: false,
      loading: false,
    });
  });
});
