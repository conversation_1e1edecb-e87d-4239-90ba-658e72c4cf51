import React from 'react';
import {Picker} from 'react-native-wheel-pick';
import {
  SELECTED_TEXT_WHEEL_PICKER,
  style,
} from '@src/components/CustomDatePicker/style';
import {View} from 'react-native';
import typography from '@src/theme/fonts';

type DatePicker = {
  handleUpdateDateFilter: (month: string, year: string) => void;
  years: string[];
  months: string[];
  selectedYear: string;
  selectedMonth: string;
};

const CustomDatePicker = (props: DatePicker): React.ReactElement => {
  const {handleUpdateDateFilter, selectedMonth, selectedYear, months, years} =
    props;
  return (
    <View style={style.container}>
      <Picker
        style={style.pickerContainer}
        textSize={typography.fontSizes.lg}
        textColor={SELECTED_TEXT_WHEEL_PICKER}
        pickerData={months}
        selectedValue={selectedMonth}
        onValueChange={(value: string) => {
          handleUpdateDateFilter(value, selectedYear);
        }}
      />
      <Picker
        style={style.pickerContainer}
        textSize={typography.fontSizes.lg}
        textColor={SELECTED_TEXT_WHEEL_PICKER}
        pickerData={years}
        selectedValue={selectedYear}
        onValueChange={(value: string) => {
          handleUpdateDateFilter(selectedMonth, value);
        }}
      />
    </View>
  );
};

export default CustomDatePicker;
