import React, { useCallback, useEffect, useRef, useState } from "react";
import { Animated, FlatList, Platform, View, ViewToken } from "react-native";
import { GLOBAL_MARGIN_HORIZONTAL, windowWidth } from "@src/constants";
import styles, { ITEM_WIDTH } from "./styles";
import { ProjectUtils } from "@src/utils/projectUtils";
import Channel from "@src/components/Channel";
import { useSelector } from "react-redux";
import { globalSelector } from "@src/store/global/selectors";
import { wait } from "@src/utils/timeUtils";

const MARGIN_BETWEEN_CURRENT_ITEM_AND_NEAR_ITEM = windowWidth * 0.02;
const MARGIN_HORIZONTAL = MARGIN_BETWEEN_CURRENT_ITEM_AND_NEAR_ITEM * 1.2;
const ANDROID_DECLARATION_RATE = 0.6;
const IOS_DECLARATION_RATE = 0.4;
const SCROLL_EVENT_THROTTLE = 16;

export type ChannelModel = {
  title: string;
  isDisplayed?: boolean;
  uri?: string;
  content?: any;
  smallIconFocused?: React.ReactElement;
  smallIconUnFocused?: React.ReactElement;
  selector?: any;
};

type CarouselChannelsProps = {
  channels: ChannelModel[];
  updateCarouselPosition: (arg: number) => void;
  currentPosition: number;
  handleRefreshPage: () => void;
};
const CarouselChannels = ({
                            channels,
                            updateCarouselPosition,
                            currentPosition,
                            handleRefreshPage
                          }: CarouselChannelsProps) => {
  const flatListRef = useRef<FlatList>(null);

  useEffect(() => {
    if (flatListRef.current) {
      flatListRef.current.scrollToOffset({
        offset: currentPosition * ITEM_WIDTH,
        animated: false
      });
    }
  }, [currentPosition]);

  const onViewableItemsChanged = useRef(
    ({ viewableItems }: { viewableItems: ViewToken[] }) => {
      if (viewableItems.length > 0 ) {
        const currentItem = viewableItems[0];
        const currentIndex = currentItem.index;
        if (currentItem.key === "left") {
          updateCarouselPosition(0);
        } else {
          updateCarouselPosition(currentIndex!! - 1);
        }
      }
    }
  );

  const renderItem = ({ item }: { item: ChannelModel }) => {
    if (item.title === "left" || item.title === "right") {
      return <View style={{ width: GLOBAL_MARGIN_HORIZONTAL }} />;
    }
    return (
      <View style={styles.channel}>
        <Animated.View
          style={{
            marginHorizontal: MARGIN_HORIZONTAL
          }}>
          {item.isDisplayed && (
            <Channel
              title={item.title}
              illustration={ProjectUtils.returnIllustrationImage(item?.title)}
              content={item.content}
              selector={item.selector}
            />
          )}
        </Animated.View>
      </View>
    );
  };

  const [isRefreshing, setIsRefreshing] = useState(false);

  const onRefresh = useCallback(() => {
    setIsRefreshing(true);
    wait(2000).then(() => setIsRefreshing(false));
    handleRefreshPage();
  }, []);

  return (
    <>
      <View style={styles.container}>
        <FlatList
          refreshing={isRefreshing}
          onRefresh={onRefresh}
          ref={flatListRef}
          showsHorizontalScrollIndicator={false}
          data={channels}
          keyExtractor={item => item.title}
          horizontal
          decelerationRate={
            Platform.OS === "ios"
              ? IOS_DECLARATION_RATE
              : ANDROID_DECLARATION_RATE
          }
          bounces={false}
          pagingEnabled
          snapToInterval={ITEM_WIDTH}
          scrollEventThrottle={SCROLL_EVENT_THROTTLE}
          onViewableItemsChanged={onViewableItemsChanged.current}
          viewabilityConfig={{ viewAreaCoveragePercentThreshold: ITEM_WIDTH }}
          renderItem={renderItem}
        />
      </View>
    </>
  );
};

export default CarouselChannels;
