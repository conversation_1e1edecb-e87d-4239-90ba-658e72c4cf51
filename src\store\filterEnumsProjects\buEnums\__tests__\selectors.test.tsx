import {RootState} from '@src/store/store';
import {FilterEnumsProjectType} from '@src/store/filterEnumsProjects/buEnums/slice';
import {buEnumsSelector} from '@src/store/filterEnumsProjects/buEnums/selectors';

describe('buEnumsSelector', () => {
  it('should select the bu enums state from the root state', () => {
    // Arrange
    const buEnumsState: FilterEnumsProjectType = {
      loading: false,
      error: false,
      success: false,
      buEnums: [],
    };

    // @ts-ignore
    const mockRootState: RootState = {
      buEnums: buEnumsState,
    };

    // Act
    const selectedState = buEnumsSelector(mockRootState);

    // Assert
    expect(selectedState).toEqual(buEnumsState);
  });
});
