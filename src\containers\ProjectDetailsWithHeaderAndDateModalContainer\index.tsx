import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { projectSelector } from "@src/store/project/selectors";
import { COLLAB_MODULES, COLLAB_MODULES_TITLES } from "@src/constants/channels/modules";
import { ProjectUtils } from "@src/utils/projectUtils";
import { usePrivilegeByModule } from "@src/hooks/module/UsePrivilegeByModule";
import { PRIVILEGES } from "@src/constants/channels/privilege";
import { useProjectPrivilegeByModule } from "@src/hooks/module/UseProjectPrivilegeByModule";
import HseFeatures from "@src/containers/HseFeatures";
import { style } from "./style";
import Error from "src/components/ErrorModal";
import {
  getFlattenKpis,
  getUserProjectWithProjectIdAndUserId,
  setCurrentProject,
  setCurrentProjectId,
  setReportingDate
} from "@src/store/project/slice";
import { ChannelModel } from "@src/components/CarouselChannels";
import ProjectDateBottomModal from "@src/containers/ProjectDateBottomModal";
import {
  MainChannelCarouselAndBottomCarouselContainer
} from "@src/containers/MainChannelCarouselAndBottomCarouselContainer";
import Loader from "@src/components/Loader";
import OverviewProject from "@src/components/OverviewProject";
import { hseSliceSelector } from "@src/store/channels/hse/selectors";
import { ROUTE_NAMES } from "@src/constants/navigation";
import { cleanUpHse, getHseFeatures } from "@src/store/channels/hse/slice";
import { cleanUpCash, getCashFlowFeatures } from "@src/store/channels/cashFlow/slice";
import { cleanUpChangeManagement, getChangeManagementFeatures } from "@src/store/channels/changeManagement/slice";
import { cleanUpRisk, getRiskFeatures } from "@src/store/channels/risk/slice";
import { cleanUpCost, getCostFeatures } from "@src/store/channels/cost/slice";
import { cleanUpHumanCap, getHumanCapFeatures } from "@src/store/channels/humanCapital/slice";
import useScreenFocusAndBlurListener from "@src/hooks/useScreenFocusListener";
import { getNotificationsExistence } from "@src/store/notifications/slice";
import { View } from "react-native";
import { costSelector } from "@src/store/channels/cost/selectors";
import { riskSelector } from "@src/store/channels/risk/selectors";
import { changeManagementSelector } from "@src/store/channels/changeManagement/selectors";
import { cashFlowSelector } from "@src/store/channels/cashFlow/selectors";
import { humanCapSelector } from "@src/store/channels/humanCapital/selectors";
import { qaSelector } from "@src/store/channels/qa/selectors";
import { progressSelector } from "@src/store/channels/progress/selectors";
import { useIsFocused } from "@react-navigation/core";
import { globalSelector } from "@src/store/global/selectors";
import { cleanUpScheduling, getSchedulingFeatures } from "@src/store/channels/scheduling/slice";
import { schedulingSelector } from "@src/store/channels/scheduling/selectors";
import { cleanUpProgress, getProgressFeatures } from "@src/store/channels/progress/slice";
import { cleanUpQa, getQAFeatures } from "@src/store/channels/qa/slice";
import HseFocusedIcon from "@src/svg/channels/hse/HseFocusedIcon";
import HseUnfocusedIcon from "@src/svg/channels/hse/HseUnfocusedIcon";
import RiskManagementFeatures from "@src/containers/RiskManagementFeatures";
import CostFeatures from "@src/containers/CostFeatures";
import SchedulingFeatures from "@src/containers/SchedulingFeatures";
import ProgressFeatures from "@src/containers/ProgressFeatures";
import QualityAssuranceFeatures from "@src/containers/QualityAssuranceFeatures";
import ChangeManagementFeatures from "@src/containers/ChangeManagementFeatures";
import CashFlowFeatures from "@src/containers/CashFlowFeatures";
import HumanCapitalFeatures from "@src/containers/HumanCapitalFeatures";
import RiskFocusedIcon from "@src/svg/channels/risk/RiskFocusedIcon";
import RiskUnfocusedIcon from "@src/svg/channels/risk/RiskUnfocusedIcon";
import QaFocusedIcon from "@src/svg/channels/qa/QaFocusedIcon";
import QaUnfocusedIcon from "@src/svg/channels/qa/QaUnfocusedIcon";
import SchedulingFocusedIcon from "@src/svg/channels/scheduling/SchedulingFocusedIcon";
import SchedulingUnfocusedIcon from "@src/svg/channels/scheduling/SchedulingUnfocusedIcon";
import ProgressFocusedIcon from "@src/svg/channels/progress/ProgressFocusedIcon";
import ProgressUnfocusedIcon from "@src/svg/channels/progress/ProgressUnfocusedIcon";
import CashFocusedIcon from "@src/svg/channels/cash/CashFocusedIcon";
import CashUnfocusedIcon from "@src/svg/channels/cash/CashUnfocusedIcon";
import ChangeManagementUnfocusedIcon from "@src/svg/channels/changeManagement/ChangeManagementUnfocusedIcon";
import ChangeManagementFocusedIcon from "@src/svg/channels/changeManagement/ChangeManagementFocusedIcon";
import HumanCapitalFocusedIcon from "@src/svg/channels/humanCapital/HumanCapitalFocusedIcon";
import HumanCapitalUnfocusedIcon from "@src/svg/channels/humanCapital/HumanCapitalUnfocusedIcon";
import CostFocusedIcon from "@src/svg/channels/cost/CostFocusedIcon";
import CostUnfocusedIcon from "@src/svg/channels/cost/CostUnfocusedIcon";
import NotificationsIconHeaderContainer from "@src/containers/headers/NotificationsIconHeaderContainer";
import Header from "@src/components/Header";
import { CalendarIconHeader } from "@src/components/CalendarIconHeader";
import ClickableImage from "@src/components/ClickableImage";
import { MenuDrawer } from "@src/svg/MenuDrawer";
import { globalStyles } from "@src/theme/style";

type ProjectDetailsWithHeaderAndDateModalContainerProps = {
  toggleDrawer: () => void;
  route: any;
};
const ProjectDetailsWithHeaderAndDateModalContainer = (
  props: ProjectDetailsWithHeaderAndDateModalContainerProps
) => {
  const dispatch = useDispatch();

  const { features: hseFeatures } = useSelector(hseSliceSelector);
  const { features: riskFeatures } = useSelector(riskSelector);
  const { features: humanCapFeatures } = useSelector(humanCapSelector);
  const { features: changeMgmtFeatures } = useSelector(changeManagementSelector);
  const { features: costFeatures } = useSelector(costSelector);
  const { features: progressFeatures } = useSelector(progressSelector);
  const { features: cashFeatures } = useSelector(cashFlowSelector);
  const { features: schedulingFeatures } = useSelector(schedulingSelector);
  const { features: qaFeatures } = useSelector(qaSelector);

  const { toggleDrawer, route } = props;

  const {
    currentProject,
    flattenKpis,
    profile,
    role,
    loading,
    reportingDateFilter,
    projectId: projectIdFromStore,
    error,
    errorUserProject,
    errorFlattenKpis
  } = useSelector(projectSelector);

  let projectId: number | null;
  if (projectIdFromStore === null) {
    projectId = route.params?.projectId;
  } else {
    projectId = projectIdFromStore;
  }

  const [isModalVisible, setIsModalVisible] = useState(false);

  const isHseModuleVisible = ProjectUtils.isModuleVisible(
    usePrivilegeByModule(COLLAB_MODULES.HSE, PRIVILEGES.READ),
    useProjectPrivilegeByModule(COLLAB_MODULES.HSE)
  );

  const isCashModuleVisible = ProjectUtils.isModuleVisible(
    usePrivilegeByModule(COLLAB_MODULES.CASH, PRIVILEGES.READ),
    useProjectPrivilegeByModule(COLLAB_MODULES.CASH)
  );

  const isRiskManagementModuleVisible = ProjectUtils.isModuleVisible(
    usePrivilegeByModule(COLLAB_MODULES.RISK_MGMT, PRIVILEGES.READ),
    useProjectPrivilegeByModule(COLLAB_MODULES.RISK_MGMT)
  );

  const isCostModuleVisible = ProjectUtils.isModuleVisible(
    usePrivilegeByModule(COLLAB_MODULES.COST, PRIVILEGES.READ),
    useProjectPrivilegeByModule(COLLAB_MODULES.COST)
  );

  const isSchedulingModuleVisible = ProjectUtils.isModuleVisible(
    usePrivilegeByModule(COLLAB_MODULES.SCHEDULING, PRIVILEGES.READ),
    useProjectPrivilegeByModule(COLLAB_MODULES.SCHEDULING)
  );

  const isProgressModuleVisible = ProjectUtils.isModuleVisible(
    usePrivilegeByModule(COLLAB_MODULES.PROGRESS, PRIVILEGES.READ),
    useProjectPrivilegeByModule(COLLAB_MODULES.PROGRESS)
  );

  const isQAModuleVisible = ProjectUtils.isModuleVisible(
    usePrivilegeByModule(COLLAB_MODULES.QA, PRIVILEGES.READ),
    useProjectPrivilegeByModule(COLLAB_MODULES.QA)
  );

  const isChangeManagementModule = ProjectUtils.isModuleVisible(
    usePrivilegeByModule(COLLAB_MODULES.CHANGE_MGMT, PRIVILEGES.READ),
    useProjectPrivilegeByModule(COLLAB_MODULES.CHANGE_MGMT)
  );

  const isHumanCapModule = ProjectUtils.isModuleVisible(
    usePrivilegeByModule(COLLAB_MODULES.HUMAN_CAP, PRIVILEGES.READ),
    useProjectPrivilegeByModule(COLLAB_MODULES.HUMAN_CAP)
  );

  const isFocused = useIsFocused();
  // Use drawer status from React Navigation
  const isDrawerOpen = true;

  const channelActionsDispatchWhileMountingProjectHealthScreen = () => {
    if (isHseModuleVisible) {
      dispatch(getHseFeatures());
    }
    if (isCostModuleVisible) {
      dispatch(getCostFeatures());
    }
    if (isRiskManagementModuleVisible) {
      dispatch(getRiskFeatures());
    }
    if (isSchedulingModuleVisible) {
      dispatch(getSchedulingFeatures());
    }
    if (isProgressModuleVisible) {
      dispatch(getProgressFeatures());
    }
    if (isQAModuleVisible) {
      dispatch(getQAFeatures());
    }
    if (isChangeManagementModule) {
      dispatch(getChangeManagementFeatures());
    }
    if (isCashModuleVisible) {
      dispatch(getCashFlowFeatures());
    }
    if (isHumanCapModule) {
      dispatch(getHumanCapFeatures());
    }
  };

  useEffect(() => {
    if (isFocused && !isDrawerOpen && reportingDateFilter != null) {
      channelActionsDispatchWhileMountingProjectHealthScreen();
    }
  }, [
    isCashModuleVisible,
    isChangeManagementModule,
    isCostModuleVisible,
    isHseModuleVisible,
    isHumanCapModule,
    isProgressModuleVisible,
    isQAModuleVisible,
    isRiskManagementModuleVisible,
    isSchedulingModuleVisible,
    isFocused,
    isDrawerOpen,
    reportingDateFilter
  ]);

  useEffect(() => {
    if (flattenKpis === null) {
      dispatch(getFlattenKpis({ projectId }));
    }
    if (profile === null && role === null) {
      dispatch(getUserProjectWithProjectIdAndUserId({ projectId }));
    }
  }, []);

  useScreenFocusAndBlurListener(
    [
      {
        action: getNotificationsExistence,
        params: { projectId }
      },
      {
        action: setCurrentProject,
        params: projectId
      },
      {
        action: setCurrentProjectId,
        params: projectId
      }
    ],
    [
      { action: cleanUpHse },
      { action: cleanUpCost },
      { action: cleanUpRisk },
      { action: cleanUpScheduling },
      { action: cleanUpProgress },
      { action: cleanUpQa },
      { action: cleanUpChangeManagement },
      { action: cleanUpCash },
      { action: cleanUpHumanCap }
    ]
  );

  const handleOpenModal = () => {
    setIsModalVisible(true);
  };

  const channelCleanUpActionsDispatchWhileApplyingNewDateFilter = () => {
    dispatch(cleanUpHse());
    dispatch(cleanUpCost());
    dispatch(cleanUpRisk());
    dispatch(cleanUpScheduling());
    dispatch(cleanUpProgress());
    dispatch(cleanUpQa());
    dispatch(cleanUpChangeManagement());
    dispatch(cleanUpCash());
    dispatch(cleanUpHumanCap());
  };

  const handleApplyFilter = (reportingDate: { year: number; month: number }) => {
    setIsModalVisible(false);
    dispatch(setReportingDate(reportingDate));
    channelCleanUpActionsDispatchWhileApplyingNewDateFilter();
    channelActionsDispatchWhileMountingProjectHealthScreen();
  };

  const handleCancelFilter = () => {
    setIsModalVisible(false);
  };

  const channels: ChannelModel[] = [
    {
      title: "left",
      isDisplayed: true
    },
    {
      title: COLLAB_MODULES_TITLES.HSE,
      isDisplayed: isHseModuleVisible,
      content: <HseFeatures />,
      smallIconFocused: <HseFocusedIcon />,
      smallIconUnFocused: <HseUnfocusedIcon />,
      selector: hseSliceSelector
    },
    {
      title: COLLAB_MODULES_TITLES.RISK_MGMT,
      isDisplayed: isRiskManagementModuleVisible,
      content: <RiskManagementFeatures />,
      smallIconFocused: <RiskFocusedIcon />,
      smallIconUnFocused: <RiskUnfocusedIcon />,
      selector: riskSelector
    },
    {
      title: COLLAB_MODULES_TITLES.COST,
      isDisplayed: isCostModuleVisible,
      content: <CostFeatures />,
      smallIconFocused: <CostFocusedIcon />,
      smallIconUnFocused: <CostUnfocusedIcon />,
      selector: costSelector
    },
    {
      title: COLLAB_MODULES_TITLES.SCHEDULING,
      isDisplayed: isSchedulingModuleVisible,
      content: <SchedulingFeatures />,
      smallIconFocused: <SchedulingFocusedIcon />,
      smallIconUnFocused: <SchedulingUnfocusedIcon />,
      selector: schedulingSelector
    },
    {
      title: COLLAB_MODULES_TITLES.PROGRESS,
      isDisplayed: isProgressModuleVisible,
      content: <ProgressFeatures />,
      smallIconFocused: <ProgressFocusedIcon />,
      smallIconUnFocused: <ProgressUnfocusedIcon />,
      selector: progressSelector
    },
    {
      title: COLLAB_MODULES_TITLES.QA,
      isDisplayed: isQAModuleVisible,
      content: <QualityAssuranceFeatures />,
      smallIconFocused: <QaFocusedIcon />,
      smallIconUnFocused: <QaUnfocusedIcon />,
      selector: qaSelector
    },
    {
      title: COLLAB_MODULES_TITLES.CHANGE_MGMT,
      isDisplayed: isChangeManagementModule,
      content: <ChangeManagementFeatures />,
      selector: changeManagementSelector,
      smallIconFocused: <ChangeManagementFocusedIcon />,
      smallIconUnFocused: <ChangeManagementUnfocusedIcon />
    },
    {
      title: COLLAB_MODULES_TITLES.CASH,
      isDisplayed: isCashModuleVisible,
      content: <CashFlowFeatures />,
      smallIconFocused: <CashFocusedIcon />,
      smallIconUnFocused: <CashUnfocusedIcon />,
      selector: cashFlowSelector
    },
    {
      title: COLLAB_MODULES_TITLES.HUMAN_CAP,
      isDisplayed: isHumanCapModule,
      content: <HumanCapitalFeatures />,
      smallIconFocused: <HumanCapitalFocusedIcon />,
      smallIconUnFocused: <HumanCapitalUnfocusedIcon />,
      selector: humanCapSelector
    },
    {
      title: "right",
      isDisplayed: true
    }
  ];

  const allowedChannels = channels.filter(
    channel =>
      channel.title !== "right" &&
      channel.title !== "left" &&
      channel.isDisplayed
  );

  const allowedChannelsMainCarousel = channels.filter(
    channel => channel.isDisplayed
  );

  return (
    <View style={style.container}>
      <View style={style.screenContainer}>
        <Header
          routeName={ROUTE_NAMES.PROJECT_HEALTH_DETAILS}
          rightContent={
            <View style={globalStyles.iconsContainer}>
              <NotificationsIconHeaderContainer projectId={projectId} />
              <CalendarIconHeader onPress={handleOpenModal} />
              <ClickableImage
                onPress={toggleDrawer}
                url={null}
                imageStyle={globalStyles.drawerLogoImage}
                SvgComponent={<MenuDrawer style={globalStyles.drawerLogoImage} />}
              />
            </View>
          }
          showBackButton={true}
        />
        {currentProject !== null && !loading && (
          <View style={{ zIndex: 44 }}>
            <OverviewProject
              toggleDrawer={toggleDrawer}
              currentProject={currentProject}
              period={reportingDateFilter}
            />
          </View>
        )}

        <MainChannelCarouselAndBottomCarouselContainer
          handleRefreshPage={() => {
            channelActionsDispatchWhileMountingProjectHealthScreen();
          }}
          mainCarouselChannels={allowedChannelsMainCarousel}
          bottomCarouselChannels={allowedChannels}
        />

        {isModalVisible && (
          <ProjectDateBottomModal
            startDate={currentProject?.startDate!!}
            reportingDateParams={reportingDateFilter}
            reportingDate={currentProject?.reportingDate!!}
            handleApplyFilter={handleApplyFilter}
            handleCancelFilter={handleCancelFilter}
            visible={isModalVisible}
          />
        )}
        {(error || errorUserProject || errorFlattenKpis) && <Error />}
        {(loading && currentProject === null) && (<Loader show={true} />)}
      </View>
    </View>
  );
};

export default ProjectDetailsWithHeaderAndDateModalContainer;
