export const getSectionValueByKey = <T>(
  obj: T,
  sectionKey: keyof T,
  key: string
) => {
  const sectionValues = obj[sectionKey];

  const values = Object.fromEntries(
    Object.entries(sectionValues as Record<string, any>).map(([subKey, value]) => [
      subKey,
      value
    ])
  );

  return values[key as string];
};

export const COLLAB_FEATURES = {
  HSE: {
    HSE_KPI: "HSE_KPI",
    HSE_CRITICAL_SOR: "HSE_CRITICAL_SOR"
  },
  PROGRESS: {},
  RISK: {
    RISK_BOARD: "RISK_BOARD",
    RISK_MATRIX_EVOL: "RISK_MATRIX_EVOL"
  },
  HUMAN_CAPITAL: {
    HUMAN_CAP_PROJECT_JESA_TEAM: "HUMAN_CAP_PROJECT_JESA_TEAM",
    HUMAN_CAP_PROJECT_CLIENT_TEAM: "HUMAN_CAP_PROJECT_CLIENT_TEAM",
    HUMAN_CAP_PROJECT_CONTRACTOR_TEAM: "HUMAN_CAP_PROJECT_CONTRACTOR_TEAM",
    HUMAN_CAP_SWH_SS_FTE: "HUMAN_CAP_SWH_SS_FTE",
    HUMAN_CAP_KEY_CONTACTS: "HUMAN_CAP_KEY_CONTACTS"
  },
  SCHEDULING: {
    SCHED_L1_2_ACTIV: "SCHED_L1_2_ACTIV",
    SCHED_QA_SCORE: "SCHED_QA_SCORE",
    SCHED_RISK_ANALYSIS: "SCHED_RISK_ANALYSIS",
    SCHED_MILESTONES: "SCHED_MILESTONES"
  },
  COST: {
    COST_CPI: "COST_CPI",
    COST_GID: "COST_GID",
    COST_TIC: "COST_TIC",
    COST_PROF_SERVICES: "COST_PROF_SERVICES",
    COST_BUDGET: "COST_BUDGET",
    WEEKLY_TIMESHEET_STATUS: "WEEKLY_TIMESHEET_STATUS"
  },
  // QA has no features privileges for the moment
  QA: {},
  CHANGE_MANAGEMENT: {
    CHANGE_MGMT_CONTRAC_CHANGE: "CHANGE_MGMT_CONTRAC_CHANGE",
    CHANGE_MGMT_JESA_CHANGE: "CHANGE_MGMT_JESA_CHANGE",
    CHANGE_MGMT_CLAIMS: "CHANGE_MGMT_CLAIMS",
    CHANGE_MGMT_VENDOR_CHANGE: "CHANGE_MGMT_VENDOR_CHANGE",
    CHANGE_MGMT_JESA_CHANGE_ANALYSIS: "CHANGE_MGMT_JESA_CHANGE_ANALYSIS",
    CHANGE_MGMT_CONTRACTOR_CHANGE_TRENDING_ANALYSIS:
      "CHANGE_MGMT_CONTRACTOR_CHANGE_TRENDING_ANALYSIS",
    CHANGE_MGMT_VENDOR_CHANGE_TRENDING_ANALYSIS:
      "CHANGE_MGMT_VENDOR_CHANGE_TRENDING_ANALYSIS",
    CHANGE_MGMT_CLAIM_TRENDING_ANALYSIS: "CHANGE_MGMT_CLAIM_TRENDING_ANALYSIS"
  },
  CASH_FLOW: {
    DSO: "CASH",
    passDueJesaPayment: "CASH_JESA_PAYMENT",
    CASH_CONTRACTOR_PAYMENT: "CASH_CONTRACTOR_PAYMENT"
  }
};

export const COLLAB_SUMMARY_FEATURE_KEY_WITH_FEATURES_CODE = {
  COST: {
    revisedBudgetTic: "COST_BUDGET",
    cpi: "COST_GID",
    forecast: "COST_GID"
  },
  CASH_FLOW: {
    dso: "CASH",
    passDueJesaPayment: "CASH_JESA_PAYMENT",
    passDueContractorPayment: "CASH_CONTRACTOR_PAYMENT"
  },
  HSE: {
    openSor: "HSE_CRITICAL_SOR",
    trir: "HSE_KPI"
  },
  SCHEDULING: {
    spi: "SCHED_QA_SCORE",
    plannedSop: "SCHED_MILESTONES",
    forecastSop: "SCHED_MILESTONES"
  },
  QA: {
    //clientSatisfaction: "Client Satisfaction",
    //capasRiskLevels: "capasRiskLevels",
    //averageOpenDays: " averageOpenDays"
  },
  // Progress has no features privileges for the moment
  PROGRESS: {
    //actual: "ACTUAL",
    //planned: "PLANNED"
  },
  HUMAN_CAPITAL: {
    contractorHeadCount: "HUMAN_CAP_PROJECT_CONTRACTOR_TEAM",
    jesaHeadCount: "HUMAN_CAP_PROJECT_CLIENT_TEAM"
  },
  CHANGE_MANAGEMENT: {
    pendingJesaChange: "CHANGE_MGMT_JESA_CHANGE",
    pendingContractorChange: "CHANGE_MGMT_CONTRAC_CHANGE"
  },
  RISK: {
    evolution: "RISK_MATRIX_EVOL",
    criticalRisks: "RISK_BOARD"
  }
};

export const COLLAB_FEATURES_TITLES = {
  HSE: {
    openSor: "Critical SOR",
    trir: "TRCFR",
    closedSor: "closedSor",
    channelStatus: "channelStatus"
  },
  RISK: {
    criticalRisks: "Very High",
    evolution: "Evolution"
  },
  COST: {
    cpi: "CPI",
    revisedBudgetTic: "Revised Budget",
    forecast: "Forecast"
  },
  PROGRESS: {
    planned: "Planned",
    actual: "Actual"
  },
  SCHEDULING: {
    spi: "SPI",
    plannedSop: "Planned SOP",
    forecastSop: "Forecast SOP"
  },
  CASH_FLOW: {
    dso: "DSO",
    passDueJesaPayment: "Past due JESA Payment",
    passDueContractorPayment: "Past Due Contractor Payment"
  },
  CHANGE_MANAGEMENT: {
    pendingJesaChange: "Pending JESA Change",
    pendingContractorChange: "Pending contractor Change"
  },
  HUMAN_CAPITAL: {
    jesaHeadCount: "Headcount JESA",
    contractorHeadCount: "Headcount Contractor"
  },
  QA: {
    clientSatisfaction: "Satisfaction",
    averageOpenDays: "Average overdue days",
    overduePinsNumber: "Numbers of overdue CAPAs"
  }
};
