import {Type} from 'class-transformer';

export interface RiskChannelSummary {
  criticalRisks?: number;
  currentEvolution?: number;
  previousEvolution?: number;
  channelStatus?: string | null;
  subParameters?: SubParameters[];
}

export class SubParameters {
  public id?: number;
  public color?: string;
  @Type(() => ReportSubParameter)
  public reportSubParameter?: ReportSubParameter;
}

export class ReportSubParameter {
  public id?: number;
  public code?: string;
  public label?: string;
  public shortCut?: string;
  @Type(() => ReportParameter)
  public reportParameter?: ReportParameter;
}

export class ReportParameter {
  public id?: number;
  public code?: string;
  public label?: string;
}
