import { View } from "react-native";
import { ObjectUtils } from "@src/utils/objectUtils";
import React from "react";
import {
  COLLAB_FEATURES,
  COLLAB_FEATURES_TITLES,
  COLLAB_SUMMARY_FEATURE_KEY_WITH_FEATURES_CODE,
  getSectionValueByKey
} from "@src/constants/channels/features";
import { useSelector } from "react-redux";
import { changeManagementSelector } from "@src/store/channels/changeManagement/selectors";
import Loader from "@src/components/Loader";
import { KEYS_CHANNEL } from "@src/constants/channels/modules";
import { ChannelFeaturesDefaultValues } from "@src/constants/channelFeaturesDefaultValues";
import FeatureContainer, { renderFeatureContainers } from "@src/containers/FeatureContainer";

const ChangeManagementFeatures = () => {
  const { features, loading } = useSelector(changeManagementSelector);

  function renderDefaultValues() {
    const changeManagementFlowFeatures: FeatureContainer[] = [
      {
        featureKey: COLLAB_FEATURES.CHANGE_MANAGEMENT.CHANGE_MGMT_JESA_CHANGE,
        featureTitle: COLLAB_FEATURES_TITLES.CHANGE_MANAGEMENT.pendingJesaChange,
        defaultFeatureValue: ChannelFeaturesDefaultValues.CHANGE_MANAGEMENT.pendingJesaChange
      },
      {
        featureKey: COLLAB_FEATURES.CHANGE_MANAGEMENT.CHANGE_MGMT_CONTRAC_CHANGE,
        featureTitle: COLLAB_FEATURES_TITLES.CHANGE_MANAGEMENT.pendingContractorChange,
        defaultFeatureValue: ChannelFeaturesDefaultValues.CHANGE_MANAGEMENT.pendingJesaChange
      }
    ];
    return renderFeatureContainers(changeManagementFlowFeatures);
  }

  if (loading) {
    return <Loader show={true} />;
  }

  return (
    <View>
      {features
        ? ObjectUtils.convertObjectToKeyValuesArray(features).map((item) => {
          if (item.key !== KEYS_CHANNEL.COMMON.CHANNEL_STATUS &&
            item.key !== KEYS_CHANNEL.CHANGE_MANAGEMENT.CLOSED_SOR) {
            return (
              <FeatureContainer
                key={item.key}
                featureKey={
                  getSectionValueByKey(COLLAB_SUMMARY_FEATURE_KEY_WITH_FEATURES_CODE, "CHANGE_MANAGEMENT", item.key)
                }
                featureTitle={
                  getSectionValueByKey(COLLAB_FEATURES_TITLES, "CHANGE_MANAGEMENT", item.key)
                }
                featureValue={item.value}
                defaultFeatureValue={
                  getSectionValueByKey(ChannelFeaturesDefaultValues, "CHANGE_MANAGEMENT", item.key)
                }
              />
            );
          }
          return null;
        })
        : renderDefaultValues()}
    </View>
  );
};

export default ChangeManagementFeatures;
