<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina6_5" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="BootSplashLogo" translatesAutoresizingMaskIntoConstraints="NO" id="Idk-HV-Y6N">
                                <rect key="frame" x="67" y="313" width="279" height="279"/>
                            </imageView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="G2i-31-iOb"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="Idk-HV-Y6N" firstAttribute="leading" secondItem="G2i-31-iOb" secondAttribute="leading" constant="67" id="NCb-pq-7Wy"/>
                            <constraint firstItem="Idk-HV-Y6N" firstAttribute="top" secondItem="G2i-31-iOb" secondAttribute="top" constant="269" id="Ng7-wo-Pmo"/>
                            <constraint firstItem="G2i-31-iOb" firstAttribute="bottom" secondItem="Idk-HV-Y6N" secondAttribute="bottom" constant="270" id="hB3-rm-SO2"/>
                            <constraint firstItem="G2i-31-iOb" firstAttribute="trailing" secondItem="Idk-HV-Y6N" secondAttribute="trailing" constant="68" id="vkc-dm-FfT"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="48.799999999999997" y="373.76311844077964"/>
        </scene>
    </scenes>
    <resources>
        <image name="BootSplashLogo" width="136" height="204"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
