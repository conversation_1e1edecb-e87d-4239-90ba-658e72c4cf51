import {RootState} from '@src/store/store';
import {
  initialState,
  NotificationsSliceType,
} from '@src/store/notifications/slice';
import {notificationsSelector} from '@src/store/notifications/selectors';

describe('notificationsSelector', () => {
  it('should select the notifications state from the root state', () => {
    // Arrange
    const notificationsState: NotificationsSliceType = initialState;

    // @ts-ignore
    const mockRootState: RootState = {
      notifications: notificationsState,
    };

    // Act
    const selectedState = notificationsSelector(mockRootState);

    // Assert
    expect(selectedState).toEqual(notificationsState);
  });
});
